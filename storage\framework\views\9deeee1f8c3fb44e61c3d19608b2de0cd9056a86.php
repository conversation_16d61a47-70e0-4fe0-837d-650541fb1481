<!-- Data Validation Modal -->
<?php if($showDataValidationModal): ?>
<div class="modal fade show" id="dataValidationModal" tabindex="-1" role="dialog" aria-labelledby="dataValidationModalLabel" aria-hidden="false" wire:ignore.self style="display: block; background-color: rgba(0,0,0,0.5);">
<?php else: ?>
<div class="modal fade" id="dataValidationModal" tabindex="-1" role="dialog" aria-labelledby="dataValidationModalLabel" aria-hidden="true" wire:ignore.self style="display: none;">
<?php endif; ?>
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="dataValidationModalLabel">
                    <i class="fa fa-user-edit me-2"></i>Compléter les informations étudiant
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" wire:click="closeDataValidationModal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle me-2"></i>
                    <strong>Informations manquantes détectées</strong><br>
                    Certaines informations sont nécessaires pour générer le certificat de scolarité. Veuillez compléter les champs ci-dessous.
                </div>

                <?php if(!empty($missingFields)): ?>
                    <div class="alert alert-warning">
                        <h6><i class="fa fa-exclamation-triangle me-2"></i>Champs manquants :</h6>
                        <ul class="mb-0">
                            <?php $__currentLoopData = $missingFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($label); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form wire:submit.prevent="saveUserDataAndGenerateCertificate">
                    <div class="row">
                        <!-- Informations personnelles -->
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fa fa-user me-2"></i>Informations personnelles
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date_naissance" class="form-label">Date de naissance <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['editableUserData.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="date_naissance"
                                   wire:model="editableUserData.date_naissance"
                                   placeholder="jj/mm/aaaa">
                            <?php $__errorArgs = ['editableUserData.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="lieu_naissance" class="form-label">Lieu de naissance <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['editableUserData.lieu_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="lieu_naissance"
                                   wire:model="editableUserData.lieu_naissance"
                                   placeholder="Ville, Pays">
                            <?php $__errorArgs = ['editableUserData.lieu_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Informations familiales -->
                        <div class="col-12 mt-3">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fa fa-users me-2"></i>Informations familiales
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="nom_pere" class="form-label">Nom du père <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['editableUserData.nom_pere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="nom_pere"
                                   wire:model="editableUserData.nom_pere"
                                   placeholder="Nom complet du père">
                            <?php $__errorArgs = ['editableUserData.nom_pere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="nom_mere" class="form-label">Nom de la mère <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['editableUserData.nom_mere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="nom_mere"
                                   wire:model="editableUserData.nom_mere"
                                   placeholder="Nom complet de la mère">
                            <?php $__errorArgs = ['editableUserData.nom_mere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Informations CIN -->
                        <div class="col-12 mt-3">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fa fa-id-card me-2"></i>Informations CIN
                            </h6>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="cin" class="form-label">Numéro CIN <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['editableUserData.cin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="cin"
                                   wire:model="editableUserData.cin"
                                   placeholder="Ex: 123456789012">
                            <?php $__errorArgs = ['editableUserData.cin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="date_delivrance" class="form-label">Date de délivrance <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['editableUserData.date_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="date_delivrance"
                                   wire:model="editableUserData.date_delivrance"
                                   placeholder="jj/mm/aaaa">
                            <?php $__errorArgs = ['editableUserData.date_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="lieu_delivrance" class="form-label">Lieu de délivrance <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['editableUserData.lieu_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="lieu_delivrance"
                                   wire:model="editableUserData.lieu_delivrance"
                                   placeholder="Ville">
                            <?php $__errorArgs = ['editableUserData.lieu_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" wire:click="closeDataValidationModal">
                    <i class="fa fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-primary" wire:click="saveUserDataAndGenerateCertificate">
                    <i class="fa fa-save me-1"></i>Enregistrer et générer le certificat
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Handle modal close when clicking outside or on close button
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal') && e.target.id === 'dataValidationModal') {
            window.livewire.find('<?php echo e($_instance->id); ?>').call('closeDataValidationModal');
        }
    });
</script>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/certificat/modals/data-validation-modal.blade.php ENDPATH**/ ?>