var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Catalan = {
    weekdays: {
        shorthand: ["Dg", "Dl", "Dt", "Dc", "Dj", "Dv", "Ds"],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "Di<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>vendres",
            "<PERSON>ssabte",
        ],
    },
    months: {
        shorthand: [
            "Gen",
            "Febr",
            "Mar<PERSON>",
            "Abr",
            "Maig",
            "Juny",
            "Jul",
            "Ag",
            "Set",
            "Oct",
            "Nov",
            "Des",
        ],
        longhand: [
            "Gener",
            "Febrer",
            "Mar<PERSON>",
            "Abril",
            "Maig",
            "Jun<PERSON>",
            "<PERSON>l",
            "Agost",
            "Setembre",
            "Octubre",
            "Novembre",
            "Desembre",
        ],
    },
    ordinal: function (nth) {
        var s = nth % 100;
        if (s > 3 && s < 21)
            return "è";
        switch (s % 10) {
            case 1:
                return "r";
            case 2:
                return "n";
            case 3:
                return "r";
            case 4:
                return "t";
            default:
                return "è";
        }
    },
    firstDayOfWeek: 1,
    rangeSeparator: " a ",
    time_24hr: true,
};
fp.l10ns.cat = fp.l10ns.ca = Catalan;
export default fp.l10ns;
