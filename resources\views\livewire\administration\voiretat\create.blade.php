 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             {{ $current_type->nom }} 
             @if ($newEtus['etat'] == 1)
                 non payé
             @else
                 payé
             @endif
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Etudiants
             </h3>

         </div>
         <div class="block-content block-content-full">

             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                 <thead>
                     <tr>
                         <th class="text-center" style="width: 50px;">#</th>
                         <th>Nom</th>
                         <th>Prénom</th>
                         <th>Parcours</th>
                         {{-- <th class="d-none d-sm-table-cell">Parcours</th>
                         <th class="d-none d-sm-table-cell">Niveau</th>
                         <th class="d-none d-sm-table-cell" style="width: 10%;">Année</th>
                         <th class="text-center" style="width: 110px;">Actions</th> --}}
                     </tr>
                 </thead>
                 <tbody>

                     @foreach ($etus as $etu)
                         <tr>
                             <td class="text-center">{{ $loop->iteration }}</td>
                             <td class="fw-semibold">
                                 {{ $etu->nom }}
                             </td>
                             <td class="fw-semibold">
                                 {{ $etu->prenom }}
                             </td>
                             <td class="fw-semibold">
                                 @if ($etu->info[0]->parcours == null)
                                     Pas de parcours
                                 @else
                                     {{ $etu->info[0]->parcours->sigle }}
                                 @endif
                             </td>
                             {{--                              
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->niveau->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->annee->nom }}
                             </td> --}}

                         </tr>
                     @endforeach



                 </tbody>
             </table>


         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
