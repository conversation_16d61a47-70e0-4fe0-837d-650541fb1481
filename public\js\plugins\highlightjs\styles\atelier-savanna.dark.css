/* Base16 Atelier Savanna Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/savanna) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON><PERSON>/base16) */

/* Atelier-Savanna Comment */
.hljs-comment {
  color: #78877d;
}

/* Atelier-Savanna Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.hljs-name,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #b16139;
}

/* Atelier-Savanna Orange */
.hljs-number,
.hljs-preprocessor,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #9f713c;
}

/* Atelier-Savanna Yellow */
.ruby .hljs-class .hljs-title,
.css .hljs-rule .hljs-attribute {
  color: #a07e3b;
}

/* Atelier-Savanna Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #489963;
}

/* Atelier-Savanna Aqua */
.hljs-title,
.css .hljs-hexcolor {
  color: #1c9aa0;
}

/* Atelier-Savanna Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #478c90;
}

/* Atelier-Savanna Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #55859b;
}

.diff .hljs-deletion,
.diff .hljs-addition {
  color: #171c19;
  display: inline-block;
  width: 100%;
}

.diff .hljs-deletion {
  background-color: #b16139;
}

.diff .hljs-addition {
  background-color: #489963;
}

.diff .hljs-change {
  color: #478c90;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #171c19;
  color: #87928a;
  padding: 0.5em;
  -webkit-text-size-adjust: none;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
