<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MatiereTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table("matieres")->insert([
            // LCI
            ["nom"=>"Organisation d’entreprise", "code"=>"M11", "ue_id"=>"1", "user_id"=>"2"],
            ["nom"=>"Géographie des échanges", "code"=>"M12", "ue_id"=>"1", "user_id"=>"2"]
            // ["nom"=>"Technique du commerce", "code"=>"M21", "ue_id"=>"2", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Droit commercial", "code"=>"M22", "ue_id"=>"2", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Marketing de base", "code"=>"M31", "ue_id"=>"3", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Mathématique financière", "code"=>"M32", "ue_id"=>"3", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Logistique", "code"=>"M41", "ue_id"=>"4", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Economie des transports", "code"=>"M42", "ue_id"=>"4", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Transport urbain et interurbain des personnes", "code"=>"M51", "ue_id"=>"5", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Transport des marchandises", "code"=>"M52", "ue_id"=>"5", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Chaîne logistique international", "code"=>"M61", "ue_id"=>"6", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Droit de transport", "code"=>"M62", "ue_id"=>"6", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Anglais", "code"=>"M71", "ue_id"=>"7", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Informatique", "code"=>"M72", "ue_id"=>"7", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Communication", "code"=>"M73", "ue_id"=>"7", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Fiscalité", "code"=>"M81", "ue_id"=>"8", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Comptabilité", "code"=>"M82", "ue_id"=>"8", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Gestion de plateforme", "code"=>"M91", "ue_id"=>"9", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Statistique descriptive", "code"=>"M92", "ue_id"=>"9", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Visite d’entreprise", "code"=>"M101", "ue_id"=>"10", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"],
            // ["nom"=>"Projet tutoré", "code"=>"M102", "ue_id"=>"10", "parcour_id"=>"5", "niveau_id"=>"1", "user_id"=>"2"]
            // ["nom"=>"Nath", "syllabus"=>"qsdfq", "user_id"=>"2", "ue_id"=>"1", "parcour_id"=>"1", "niveau_id"=>"1"],
            // ["nom"=>"Phys", "syllabus"=>"qsdfq", "user_id"=>"2", "ue_id"=>"1", "parcour_id"=>"1", "niveau_id"=>"1"]
            
        ]);
    }
}
