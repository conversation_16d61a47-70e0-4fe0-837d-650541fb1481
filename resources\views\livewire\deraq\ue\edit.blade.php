<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListUe()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListUe()">Liste des UE</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Edition de UE
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="updateUe()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire d'édition des UE</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Code<span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editUe.code"
                            class="form-control @error('editUe.code') is-invalid @enderror" id="example-text-input"
                            name="firstname" placeholder="Text Input">

                        @error('editUe.code')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom de l'UE<span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editUe.nom"
                            class="form-control @error('editUe.nom') is-invalid @enderror" id="example-text-input"
                            name="lastname" placeholder="Text Input">

                        @error('editUe.nom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Crédit<span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editUe.credit"
                            class="form-control @error('editUe.credit') is-invalid @enderror" id="example-text-input"
                            name="lastname" placeholder="Text Input">

                        @error('editUe.credit')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <div class="col-6">
                        <label class="form-label" for="example-select">Parcours <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editUe.parcour_id') is-invalid @enderror"
                            wire:model="editUe.parcour_id" id="example-select" name="example-select">
                            <option selected value="0">Open this select menu</option>
                            @foreach ($parcours as $parcour)
                                <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                            @endforeach
                        </select>

                        @error('editUe.parcour_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-select">Niveaux <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editUe.niveau_id') is-invalid @enderror"
                            wire:model="editUe.niveau_id" id="example-select" name="example-select">
                            <option selected value="0">Open this select menu</option>
                            @foreach ($niveaux as $niveau)
                                <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                            @endforeach
                        </select>

                        @error('editUe.niveau_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-select">Semestre <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editUe.semestre_id') is-invalid @enderror"
                            wire:model="editUe.semestre_id" id="example-select" name="example-select">
                            <option selected value="0">Open this select menu</option>
                            @foreach ($semestres as $semestre)
                                <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                            @endforeach
                        </select>

                        @error('editUe.semestre_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-select">Année Universitaire <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editUe.annee_universitaire_id') is-invalid @enderror"
                            wire:model="editUe.annee_universitaire_id" id="example-select" name="example-select">
                            <option selected value="0">Open this select menu</option>
                            @foreach ($annees as $annee)
                                <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                            @endforeach
                        </select>

                        @error('editUe.annee_universitaire_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    

                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
