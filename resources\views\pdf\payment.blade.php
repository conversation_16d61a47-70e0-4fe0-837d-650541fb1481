<!DOCTYPE html>
<html>

<head>
    <title>Student Grading Result</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css" media="screen">
        html {
            font-family: sans-serif;
            line-height: 1.15;
            margin: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            text-align: left;
            background-color: #fff;
            font-size: 14px;
            margin: 36pt;
        }

        h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        strong {
            font-weight: bolder;
        }

        img {
            vertical-align: middle;
            border-style: none;
        }

        table {
            border-collapse: collapse;
        }

        th {
            text-align: inherit;
        }

        h4,
        .h4 {
            margin-bottom: 0.5rem;
            font-weight: 500;
            line-height: 1.2;
        }

        h4,
        .h4 {
            font-size: 1.5rem;
        }

        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            vertical-align: top;
        }

        .table.table-items td {
            border-top: 1px solid #dee2e6;
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
            background-color: #3B71CA;
        }



        .mt-5 {
            margin-top: 3rem !important;
        }

        .pr-0,
        .px-0 {
            padding-right: 0 !important;
        }

        .pl-0,
        .px-0 {
            padding-left: 0 !important;
        }

        .text-right {
            text-align: right !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-uppercase {
            text-transform: uppercase !important;
        }

        * {
            font-family: "DejaVu Sans";
        }

        body,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        table,
        th,
        tr,
        td,
        p,
        div {
            line-height: 1.1;
        }

        .party-header {
            font-size: 1.5rem;
            font-weight: 400;
        }

        .total-amount {
            font-size: 12px;
            font-weight: 700;
        }

        .border-0 {
            border: none !important;
        }

        .cool-gray {
            color: #3B71CA;
        }

        .red {
            color: #DC4C64;
        }

        .tg {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
        }


        .tg td {
            border-color: black;
            border-style: solid;
            border-width: 1px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            overflow: hidden;
            padding: 5px 5px;
            word-break: normal;
        }

        .tg th {
            border-color: black;
            border-style: solid;
            border-width: 1px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: normal;
            overflow: hidden;
            padding: 5px 5px;
            word-break: normal;
        }

        .tg .tg-cly1 {
            text-align: left;
            vertical-align: middle
        }

        .tg .tg-zr06 {
            background-color: #FFF;
            text-align: left;
            vertical-align: middle
        }

        tr:nth-child(even) {
            background-color: #f7f7f7;
        }
    </style>
</head>

<body>

    <table class="table">
        <tbody>
            <tr>
                <td class="border-0 pl-0" width="30%">
                    <img src="https://www.institut-imsaa.com/image/logo/logo2-removebg-preview.png" alt="logo IMSAA"
                        height="100">
                </td>
                <td class="border-0 pl-0 text-center">
                    <h4 class="text-uppercase cool-gray">
                        <strong> <span class="red">I</span>NSTITUT DE <span class="red">M</span>ANAGEMENT ET
                            DES
                            <span class="red">S</span>CIENCES <span class="red">A</span>PPLIQUEES <br>D'<span
                                class="red">A</span>NTSIRANANA
                            </span>
                    </h4>
                    <p>DIRECTION GENERALE </p>
                    <p>DIRECTION FINANCIERE ET ADMINISTRATIVE</p>


                </td>
            </tr>
        </tbody>
    </table>

    <p class="text-center" style="font-size: 15px;">
        <strong>Journal de caisse du {{ now()->format('d-m-Y') }} </strong>
    </p>

    <table class="tg">
        <thead>
            <tr>
                <th class="tg-cly1"><span style="color: black">Code</span></th>
                <th class="tg-cly1">
                    <span style="color: black">Libéllés</span>
                </th>
                <th class="tg-cly1"><span style="color: black">Mode</span></th>
                <th class="tg-cly1"><span style="color: black">Débit</span></th>
                <th class="tg-cly1"><span style="color: black">Crédit</span></th>
            </tr>
        </thead>
        <tbody>
            @php
                $debit = 0;
                $credit = 0;
            @endphp
            @forelse ($pays as $pay)
                <tr style="height:10px">
                    <td class="tg-zr06"><span style="color: black">{{ $pay->code }}</span></td>
                    <td class="tg-zr06">
                        <span style="color: black">{{ $pay->user->nom }} {{ $pay->user->prenom }} :
                            {{ $pay->payment->nom }}</span>
                    </td>


                    <td class="tg-zr06"><span style="color: black">
                            {{ $pay->moyen->nom }}
                        </span>
                    </td>
                    <td class="tg-zr06"><span style="color: black">
                            @if ($pay->encaissement->id == 1)
                                @php
                                    $debit += $pay->montant;
                                @endphp
                                {{ $pay->prixForHumans }}
                            @else
                                0 Ar
                            @endif
                        </span>
                    </td>
                    <td class="tg-zr06"><span style="color: black">
                            @if ($pay->encaissement->id == 2)
                                @php
                                    $credit += $pay->montant;
                                @endphp
                                {{ $pay->prixForHumans }}
                            @else
                                0 Ar
                            @endif
                        </span>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="5">
                        <div class="alert alert-warning d-flex align-items-center justify-content-between"
                            role="alert">
                            <div class="flex-grow-1 me-3">
                                <p class="mb-0">
                                    Impossible de générer. Pas encore de payment !!
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fa fa-fw fa-exclamation-circle"></i>
                            </div>

                        </div>
                    </td>
                </tr>
            @endforelse
            {{-- <tr>
          <td class="tg-zr06"><span style="color: black"></span></td>
          <td class="tg-zr06">
            <span style="color: black">ZAKIRALY Ansira</span>
          </td>
          <td class="tg-zr06"><span style="color: black">BIEN</span></td>
        </tr> --}}
        </tbody>
    </table>

    {{-- <table class="table table-items">
        <thead>
            <tr>
                <th scope="col" class="text-center border-0 pl-0">Rang</th>

                <th scope="col" class="border-0">Nom et Prénom</th>

                <th scope="col" class=" border-0 pr-0">Mention</th>
            </tr>
        </thead>
        <tbody> --}}
    {{-- Items --}}
    {{-- @foreach ($results as $result)
                <tr>
                    <td class="text-center pl-0">
                        {{ $loop->iteration }}

                    </td>

                    <td class="">{{ $result['nom'] }} {{ $result['prenom'] }}</td>

                    <td class=" pr-0">
                        @if ($result['moyenne'] < 12)
                            Passable
                        @elseif ($result['moyenne'] >= 12 && $result['moyenne'] < 14)
                            Assez-bien
                        @elseif ($result['moyenne'] >= 14 && $result['moyenne'] < 16)
                            Bien
                        @else
                            Très bien
                        @endif
                    </td>
                </tr>
            @endforeach --}}

    {{-- @forelse ($notes as $result)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>
                        {{ $result->nom }} {{ $result->prenom }}
                    </td>

                
                    <td>
                        @if ($result->moyenne < 12)
                            Passable
                        @elseif ($result->moyenne >= 12 && $result->moyenne < 14)
                            Assez-bien
                        @elseif ($result->moyenne >= 14 && $result->moyenne < 16)
                            Bien
                        @else
                            Très bien
                        @endif

                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="4">
                        <div class="alert alert-warning d-flex align-items-center justify-content-between"
                            role="alert">
                            <div class="flex-grow-1 me-3">
                                <p class="mb-0">
                                    Impossible de générer. Pas encore de note ajouté !!
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fa fa-fw fa-exclamation-circle"></i>
                            </div>

                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table> --}}

    {{-- <table class="table">
        <thead>
            <tr>
                <th>Rang</th>
                <th>Nom et Prénom</th>
                <th>Mention</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($results as $result)
                @if ($result['moyenne'] >= 10)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>
                            {{ $result['nom'] }} {{ $result['prenom'] }}
                        </td>

                        <td>
                            @if ($result['moyenne'] < 12)
                                Passable
                            @elseif ($result['moyenne'] >= 12 && $result['moyenne'] < 14)
                                Assez-bien
                            @elseif ($result['moyenne'] >= 14 && $result['moyenne'] < 16)
                                Bien
                            @else
                                Très bien
                            @endif

                        </td>
                    </tr>
                @endif

            @empty
                <tr>
                    <td colspan="3">
                        <div class="alert alert-warning d-flex align-items-center justify-content-between"
                            role="alert">
                            <div class="flex-grow-1 me-3">
                                <p class="mb-0">
                                    Impossible de générer. Pas encore de note ajouté !!
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fa fa-fw fa-exclamation-circle"></i>
                            </div>

                        </div>
                    </td>
                </tr>
            @endforelse

        </tbody>
    </table> --}}

    @php
        $td = number_format($debit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
        $tc = number_format($credit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
        $t = number_format($debit - $credit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
    @endphp
    <div class="text-center mt-5">
        <p>
            Total débit: {{ $td }}
        </p>
        <p>
            Total crédit: {{ $tc }}
        </p>
        <p>
            Total payment du jour : {{ $t }}
        </p>
    </div>

</body>

</html>

{{-- <!DOCTYPE html>
<html>

<head>
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            font-size: 16px;
            font-weight: 400;
            color: #444;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            background-color: #fff;
        }

        /* Table header */
        th {
            background-color: #0066CC;
            text-align: left;
            padding: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 2px solid #ddd;
            color: #fff;
        }

        /* Table rows */
        tr {
            background-color: #fff;
            border-bottom: 1px solid #ddd;
        }

        /* Table cell content */
        td {
            padding: 10px;
        }


        table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>

<body>
    <table>
        <thead>
            <tr>
                <th>Rang</th>
                <th>Nom et Prénom</th>
                <th>Mention</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($results as $result)
                @if ($result['moyenne'] >= 10)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>
                            {{ $result['nom'] }} {{ $result['prenom'] }}
                        </td>

                        <td>
                            @if ($result['moyenne'] < 12)
                                Passable
                            @elseif ($result['moyenne'] >= 12 && $result['moyenne'] < 14)
                                Assez-bien
                            @elseif ($result['moyenne'] >= 14 && $result['moyenne'] < 16)
                                Bien
                            @else
                                Très bien
                            @endif

                        </td>
                    </tr>
                @endif

            @empty
                <tr>
                    <td colspan="3">
                        <div class="alert alert-warning d-flex align-items-center justify-content-between" role="alert">
                            <div class="flex-grow-1 me-3">
                                <p class="mb-0">
                                    Impossible de générer. Pas encore de note ajouté !!
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fa fa-fw fa-exclamation-circle"></i>
                            </div>

                        </div>
                    </td>
                </tr>
            @endforelse

        </tbody>
    </table>
</body>

</html> --}}
