/*

Zenburn style from voldmar.ru (c) <PERSON> <<EMAIL>>
based on dark.css by <PERSON>

*/

.hljs {
  display: block; padding: 0.5em;
  background: #3F3F3F;
  color: #DCDCDC;
}

.hljs-keyword,
.hljs-tag,
.css .hljs-class,
.css .hljs-id,
.lisp .hljs-title,
.nginx .hljs-title,
.hljs-request,
.hljs-status,
.clojure .hljs-attribute {
  color: #E3CEAB;
}

.django .hljs-template_tag,
.django .hljs-variable,
.django .hljs-filter .hljs-argument {
  color: #DCDCDC;
}

.hljs-number,
.hljs-date {
  color: #8CD0D3;
}

.dos .hljs-envvar,
.dos .hljs-stream,
.hljs-variable,
.apache .hljs-sqbracket {
  color: #EFDCBC;
}

.dos .hljs-flow,
.diff .hljs-change,
.python .exception,
.python .hljs-built_in,
.hljs-literal,
.tex .hljs-special {
  color: #EFEFAF;
}

.diff .hljs-chunk,
.hljs-subst {
  color: #8F8F8F;
}

.dos .hljs-keyword,
.python .hljs-decorator,
.hljs-title,
.haskell .hljs-type,
.diff .hljs-header,
.ruby .hljs-class .hljs-parent,
.apache .hljs-tag,
.nginx .hljs-built_in,
.tex .hljs-command,
.hljs-prompt {
    color: #efef8f;
}

.dos .hljs-winutils,
.ruby .hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.ruby .hljs-string {
  color: #DCA3A3;
}

.diff .hljs-deletion,
.hljs-string,
.hljs-tag .hljs-value,
.hljs-preprocessor,
.hljs-pragma,
.hljs-built_in,
.sql .hljs-aggregate,
.hljs-javadoc,
.smalltalk .hljs-class,
.smalltalk .hljs-localvars,
.smalltalk .hljs-array,
.css .hljs-rules .hljs-value,
.hljs-attr_selector,
.hljs-pseudo,
.apache .hljs-cbracket,
.tex .hljs-formula,
.coffeescript .hljs-attribute {
  color: #CC9393;
}

.hljs-shebang,
.diff .hljs-addition,
.hljs-comment,
.java .hljs-annotation,
.hljs-template_comment,
.hljs-pi,
.hljs-doctype {
  color: #7F9F7F;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
