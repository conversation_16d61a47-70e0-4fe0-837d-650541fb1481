<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">
        <h1 class="h3 fw-bold mb-4">
            Status de paiement
        </h1>
        <div class="row justify-content-center p-md-2">
            <div class="col-md-3">
                <div class="mb-4">
                    <label class="form-label" for="parcour-select">Parcours <span class="text-danger">*</span></label>
                    <select class="form-select @error('listEtus.parcour_id') is-invalid @enderror"
                        wire:model="listEtus.parcour_id" id="parcour-select" name="parcour-select">
                        <option value="">Sélectionner un parcours</option>
                        <option value="100">Tous les parcours</option>
                        @foreach ($parcours as $parcour)
                            <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                        @endforeach
                    </select>
                    @error('listEtus.parcour_id')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-4">
                    <label class="form-label" for="niveau-select">Niveau <span class="text-danger">*</span></label>
                    <select class="form-select @error('listEtus.niveau_id') is-invalid @enderror"
                        wire:model="listEtus.niveau_id" id="niveau-select" name="niveau-select">
                        <option value="0">Sélectionner un niveau</option>
                        @foreach ($niveaux as $niveau)
                            <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                        @endforeach
                    </select>
                    @error('listEtus.niveau_id')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-4">
                    <label class="form-label" for="annee-select">Année Universitaire <span
                            class="text-danger">*</span></label>
                    <select class="form-select @error('listEtus.annee_universitaire_id') is-invalid @enderror"
                        wire:model="listEtus.annee_universitaire_id" id="annee-select" name="annee-select">
                        <option value="0">Sélectionner une année</option>
                        @foreach ($annees as $annee)
                            <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                        @endforeach
                    </select>
                    @error('listEtus.annee_universitaire_id')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="col-md-3 d-flex align-items-center">
                <button type="button" wire:click="showStatusEtu()" class="btn btn-primary"
                    wire:loading.attr="disabled">
                    <div wire:loading wire:target="showStatusEtu"
                        class="spinner-border spinner-border-sm text-light me-2" role="status">
                    </div>Appliquer
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <!-- Dynamic Table Full -->
    <div class="block block-rounded">
        <div class="block-header block-header-default d-flex justify-content-between">
            <h3 class="block-title">
                Liste des Etudiants
            </h3>
            <!-- Export buttons could go here -->
            <div>
                <button type="button" class="btn btn-sm btn-alt-secondary" onclick="window.print()">
                    <i class="fa fa-print me-1"></i> Imprimer
                </button>
                <button type="button" class="btn btn-sm btn-alt-success" id="exportExcel">
                    <i class="fa fa-file-excel me-1"></i> Excel
                </button>
            </div>
        </div>
        <div class="block-content block-content-full">
            <!-- Responsive table wrapper with fixed header and first column -->
            <div class="table-container" style="position: relative; max-height: 70vh; overflow: auto;">
                <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                <table class="table table-bordered table-striped table-vcenter fs-sm" id="payment-status-table">
                    <thead class="sticky-header">
                        <tr>
                            <th class="sticky-col-1" style="width: 50px; min-width: 50px;">#</th>
                            <th class="sticky-col-2" style="min-width: 200px;">Nom et Prénom</th>
                            @foreach ($etus[0]['paiment'] ?? [] as $type)
                                <th style="min-width: 150px;">{{ $type['type_nom'] }}</th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($etus as $etudiant)
                            <tr>
                                <td class="sticky-col-1">{{ $loop->iteration }}</td>
                                <td class="sticky-col-2 font-weight-bold">{{ $etudiant['nom'] }}</td>
                                @foreach ($etudiant['paiment'] as $payment)
                                    <td>
                                        @switch($payment['status'])
                                            @case(0)
                                                <span class="badge bg-danger">Non payé</span>
                                                <div class="fs-xs text-muted mt-1">0 /
                                                    {{ number_format($payment['MAX'], 0, ',', ' ') }} MGA</div>
                                            @break

                                            @case(1)
                                                <span class="badge bg-warning">Partiellement payé</span>
                                                <div class="progress mt-1" style="height: 4px;">
                                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 50%">
                                                    </div>
                                                </div>
                                            @break

                                            @case(2)
                                                <span class="badge bg-success">Payé</span>
                                                <div class="fs-xs text-muted mt-1">
                                                    {{ number_format($payment['MAX'], 0, ',', ' ') }} MGA</div>
                                            @break
                                        @endswitch
                                    </td>
                                @endforeach
                            </tr>
                            @empty
                                <tr>
                                    <td colspan="100%">
                                        <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                            role="alert">
                                            <div class="flex-grow-1 me-3">
                                                <p class="mb-0">
                                                    Choisissez les informations et cliquez sur générer !
                                                </p>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <i class="fa fa-fw fa-exclamation-circle"></i>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination if needed -->
                @if (count($etus) > 0)
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="fs-sm text-muted">Affichage de {{ count($etus) }} étudiants</span>
                        </div>
                    </div>
                @endif
            </div>
        </div>
        <!-- END Dynamic Table Full -->
    </div>
    <!-- END Page Content -->

    <!-- Custom CSS for fixed headers and columns -->
    <style>
        /* Amélioration du comportement des colonnes sticky */
        .table-container {
            position: relative;
            max-height: 70vh;
            overflow: auto;
            margin-bottom: 20px;
        }

        .sticky-header th {
            position: sticky;
            top: 0;
            background-color: #f8f9fa;
            z-index: 10;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        }

        /* Correction pour les colonnes sticky - PROBLÈME PRINCIPAL */
        .sticky-col-1 {
            position: sticky;
            left: 0;
            background-color: #fff;
            z-index: 9;
            box-shadow: 2px 0 5px -2px rgba(0, 0, 0, 0.15);
        }

        .sticky-col-2 {
            position: sticky;
            left: 50px;
            /* Doit être égale à la largeur de la première colonne */
            background-color: #fff;
            z-index: 9;
            box-shadow: 2px 0 5px -2px rgba(0, 0, 0, 0.15);
        }

        /* Assurer que les en-têtes de colonnes fixes soient au-dessus des autres */
        .sticky-header .sticky-col-1 {
            z-index: 11;
            background-color: #f8f9fa;
        }

        .sticky-header .sticky-col-2 {
            z-index: 11;
            background-color: #f8f9fa;
        }

        /* S'assurer que les cellules fixes ont une largeur définie */
        #payment-status-table th.sticky-col-1,
        #payment-status-table td.sticky-col-1 {
            min-width: 50px;
            width: 50px;
        }

#payment-status-table th.sticky-col-2,
#payment-status-table td.sticky-col-2 {
    min-width: 200px; /* Keep a minimum width */
    /* Removed width, max-width, white-space, overflow, text-overflow to allow content to expand */
}

        /* Améliorer la visibilité des cellules fixes pendant le défilement */
        #payment-status-table tr:nth-child(even) td.sticky-col-1,
        #payment-status-table tr:nth-child(even) td.sticky-col-2 {
            background-color: #f9f9f9;
        }

        #payment-status-table tr:hover td.sticky-col-1,
        #payment-status-table tr:hover td.sticky-col-2 {
            background-color: rgba(0, 0, 0, 0.03);
        }
    </style>

    <!-- Custom JS for export functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fix DataTables initialization to work with sticky columns
            if (typeof $.fn.dataTable !== 'undefined') {
                const dataTableOptions = {
                    responsive: false, // Disable responsive to use our custom solution
                    scrollX: false, // Disable built-in scroll
                    scrollY: false, // Disable built-in scroll
                    paging: false, // We're using our custom container for scrolling
                    searching: true, // Enable search
                    info: false, // Disable info display
                    language: {
                        search: "Rechercher:",
                        zeroRecords: "Aucun résultat trouvé",
                        infoEmpty: "Aucun enregistrement disponible",
                        searchPlaceholder: "Filtrer les étudiants..."
                    }
                };

                // Only initialize if there are students
                if (document.querySelector('#payment-status-table tbody tr td:not([colspan])')) {
                    const table = $('#payment-status-table').DataTable(dataTableOptions);

                    // Add search box outside of table for better UX
                    $('.block-header').append(
                        '<div class="mt-2 mt-md-0">' +
                        '<div class="input-group">' +
                        '<span class="input-group-text"><i class="fa fa-search"></i></span>' +
                        '<input type="text" class="form-control" id="table-search" placeholder="Rechercher un étudiant...">' +
                        '</div>' +
                        '</div>'
                    );

                    $('#table-search').on('keyup', function() {
                        table.search(this.value).draw();
                    });

                    // Handle Excel export
                    $('#exportExcel').on('click', function() {
                        alert('Fonctionnalité d\'export Excel à implémenter');
                        // Implementation would require server-side code or a library like SheetJS
                    });
                }
            }
        });
    </script>
