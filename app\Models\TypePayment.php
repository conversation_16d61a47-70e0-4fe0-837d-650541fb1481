<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TypePayment extends Model
{
    use HasFactory;

    public $timestamps = false; 


    protected $fillable = [
        'nom',
        
    ];

    public function historyPay(){
        return $this->hasMany(HistoriquePayment::class);
    }

    public function niveau(){
        return $this->belongsToMany(Niveau::class)->withPivot(['prix', 'annee_universitaire_id']);
    }
}
