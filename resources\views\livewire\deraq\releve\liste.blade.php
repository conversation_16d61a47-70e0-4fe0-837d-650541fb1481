<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">

        <h1 class="h3 fw-bold mb-2">
            {{ $current_user->nom }} {{ $current_user->prenom }}
        </h1>

    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">

    <!-- Dynamic Table Full -->
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                Relevé de notes
            </h3>
            @if (!$semestres->isEmpty())
                <div class="block-options">
                    <a class="btn btn-sm btn-primary me-1" wire:click="pdfGenerate()">
                        <i class="fa fa-plus me-1"></i> Imprimer en PDF
                    </a>
                </div>
            @endif
        </div>
        <div class="block-content block-content-full">
            <p>
                Mention : {{ $current_parcours->mention->nom }}, Parcours : {{ $current_parcours->sigle }}
                ({{ $current_parcours->nom }})
            </p>
            <p>
                Niveau : {{ $current_niveau->nom }}
            </p>
            <p>
                Année Universitaire : {{ $current_annee->nom }}
            </p>

            <div class="table-responsive">
                <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">Code UE</th>
                            <th scope="col" class="text-center">Unité d’Enseignement</th>
                            <th scope="col" class="text-center">Code EC</th>
                            <th scope="col" class="text-center">Elément Constitutif</th>
                            <th scope="col" class="text-center">Note</th>
                            <th scope="col" class="text-center">Crédit</th>
                            <th scope="col" class="text-center">Moy UE</th>
                            <th scope="col" class="text-center">Décision</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{-- 
                @foreach ($semestres as $ec)
                        <tr>
                            <td class="text-center">{{ $ec->nom }}</td>
                            <td class="text-center fw-semibold">
                                @foreach ($ec->ue as $notes)
                                    {{ $notes->matiere->count() }}
                                    @foreach ($notes->matiere as $m)
                                        {{ $m->ue->count() }}
                                    @endforeach
                                @endforeach
                                {{ $ec->ue->nom }}
                            </td>
                            <td class="text-center d-none d-sm-table-cell">
                                @foreach ($ec->notesP1 as $notes)
                                    {{ $notes->valeur }}
                                @endforeach
                                {{ $ec->matiere->moyenne }}
                            </td>
                            <td class="text-center">
                                @foreach ($ec->ue->note as $notes)
                                    {{ $notes->valeur }}
                                @endforeach
                                {{ $ec->ue->n }}
                            </td>

                        </tr>
                    @endforeach --}}
                        @php
                            $count = 0;
                            $moygen = 0;
                        @endphp

                        @forelse ($semestres as $semestre)
                            <tr>
                                <td colspan="8" class="text-center">
                                    {{ $semestre->nom }}
                                </td>
                            </tr>
                            @foreach ($semestre->ue as $ec)
                                @php
                                    
                                    $total = 0;
                                    $moy = 0;
                                @endphp
                                @if ($ec->matiere->count() != 0)
                                    @for ($i = 0; $i < $ec->matiere->count(); $i++)
                                        @php
                                            $total += $ec->matiere[$i]->moyenne;
                                        @endphp
                                    @endfor
                                    @php
                                        $moy = $total / $ec->matiere->count();
                                        $moygen += $moy;
                                        $count = $count + 1;
                                        $m = number_format($moy, 2, ',', '.');
                                        $u = $ec->matiere[0]->moyenne;
                                        $s = number_format($u, 2, ',', '.');
                                    @endphp
                                    <tr>
                                        <td rowspan="{{ $ec->matiere->count() }}" class="text-center">
                                            {{ $ec->code }}
                                        </td>
                                        <td rowspan="{{ $ec->matiere->count() }}" class="text-center">
                                            {{ $ec->nom }}
                                        </td>
                                        <td class="text-center"> {{ $ec->matiere[0]->code }} </td>
                                        <td class="text-center"> {{ $ec->matiere[0]->nom }} </td>
                                        <td class="text-center"> {{ $s }}</td>
                                        <td rowspan="{{ $ec->matiere->count() }}" class="text-center">
                                            {{ $ec->credit }}
                                        </td>
                                        <td rowspan="{{ $ec->matiere->count() }}" class="text-center">

                                            {{ $m }}
                                        </td>
                                        <td rowspan="{{ $ec->matiere->count() }}" class="text-center">
                                            @if ($moy >= 10)
                                                Validé
                                            @else
                                                Non validé
                                            @endif
                                        </td>
                                    </tr>
                                    @for ($i = 1; $i < $ec->matiere->count(); $i++)
                                        @php
                                            $t = $ec->matiere[$i]->moyenne;
                                            $x = number_format($t, 2, ',', '.');
                                        @endphp
                                        <tr>
                                            <td class="text-center"> {{ $ec->matiere[$i]->code }} </td>
                                            <td class="text-center"> {{ $ec->matiere[$i]->nom }} </td>
                                            <td class="text-center"> {{ $x }}</td>
                                        </tr>
                                    @endfor
                                @endif
                            @endforeach
                        @empty
                            <tr>
                                <td colspan="8">
                                    <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                        role="alert">
                                        <div class="flex-grow-1 me-3">
                                            <p class="mb-0">
                                                Impossible de générer. Pas encore de note ajouté !!
                                            </p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-fw fa-exclamation-circle"></i>
                                        </div>

                                    </div>
                                </td>
                            </tr>
                        @endforelse


                        {{-- 
                    @php
                        $rowid = 0;
                        $rowspan = 0;
                    @endphp
                    @foreach ($ecs as $key => $ec)
                        @php
                            $rowid += 1;
                        @endphp
                        <tr>
                            @if ($key == 0 || $rowspan == $rowid)
                                @php
                                    $rowid = 0;
                                    $rowspan = $ec->ue->matiere->count();
                                @endphp
                                <td rowspan="{{ $rowspan }}">{{ $ec->ue->nom }}</td>
                            @endif
                            <td>{{ $ec->nom }}</td>
                            <td>{{ $ec->moyenne }}</td>
                            <td rowspan="{{ $rowspan }}">3</td>
                        </tr>
                    @endforeach --}}

                        {{-- @foreach ($ecs as $ec)
                        @php($first = true) @endphp
                        @foreach ($ec->matiere as $matier)
                            <tr>
                                @if ($first == true)
                                    <td rowspan="{{ $ec->matiere->count() }}"> {{ $ec->matiere->count() }} </td>
                                    @php($first = false) @endphp
                                @endif
                                <td> {{ $matier->nom }} </td>
                                <td> {{ $matier->moyenne }}</td>
                            </tr>
                        @endforeach
                    @endforeach --}}

                        {{-- @foreach ($ecs as $ec)
                        <tr>
                            <td class="text-center">{{ $ec->nom }}</td>
                            <td class="text-center fw-semibold">
                                @foreach ($ec->matiere as $notes)
                                    {{ $notes->nom }}
                                @endforeach
                                {{ $ec->matiere->nom }}
                            </td>
                            <td class="text-center d-none d-sm-table-cell">
                                @foreach ($ec->notesP1 as $notes)
                                    {{ $notes->valeur }}
                                @endforeach
                                {{ $ec->matiere->moyenne }}
                            </td>
                            <td class="text-center">
                                @foreach ($ec->ue->note as $notes)
                                    {{ $notes->valeur }}
                                @endforeach
                                {{ $ec->ue->n }}
                            </td>

                        </tr>
                    @endforeach --}}



                    </tbody>
                </table>
            </div>
            @if ($count != 0)
                @php
                    $moys = number_format($moygen / $count, 2, ',', '.');
                @endphp
                Moyenne générale : {{ $moys }}
            @endif


        </div>
    </div>
    <!-- END Dynamic Table Full -->


</div>
<!-- END Page Content -->
