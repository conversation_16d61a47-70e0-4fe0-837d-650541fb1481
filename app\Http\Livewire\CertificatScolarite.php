<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\User;
use Illuminate\Support\Facades\View;
use Livewire\Component;
use PDF;

class CertificatScolarite extends Component
{
    public $current_user;
    public $current_parcours;
    public $current_niveau;
    public $current_annee;
    public $inscription;

    // Modal properties
    public $showDataValidationModal = false;
    public $editableUserData = [];
    public $missingFields = [];
    public $validationErrors = [];

    public function mount($userId, $parcourId, $niveauId, $anneeId)
    {
        $this->current_user = User::find($userId);
        $this->current_parcours = Parcour::find($parcourId);
        $this->current_niveau = Niveau::find($niveauId);
        $this->current_annee = AnneeUniversitaire::find($anneeId);

        // Récupérer l'inscription de l'étudiant pour cette année
        $this->inscription = InscriptionStudent::where('user_id', $userId)
            ->where('parcour_id', $parcourId)
            ->where('niveau_id', $niveauId)
            ->where('annee_universitaire_id', $anneeId)
            ->first();
    }

    public function render()
    {
        return view('livewire.deraq.certificat.index')
            ->extends('layouts.backend')
            ->section('content');
    }

    /**
     * Check if student data is complete for certificate generation
     */
    public function checkDataCompleteness()
    {
        $requiredFields = [
            'date_naissance' => 'Date de naissance',
            'lieu_naissance' => 'Lieu de naissance',
            'nom_pere' => 'Nom du père',
            'nom_mere' => 'Nom de la mère',
            'cin' => 'Numéro CIN',
            'date_delivrance' => 'Date de délivrance CIN',
            'lieu_delivrance' => 'Lieu de délivrance CIN'
        ];

        $this->missingFields = [];

        foreach ($requiredFields as $field => $label) {
            if (empty($this->current_user->$field)) {
                $this->missingFields[$field] = $label;
            }
        }

        return empty($this->missingFields);
    }

    /**
     * Open data validation modal if data is incomplete
     */
    public function openDataValidationModal()
    {
        if (!$this->checkDataCompleteness()) {
            $this->editableUserData = $this->current_user->toArray();
            $this->showDataValidationModal = true;
            $this->validationErrors = [];
            $this->dispatchBrowserEvent('showDataValidationModal');
        } else {
            // Data is complete, generate PDF directly
            return $this->pdfGenerate();
        }
    }

    /**
     * Close the data validation modal
     */
    public function closeDataValidationModal()
    {
        $this->showDataValidationModal = false;
        $this->editableUserData = [];
        $this->validationErrors = [];
        $this->dispatchBrowserEvent('hideDataValidationModal');
    }

    /**
     * Validation rules for user data
     */
    protected function rules()
    {
        return [
            'editableUserData.date_naissance' => 'required|string',
            'editableUserData.lieu_naissance' => 'required|string|max:255',
            'editableUserData.nom_pere' => 'required|string|max:255',
            'editableUserData.nom_mere' => 'required|string|max:255',
            'editableUserData.cin' => 'required|string|max:20',
            'editableUserData.date_delivrance' => 'required|string',
            'editableUserData.lieu_delivrance' => 'required|string|max:255',
        ];
    }

    /**
     * Custom validation messages
     */
    protected function messages()
    {
        return [
            'editableUserData.date_naissance.required' => 'La date de naissance est obligatoire.',
            'editableUserData.lieu_naissance.required' => 'Le lieu de naissance est obligatoire.',
            'editableUserData.nom_pere.required' => 'Le nom du père est obligatoire.',
            'editableUserData.nom_mere.required' => 'Le nom de la mère est obligatoire.',
            'editableUserData.cin.required' => 'Le numéro CIN est obligatoire.',
            'editableUserData.date_delivrance.required' => 'La date de délivrance est obligatoire.',
            'editableUserData.lieu_delivrance.required' => 'Le lieu de délivrance est obligatoire.',
        ];
    }

    /**
     * Save updated user data and generate certificate
     */
    public function saveUserDataAndGenerateCertificate()
    {
        $this->validate();

        try {
            // Update user data
            $this->current_user->update([
                'date_naissance' => $this->editableUserData['date_naissance'],
                'lieu_naissance' => $this->editableUserData['lieu_naissance'],
                'nom_pere' => $this->editableUserData['nom_pere'],
                'nom_mere' => $this->editableUserData['nom_mere'],
                'cin' => $this->editableUserData['cin'],
                'date_delivrance' => $this->editableUserData['date_delivrance'],
                'lieu_delivrance' => $this->editableUserData['lieu_delivrance'],
                'is_filled' => true, // Mark as data complete
            ]);

            // Refresh the current user data
            $this->current_user->refresh();

            // Close modal
            $this->closeDataValidationModal();

            // Show success message
            $this->dispatchBrowserEvent('showSuccessMessage', [
                'message' => 'Données étudiant mises à jour avec succès!'
            ]);

            // Generate certificate
            return $this->pdfGenerate();

        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('showErrorMessage', [
                'message' => 'Erreur lors de la mise à jour des données: ' . $e->getMessage()
            ]);
        }
    }

    public function pdfGenerate()
    {
        $view = View::make('pdf.certificat', [
            'current_user' => $this->current_user,
            'current_parcours' => $this->current_parcours,
            'current_niveau' => $this->current_niveau,
            'current_annee' => $this->current_annee,
            'inscription' => $this->inscription
        ]);

        $html = $view->render();

        $pdf = PDF::loadHTML($html);
        $pdf->setPaper('A4', 'portrait');

        $filename = 'certificat_scolarite_' . 
                   $this->current_user->nom . '.pdf';

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->output();
        }, $filename);
    }
}
