<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\User;
use Illuminate\Support\Facades\View;
use Livewire\Component;
use PDF;

class CertificatScolarite extends Component
{
    public $current_user;
    public $current_parcours;
    public $current_niveau;
    public $current_annee;
    public $inscription;

    public function mount($userId, $parcourId, $niveauId, $anneeId)
    {
        $this->current_user = User::find($userId);
        $this->current_parcours = Parcour::find($parcourId);
        $this->current_niveau = Niveau::find($niveauId);
        $this->current_annee = AnneeUniversitaire::find($anneeId);

        // Récupérer l'inscription de l'étudiant pour cette année
        $this->inscription = InscriptionStudent::where('user_id', $userId)
            ->where('parcour_id', $parcourId)
            ->where('niveau_id', $niveauId)
            ->where('annee_universitaire_id', $anneeId)
            ->first();
    }

    public function render()
    {
        return view('livewire.deraq.certificat.index')
            ->extends('layouts.backend')
            ->section('content');
    }

    public function pdfGenerate()
    {
        $view = View::make('pdf.certificat', [
            'current_user' => $this->current_user,
            'current_parcours' => $this->current_parcours,
            'current_niveau' => $this->current_niveau,
            'current_annee' => $this->current_annee,
            'inscription' => $this->inscription
        ]);

        $html = $view->render();

        $pdf = PDF::loadHTML($html);
        $pdf->setPaper('A4', 'portrait');

        $filename = 'certificat_scolarite_' . 
                   $this->current_user->nom . '.pdf';

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->output();
        }, $filename);
    }
}
