/* Base16 Atelier Seaside Light - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/seaside) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON><PERSON>/base16) */

/* Atelier-Seaside Comment */
.hljs-comment,
.hljs-quote {
  color: #687d68;
}

/* Atelier-Seaside Red */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-name,
.hljs-regexp,
.hljs-link,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #e6193c;
}

/* Atelier-Seaside Orange */
.hljs-number,
.hljs-meta,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
  color: #87711d;
}

/* Atelier-Seaside Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet {
  color: #29a329;
}

/* Atelier-Seaside Blue */
.hljs-title,
.hljs-section {
  color: #3d62f5;
}

/* Atelier-Seaside Purple */
.hljs-keyword,
.hljs-selector-tag {
  color: #ad2bee;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #f4fbf4;
  color: #5e6e5e;
  padding: 0.5em;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
