<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NiveauTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table("niveaux")->insert([
            ["sigle"=>"L1", "nom"=>"1ère année"],
            ["sigle"=>"L2", "nom"=>"2ème année"],
            ["sigle"=>"L3", "nom"=>"3ème année"],
            ["sigle"=>"M1", "nom"=>"4ème année"],
            ["sigle"=>"M2", "nom"=>"5ème année"],
        ]);
    }
}
