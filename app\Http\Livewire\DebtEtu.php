<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Niveau;
use App\Models\TypePayment;
use App\Models\User;
use Livewire\Component;

class DebtEtu extends Component
{
    public $currentPage = PAGELIST;

    public $debtPay = [];
    public $newEtus = [];

    public Niveau $current_niveau;
    public AnneeUniversitaire $current_annee;

    public function render()
    {
        return view('livewire.administration.debtpay.index', [
            "niveaux" => Niveau::all(),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editTypePayment.nom' => 'required',
            ];
        }

        return [
            'newEtus.niveau_id' => 'required',
            'newEtus.annee_universitaire_id' => 'required',

        ];
    }

    public function goToListResult()
    {
        $this->currentPage = PAGELIST;
        $this->debtPay = [];
    }

    public function goToEtat()
    {
        $validationAttributes = $this->validate();

        $this->generate($validationAttributes["newEtus"]["niveau_id"], $validationAttributes["newEtus"]["annee_universitaire_id"]);

        $this->currentPage = PAGECREATEFORM;
    }

    public function generate($niveau_id, $annee_universitaire_id)
    {
        $this->current_niveau = Niveau::findOrFail($niveau_id);
        $this->current_annee = AnneeUniversitaire::findOrFail($annee_universitaire_id);

        $types = TypePayment::whereHas('niveau', fn($q) => $q->whereNiveauId($niveau_id))->get();

        $users = User::with([
            'info.parcours',
            'historique' => fn($q) =>
            $q->where('annee_universitaire_id', $annee_universitaire_id)
        ])->whereHas(
            'info',
            fn($q) =>
            $q->whereAnneeUniversitaireId($annee_universitaire_id)
                ->whereNiveauId($niveau_id)
        )->get();

        foreach ($types as $type) {
            $type_prix = $type->niveau()->whereNiveauId($this->current_niveau->id)->wherePivot('annee_universitaire_id', $this->current_annee->id)->first()->pivot->prix ?? 0;

            foreach ($users as $user) {
                $prix = $user->historique->where('type_payment_id', $type->id)->sum('montant');

                if ($prix > 0 && $prix < $type_prix) {
                    $montant = number_format($prix, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
                    $manque = number_format($type_prix - $prix, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');

                    $this->debtPay[] = [
                        "user_nom" => $user->nom,
                        "user_prenom" => $user->prenom,
                        "user_parcours" => optional(optional($user->info->first())->parcours)->sigle ?? 'N/A',
                        "type_nom" => $type->nom,
                        "montant" => $montant,
                        "manque" => $manque
                    ];
                }
            }
        }
    }
}
