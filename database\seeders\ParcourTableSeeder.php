<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ParcourTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table("parcours")->insert([
            // ["sigle" => "GTRH", "nom" => "Gestion Touristique, Restauration et Hôtellerie", "mention_id" => 1],
            // ["sigle" => "TBA", "nom" => "Technique Bancaire et Assurance", "mention_id" => 1],
            // ["sigle" => "COM", "nom" => "Communication et Marketing", "mention_id" => 1],
            // ["sigle" => "GFC", "nom" => "Gestion Finance et Comptable", "mention_id" => 1],
            // ["sigle" => "LCI", "nom" => "Logistique et Commerce internationale", "mention_id" => 1],
            // ["sigle" => "GM", "nom" => "Génie Mécanique", "mention_id" => 2],
            // ["sigle" => "GE", "nom" => "Génie Electrique", "mention_id" => 2],
            // ["sigle" => "BTP", "nom" => "BATIMENT ET TRAVAUX PUBLICS", "mention_id" => 2],

            ["sigle" => "THR", "nom" => "Tourisme Hôtellerie et Restauration", "mention_id" => 4],
            ["sigle" => "TBA", "nom" => "Techniques Bancaire et Assurance", "mention_id" => 1],
            ["sigle" => "CM", "nom" => "Communication et Marketing", "mention_id" => 5],
            ["sigle" => "GFC", "nom" => "Gestion Finance et Comptabilité", "mention_id" => 3],
            ["sigle" => "TD", "nom" => "Transit et Douane", "mention_id" => 2],
            ["sigle" => "GPM", "nom" => "Génie Productique Mécanique", "mention_id" => 8],
            ["sigle" => "GEE", "nom" => "Génie Electrique et Electronique", "mention_id" => 6],
            ["sigle" => "GBC", "nom" => "Génie Bâtiment et Construction", "mention_id" => 7],
            ["sigle" => "CCC", "nom" => "Conseille Chargé Clientèle", "mention_id" => 1],
            ["sigle" => "LCI", "nom" => "Logistique et Commerce International", "mention_id" => 2],
            ["sigle" => "RCA", "nom" => "Révision Comptable et Audit", "mention_id" => 3],
            ["sigle" => "GVT", "nom" => "Gestion et Valorisation des Activités Touristiques", "mention_id" => 4],
            ["sigle" => "GET", "nom" => "Gestion des Etablissements Touristiques", "mention_id" => 4],
            ["sigle" => "ICA", "nom" => "Information et Communication des Affaires", "mention_id" => 5],
            ["sigle" => "ISE", "nom" => "Ingénierie des Systèmes électriques", "mention_id" => 6],
            ["sigle" => "SEE", "nom" => "Systèmes électriques et Electronique", "mention_id" => 6],
            ["sigle" => "BIU", "nom" => "Bâtiment et Infrastructure Urbaines", "mention_id" => 7],
            ["sigle" => "GTP", "nom" => "Génie Travaux Publics", "mention_id" => 7],
            ["sigle" => "IEM", "nom" => "Ingénierie des Equipement Mécanique", "mention_id" => 8],
            ["sigle" => "GMN", "nom" => "Génie Mécatronique Navale", "mention_id" => 8],
            ["sigle" => "MMS", "nom" => "Mécanique des Matériaux et des Structures", "mention_id" => 8],
            ["sigle" => "ADA", "nom" => "Administration des affaires", "mention_id" => 9],
            ["sigle" => "AES", "nom" => "ADMINISTRATION DES ETABLISSEMENTS DE SANTE", "mention_id" => 10],
            ["sigle" => "GRT", "nom" => "Génie Réseaux et Télécommunications", "mention_id" => 11],
        ]);
    }
}
