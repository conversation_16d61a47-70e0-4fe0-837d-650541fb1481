<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('matieres', function (Blueprint $table) {
            $table->id();
            $table->string("nom");
            $table->string("code")->unique();
            $table->longText("syllabus")->nullable();
            $table->foreignId("user_id")->constrained();
            $table->foreignId("ue_id")->constrained();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('matieres', function (Blueprint $table) {
            $table->dropForeign("user_id");
            $table->dropForeign("ue_id");
        });

        Schema::dropIfExists('matieres');
    }
};
