<?php

namespace App\Http\Controllers;

use App\Models\HistoriquePayment;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;

class PdfController extends Controller
{
    public function generatePdf() {
       $pays = HistoriquePayment::with(['user', 'payment', 'moyen', 'encaissement'])->whereDate('created_at', now()->today())->get();

       $pdf = Pdf::loadView('pdf.payment', compact('pays'));
       return $pdf->download("payment ". now() . ".pdf");
    }
}
