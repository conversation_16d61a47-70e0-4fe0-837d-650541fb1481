var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Greek = {
    weekdays: {
        shorthand: ["Κυ", "Δε", "Τρ", "Τε", "Πέ", "Πα", "<PERSON><PERSON>"],
        longhand: [
            "Κυρια<PERSON><PERSON>",
            "Δευτέρα",
            "Τρίτη",
            "Τετάρτη",
            "Πέμπτη",
            "Παρασκευή",
            "Σάββατο",
        ],
    },
    months: {
        shorthand: [
            "Ιαν",
            "Φεβ",
            "Μάρ",
            "Απρ",
            "Μάι",
            "Ιούν",
            "Ιούλ",
            "Αύγ",
            "Σεπ",
            "Οκτ",
            "Νοέ",
            "Δεκ",
        ],
        longhand: [
            "Ιανουάριος",
            "Φεβρουάριος",
            "<PERSON>άρτιος",
            "Απρί<PERSON>ι<PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>νι<PERSON>",
            "Ιούλιος",
            "Αύγουστος",
            "Σεπτέμβρι<PERSON>",
            "Οκτ<PERSON>βριος",
            "Νοέμβρι<PERSON>",
            "Δεκέμβρι<PERSON>",
        ],
    },
    firstDayOfWeek: 1,
    ordinal: function () {
        return "";
    },
    weekAbbreviation: "Εβδ",
    rangeSeparator: " έως ",
    scrollTitle: "Μετακυλήστε για προσαύξηση",
    toggleTitle: "Κάντε κλικ για αλλαγή",
    amPM: ["ΠΜ", "ΜΜ"],
    yearAriaLabel: "χρόνος",
    monthAriaLabel: "μήνας",
    hourAriaLabel: "ώρα",
    minuteAriaLabel: "λεπτό",
};
fp.l10ns.gr = Greek;
export default fp.l10ns;
