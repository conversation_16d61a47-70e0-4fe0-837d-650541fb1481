/*
 *  Document   : datatables.js
 *  Author     : pixelcave
 *  Description: Using custom JS code to init DataTables plugin
 */

// DataTables, for more examples you can check out https://www.datatables.net/
class pageTablesDatatables {
  /*
   * Init DataTables functionality
   *
   */
  static initDataTables() {
    // Override a few default classes
    jQuery.extend(jQuery.fn.DataTable.ext.classes, {
      sWrapper: "dataTables_wrapper dt-bootstrap5",
      sFilterInput: "form-control form-control-sm",
      sLengthSelect: "form-select form-select-sm"
    });

    // Override a few defaults
    jQuery.extend(true, jQuery.fn.DataTable.defaults, {
      language: {
        lengthMenu: "_MENU_",
        search: "_INPUT_",
        searchPlaceholder: "Rechercher..",
        info: "Page <strong>_PAGE_</strong> à <strong>_PAGES_</strong>",
        infoFiltered: "(filtrées depuis un total de _MAX_ entrées)",
        infoEmpty: "Affichage de 0 à 0 sur 0 entrées",
        zeroRecords: "Aucune entrée correspondante trouvée",
        paginate: {
          first: '<i class="fa fa-angle-double-left"></i>',
          previous: '<i class="fa fa-angle-left"></i>',
          next: '<i class="fa fa-angle-right"></i>',
          last: '<i class="fa fa-angle-double-right"></i>'
        }
      }
    });

    // Override buttons default classes
    jQuery.extend(true, jQuery.fn.DataTable.Buttons.defaults, {
      dom: {
        button: {
          className: 'btn btn-sm btn-primary'
        },
      }
    });

    // Init full DataTable
    jQuery('.js-dataTable-full').dataTable({
      pageLength: 5,
      lengthMenu: [[5, 10, 15, 20], [5, 10, 15, 20]],
      autoWidth: false
    });

    // Init DataTable with Buttons
    jQuery('.js-dataTable-buttons').dataTable({
      pageLength: 5,
      lengthMenu: [[5, 10, 15, 20], [5, 10, 15, 20]],
      autoWidth: false,
      buttons: ['copy', 'csv', 'excel', 'pdf', 'print'],
      dom: "<'row'<'col-sm-12'<'text-center bg-body-light py-2 mb-2'B>>>" +
        "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>"
    });
  }

  /*
   * Init functionality
   *
   */
  static init() {
    this.initDataTables();
  }
}

// Initialize when page loads
One.onLoad(() => pageTablesDatatables.init());
