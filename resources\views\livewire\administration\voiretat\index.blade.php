@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    
    <!-- Select2 pour améliorer la sélection multiple -->
    <script src="{{ asset('js/plugins/select2/js/select2.full.min.js') }}"></script>
@endsection

@section('css')
    <link rel="stylesheet" href="{{ asset('js/plugins/select2/css/select2.min.css') }}">
@endsection

<div wire:ignore.self>
    @if ($currentPage == PAGECREATEFORM)
        @include('livewire.administration.voiretat.create')
    @endif

    @if ($currentPage == PAGEEDITFORM)
        @include('livewire.administration.voiretat.edit')
    @endif

    @if ($currentPage == PAGELIST)
        @include('livewire.administration.voiretat.liste')
    @endif
</div>

<script>
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: event.detail.type || 'success',
            icon: event.detail.icon || 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    });

    document.addEventListener('livewire:load', function () {
        // Initialiser Select2 pour le sélecteur multiple
        initSelect2();

        // Réinitialiser Select2 quand Livewire rafraîchit le DOM
        Livewire.hook('message.processed', (message, component) => {
            initSelect2();
        });
    });

    function initSelect2() {
        if (document.getElementById('type-payment-select')) {
            $('#type-payment-select').select2({
                placeholder: 'Sélectionnez les types de paiement',
                allowClear: true
            }).on('change', function (e) {
                // Synchroniser les valeurs sélectionnées avec Livewire
                @this.set('newEtus.type_payment_id', $(this).val());
            });
        }
    }
</script>