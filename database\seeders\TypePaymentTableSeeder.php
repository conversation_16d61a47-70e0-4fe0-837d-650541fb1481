<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TypePaymentTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table("type_payments")->insert([
            ["nom"=>"Droit d'inscription"],
            ["nom"=>"Fiche"],
            ["nom"=>"Droit d'examen"],
            ["nom"=>"Certificat de scolarité"],
            ["nom"=>"Impression"],
            ["nom"=>"Photocopie"],
            ["nom"=>"Droit d'encadrement"],
            ["nom"=>"Droit de soutenance"],
            ["nom"=>"Droit de diplôme"],
            ["nom"=>"Fiche de stage"],
            ["nom"=>"Ecolage Novembre"],
            ["nom"=>"Ecolage Décembre"],
            ["nom"=>"Ecolage Janvier"],
            ["nom"=>"Ecolage Février"],
            ["nom"=>"Ecolage Mars"],
            ["nom"=>"Ecolage Avril"],
            ["nom"=>"Ecolage Mai"],
            ["nom"=>"Ecolage Juin"],
            ["nom"=>"Ecolage Juillet"],
            ["nom"=>"Ecolage Août"],
            ["nom"=>"Ecolage Septembre"],
            ["nom"=>"Ecolage Octobre"],
            ["nom"=>"Polo"]
        ]);
    }
}
