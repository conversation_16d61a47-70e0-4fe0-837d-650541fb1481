<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\MoyenPayment;
use Livewire\Component;

class DiversPay extends Component
{
    public $currentPage = PAGELIST;

    public $newPay = [];
    
    public function render()
    {
        return view('livewire.caf.diverspay.index', [
            "moyens" => MoyenPayment::all(),
            "annee" => AnneeUniversitaire::all(),
            
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editDomaine.nom' => 'required',
            ];
        }

        return [
            "newPay.code" => 'required',
            "newPay.montant" => "required|numeric",
            "newPay.moyen" => 'required',
            "newPay.libelle" => 'required',
            "newPay.annee_universitaire_id" => 'required',
            "newPay.type_id" => 'required',
            
        ];
    }

    

    public function addPay()
    {
        
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        $type = ($validationAttributes["newPay"]["type_id"] == 31) ? 2 : 1;

        auth()->user()->historique()->create([
            "moyen_payment_id" => $validationAttributes["newPay"]["moyen"],
            "libelle" => $validationAttributes["newPay"]["libelle"],
            "montant" => $validationAttributes["newPay"]["montant"],
            "code" => $validationAttributes["newPay"]["code"],
            "type_encaissement_id" => $type,
            "annee_universitaire_id" => $validationAttributes["newPay"]["annee_universitaire_id"],
            "type_payment_id" => $validationAttributes["newPay"]["type_id"],
        ]);

        $this->newPay = [];

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Payment effectué avec succès!"]);
    }
}
