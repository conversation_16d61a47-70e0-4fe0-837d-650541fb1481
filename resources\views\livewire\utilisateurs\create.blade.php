<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListUser()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListUser()">Liste d'utilisateur</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Creation d'utilisateur
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="addUser()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire d'ajout</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.nom"
                            class="form-control @error('newUser.nom') is-invalid @enderror" id="example-text-input"
                            name="firstname" placeholder="Text Input">

                        @error('newUser.nom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Prénom <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.prenom"
                            class="form-control @error('newUser.prenom') is-invalid @enderror" id="example-text-input"
                            name="lastname" placeholder="Text Input">

                        @error('newUser.prenom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Date de naissance <span
                                class="text-danger">*</span></label>
                        <input type="text" onchange='Livewire.emit("selectDate", this.value)'
                            class="js-datepicker form-control @error('newUser.date_naissance') is-invalid @enderror"
                            id="example-text-input" name="birthday" data-week-start="1" data-autoclose="true"
                            data-today-highlight="true" data-date-format="dd/mm/yy" placeholder="dd/mm/yy">

                        @error('newUser.date_naissance')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-select">Sexe <span class="text-danger">*</span></label>
                        <select class="form-select @error('newUser.sexe') is-invalid @enderror"
                            wire:model="newUser.sexe" id="example-select" name="example-select">
                            <option selected value="">Open this select menu</option>
                            <option value="H">Homme</option>
                            <option value="F">Femme</option>
                        </select>

                        @error('newUser.sexe')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Lieu de naissance <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.lieu_naissance"
                            class="form-control @error('newUser.lieu_naissance') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('newUser.lieu_naissance')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Adresse <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.adresse"
                            class="form-control @error('newUser.adresse') is-invalid @enderror" id="example-text-input"
                            name="address" placeholder="Text Input">

                        @error('newUser.adresse')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nationalité <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.nationalite"
                            class="form-control @error('newUser.nationalite') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('newUser.nationalite')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Ville <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.ville"
                            class="form-control @error('newUser.ville') is-invalid @enderror" id="example-text-input"
                            name="example-text-input" placeholder="Text Input">

                        @error('newUser.ville')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Pays <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.pays"
                            class="form-control @error('newUser.pays') is-invalid @enderror" id="example-text-input"
                            name="example-text-input" placeholder="Text Input">

                        @error('newUser.pays')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-email-input">Email <span
                                class="text-danger">*</span></label>
                        <input type="email" wire:model="newUser.email"
                            class="form-control @error('newUser.email') is-invalid @enderror" id="example-email-input"
                            name="example-email-input" placeholder="Email Input">

                        @error('newUser.email')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone1 <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.telephone1"
                            class="form-control @error('newUser.telephone1') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('newUser.telephone1')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone2</label>
                        <input type="text" wire:model="newUser.telephone2" class="form-control"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">numeroPieceIdentite</label>
                        <input type="text" wire:model="newUser.cin"
                            class="form-control @error('newUser.cin') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('newUser.cin')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    {{-- Role --}}
                    <h2 class="content-heading border-bottom mb-4 pb-2">Divers informations</h2>

                    <div class="col-12">
                        <label class="form-label" for="example-select">Rôle</label>
                        <select class="form-select @error('newUser.role') is-invalid @enderror"
                            wire:model="newUser.role" id="example-select" name="example-select">
                            <option selected value="0">Open this select menu</option>
                            @foreach ($roles as $role)
                                <option value="{{ $role->id }}">{{ $role->nom }}</option>
                            @endforeach
                        </select>

                        @error('newUser.role')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    @if ($isEtu)
                        <div class="col-6">
                            <label class="form-label" for="example-select1">Parcours(pour les étudiants)</label>
                            <select class="form-select @error('newUser.parcour_id') is-invalid @enderror"
                                wire:model="newUser.parcour_id" id="example-select1" name="example-select1">
                                <option selected value="null">Open this select menu</option>
                                @foreach ($parcours as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->nom }}</option>
                                @endforeach
                            </select>

                            @error('newUser.parcour_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="col-6">
                            <label class="form-label" for="example-select1">Niveau(pour les étudiants)</label>
                            <select class="form-select @error('newUser.niveau_id') is-invalid @enderror"
                                wire:model="newUser.niveau_id" id="example-select1" name="example-select1">
                                <option selected value="null">Open this select menu</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>

                            @error('newUser.niveau_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    @endif



                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
