<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('inscription_students', function (Blueprint $table) {
            $table->id();
            $table->foreignId("annee_universitaire_id")->constrained();
            $table->foreignId("user_id")->constrained();
            $table->foreignId("parcour_id")->constrained();
            $table->foreignId("niveau_id")->constrained();
            $table->timestamps();
            $table->softDeletes();
        });

        
        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        Schema::table('inscription_students', function (Blueprint $table) {
            $table->dropForeign("user_id");
            $table->dropForeign("parcour_id");
            $table->dropForeign("niveau_id");
            $table->dropForeign("annee_universitaire_id");
        });
        Schema::dropIfExists('inscription_students');
    }
};
