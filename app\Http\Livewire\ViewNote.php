<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\Semestre;
use App\Models\TypeNote;
use App\Models\Ue;
use App\Models\User;
use Illuminate\Support\Collection;
use Livewire\Component;
use Livewire\WithPagination;

class ViewNote extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $editNote = [];
    public $query;
    public $filtreAnnee;
    public $filtreParcours;
    public $filtreNiveau;
    public $filtreType;

    public User $current_user;
    public AnneeUniversitaire $current_annee;
    public Semestre $current_semestre;
    public Niveau $current_niveau;
    public Collection $matieres;
    public Collection $parcours;
    public ?int $parcour = null;

    public ?int $matiere = null;

    public function updatingQuery()
    {
        $this->resetPage();
    }
    public function updatingFiltreAnnee()
    {
        $this->resetPage();
    }
    public function updatingFiltreParcours()
    {
        $this->resetPage();
    }
    public function updatingFiltreNiveau()
    {
        $this->resetPage();
    }
    public function updatingFiltreType()
    {
        $this->resetPage();
    }

    public function render()
    {

        $persQuery = Note::query()->with(['user', 'matiere', 'typeNote', 'ue']);

        if ($this->query != "") {
            $persQuery->whereHas('user', function ($query) {
                $query->where('nom', 'like', '%' . $this->query . '%')
                    ->orWhere('prenom', 'like', '%' . $this->query . '%');
            });
        }

        if ($this->filtreParcours != "") {
            $persQuery->whereHas('matiere.ue.parcours', fn($q) => $q->whereParcourId($this->filtreParcours));
        }
        if ($this->filtreNiveau != "") {
            $persQuery->whereHas('matiere.ue.niveau', fn($q) => $q->whereNiveauId($this->filtreNiveau));
        }
        if ($this->filtreType != "") {
            $persQuery->whereTypeNoteId($this->filtreType);
        }

        if ($this->filtreAnnee != "") {
            $persQuery->whereHas('matiere.ue', fn($q) => $q->whereAnneeUniversitaireId($this->filtreAnnee));
        }

        return view('livewire.deraq.viewnote.index', [
            "notes" => $persQuery->latest()->paginate(10),
            "supNote" => $persQuery->latest()->onlyTrashed()->paginate(10),
            "annees" => AnneeUniversitaire::all(),
            "parcourse" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "typenotes" => TypeNote::all(),

        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        return [
            "editNote.valeur" => "required|numeric|min:0|max:20",
            "editNote.history_note_id" => "required",
            "editNote.matiere_id" => "required",
        ];
    }

    public function goToEditNote(Note $note, User $user, AnneeUniversitaire $annee, Semestre $semestre, Niveau $niveau_id)
    {

        $this->currentPage = PAGEEDITFORM;
        $this->editNote = $note->toArray();
        $this->current_user = $user;
        $this->current_annee = $annee;
        $this->current_semestre = $semestre;
        $this->current_niveau = $niveau_id;
        $this->parcours = Parcour::all();
        $this->matieres = collect();

        // dd($annee, $semestre, $niveau_id);
    }

    public function updatedParcour($value)
    {
        $this->matieres = Matiere::whereHas('ue', fn($q) => $q->whereSemestreId($this->current_semestre->id)
            ->whereParcourId($value)
            ->whereNiveauId($this->current_niveau->id)
            ->whereAnneeUniversitaireId($this->current_annee->id))
            ->get(['id', 'nom']);
        $this->matiere = $this->matieres->first()->id ?? null;
    }

    public function goToListNote()
    {
        $this->currentPage = PAGELIST;
        $this->editNote = [];
        $this->parcour = null;
    }

    public function goToSuppNote()
    {
        $this->currentPage = PAGECREATEFORM;
    }

    public function updateNote()
    {
        $this->validate();

        Note::find($this->editNote["id"])->update([
            "valeur" => $this->editNote["valeur"],
            "history_note_id" => $this->editNote["history_note_id"],
            "matiere_id" => $this->matiere,
        ]);


        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Note mis à jour avec succès!"]);
        $this->currentPage = PAGELIST;
        $this->parcour = null;
    }

}
