<!-- Edit Student Modal -->
<div class="modal fade" id="modal-edit-student" tabindex="-1" role="dialog" aria-labelledby="modal-edit-student-label" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-edit-student-label">Modifier l'Étudiant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" wire:click="closeEditStudentModal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form wire:submit.prevent="updateUser">
                    {{-- Hidden input might be useful if needed, but ID is in $editUser['id'] --}}
                    {{-- <input type="hidden" wire:model="editUser.id"> --}}
                    {{-- <input type="hidden" wire:model="editParcours.id"> --}}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="editUser.nom" class="form-label">Nom <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('editUser.nom') is-invalid @enderror" id="editUser.nom" wire:model.defer="editUser.nom" required>
                            @error('editUser.nom') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="col-md-6">
                            <label for="editUser.prenom" class="form-label">Prénom(s)</label>
                            <input type="text" class="form-control @error('editUser.prenom') is-invalid @enderror" id="editUser.prenom" wire:model.defer="editUser.prenom">
                             @error('editUser.prenom') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="editUser.telephone1" class="form-label">Téléphone <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control @error('editUser.telephone1') is-invalid @enderror" id="editUser.telephone1" wire:model.defer="editUser.telephone1" required>
                            @error('editUser.telephone1') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                         <div class="col-md-6">
                            <label for="editUser.email" class="form-label">Email</label>
                            <input type="email" class="form-control @error('editUser.email') is-invalid @enderror" id="editUser.email" wire:model.defer="editUser.email">
                            @error('editUser.email') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                     <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="editUser.sexe" class="form-label">Sexe <span class="text-danger">*</span></label>
                            <select class="form-select @error('editUser.sexe') is-invalid @enderror" id="editUser.sexe" wire:model.defer="editUser.sexe" required>
                                <option value="M">Masculin</option>
                                <option value="F">Féminin</option>
                            </select>
                             @error('editUser.sexe') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="col-md-4">
                            <label for="editUser.date_naissance" class="form-label">Date de Naissance</label>
                            {{-- Use wire:key to help Livewire track the flatpickr input correctly --}}
                            <input type="text" class="form-control js-flatpickr @error('editUser.date_naissance') is-invalid @enderror" id="editUser.date_naissance" wire:model.defer="editUser.date_naissance" placeholder="YYYY-MM-DD" wire:key="edit_dob_{{ $editUser['id'] ?? 'new' }}">
                             @error('editUser.date_naissance') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                         <div class="col-md-4">
                            <label for="editUser.lieu_naissance" class="form-label">Lieu de Naissance</label>
                            <input type="text" class="form-control @error('editUser.lieu_naissance') is-invalid @enderror" id="editUser.lieu_naissance" wire:model.defer="editUser.lieu_naissance">
                             @error('editUser.lieu_naissance') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editUser.adresse" class="form-label">Adresse</label>
                        <input type="text" class="form-control @error('editUser.adresse') is-invalid @enderror" id="editUser.adresse" wire:model.defer="editUser.adresse">
                        @error('editUser.adresse') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>

                    <hr>
                    <h6 class="mb-3">Informations Académiques</h6>

                    <div class="row mb-3">
                        <div class="col-md-6">
                             <label for="editParcours.parcour_id" class="form-label">Parcours <span class="text-danger">*</span></label>
                             <select class="form-select @error('editParcours.parcour_id') is-invalid @enderror" id="editParcours.parcour_id" wire:model.defer="editParcours.parcour_id" required>
                                 <option value="">Sélectionner...</option>
                                 @foreach($parcours as $parcour)
                                     <option value="{{ $parcour->id }}">{{ $parcour->nom }} ({{ $parcour->sigle }})</option>
                                 @endforeach
                             </select>
                             @error('editParcours.parcour_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="col-md-6">
                             <label for="editParcours.niveau_id" class="form-label">Niveau <span class="text-danger">*</span></label>
                             <select class="form-select @error('editParcours.niveau_id') is-invalid @enderror" id="editParcours.niveau_id" wire:model.defer="editParcours.niveau_id" required>
                                 <option value="">Sélectionner...</option>
                                  @foreach($niveaux as $niveau)
                                     <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                 @endforeach
                             </select>
                             @error('editParcours.niveau_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                    <!-- Add other fields as needed -->

                    <div class="modal-footer px-0 pb-0">
                        <button type="button" class="btn btn-alt-secondary" data-bs-dismiss="modal" wire:click="closeEditStudentModal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            <span wire:loading wire:target="updateUser" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Enregistrer les modifications
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
