<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoryNote;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\TypeNote;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class CoursSecretaire extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    
    // Mode d'affichage (liste des cours ou notes d'un cours)
    public $viewMode = 'courses'; // 'courses' ou 'notes'
    
    // Paramètres pour la liste des cours
    public $query = '';
    public $filtreParcours = '';
    public $filtreNiveau = '';
    public $filtreAnnee = '';
    public $enseignantId = '';
    
    // Paramètres pour l'édition des notes
    public $currentCourseId;
    public $currentMatiere;
    public $noteType = 1;
    public $noteEtus = [];
    public $isEditable = true;
    public $searchTerm = '';
    public $bulkNote = null;
    public $selectedStudents = [];
    public $perPage = 10;
    public $saveStatus = [];
    public $autoSave = true;
    
    // Timer de sauvegarde automatique
    public $saveTimers = [];
    
    // Pour l'affichage
    public $showFilters = false;
    
    protected $listeners = ['refreshNotes' => '$refresh'];

    public function updatingQuery()
    {
        $this->resetPage();
    }
    
    public function updatingFiltreParcours()
    {
        $this->resetPage();
    }
    
    public function updatingFiltreNiveau()
    {
        $this->resetPage();
    }
    
    public function updatingFiltreAnnee()
    {
        $this->resetPage();
    }
    
    public function updatingEnseignantId()
    {
        $this->resetPage();
    }

    public function render()
    {
        if ($this->viewMode === 'courses') {
            return $this->renderCoursesList();
        } else {
            return $this->renderNoteEditor();
        }
    }
    
    protected function renderCoursesList()
    {
        $persQuery = Matiere::query()->with(['user', 'ue']);

        if ($this->query != "") {
            $persQuery->where(function ($query) {
                $query->where('nom', 'like', '%' . $this->query . '%')
                    ->orWhere('code', 'like', '%' . $this->query . '%');
            });
        }

        if ($this->filtreParcours != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereParcourId($this->filtreParcours));
        }
        
        if ($this->filtreNiveau != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereNiveauId($this->filtreNiveau));
        }
        
        if ($this->filtreAnnee != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereAnneeUniversitaireId($this->filtreAnnee));
        }
        
        if ($this->enseignantId != "") {
            $persQuery->where('user_id', $this->enseignantId);
        }

        return view('livewire.secretaire.cours.index', [
            "cours" => $persQuery->latest()->paginate(10),
            "enseignants" => User::whereHas('roles', fn($q) => $q->where('role_id', '=', 2))
                ->get(['id', 'nom', 'prenom']),
            "niveaux" => Niveau::all(),
            "parcours" => Parcour::all(),
            "annees" => AnneeUniversitaire::all()
        ])
        ->extends('layouts.backend')
        ->section('content');
    }
    
    protected function renderNoteEditor()
    {
        return view('livewire.secretaire.cours.liste', [
            "typeNotes" => TypeNote::all(),
            "noteEtus" => $this->noteEtus,
            "matiere" => $this->currentMatiere,
            "saveStatus" => $this->saveStatus
        ])
        ->extends('layouts.backend')
        ->section('content');
    }
    
    // Changer de mode pour passer à l'édition des notes d'un cours spécifique
    public function viewNotes($courseId)
    {
        $this->currentCourseId = $courseId;
        $this->currentMatiere = Matiere::find($courseId);
        $this->noteType = 1; // Par défaut, premier type de note
        $this->viewMode = 'notes';
        $this->loadStudents();
    }
    
    // Retourner à la liste des cours
    public function backToCourses()
    {
        $this->viewMode = 'courses';
        $this->reset(['currentCourseId', 'currentMatiere', 'noteEtus', 'selectedStudents', 'bulkNote']);
    }
    
    // Charger les étudiants pour un cours
    public function loadStudents()
    {
        $notes = Note::with(['user', 'historyNote'])
            ->whereMatiereId($this->currentMatiere->id)
            ->whereTypeNoteId($this->noteType)
            ->get();
        
        // Récupérer tous les étudiants actuels dans le niveau/parcours
        $users = User::whereHas('info', fn ($q) => 
            $q->whereAnneeUniversitaireId($this->currentMatiere->ue->annee_universitaire_id)
              ->whereNiveauId($this->currentMatiere->ue->niveau_id)
              ->whereParcourId($this->currentMatiere->ue->parcour_id)
        )
        ->when($this->searchTerm, function($query) {
            return $query->where(function($q) {
                $q->where('nom', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('prenom', 'like', '%' . $this->searchTerm . '%');
            });
        })
        ->get(['id', 'nom', 'prenom']);
        
        // Réinitialiser le statut de sauvegarde
        $this->saveStatus = [];
            
        if ($notes->isEmpty()) {
            $this->isEditable = true;
            $this->noteEtus = [];
            
            foreach ($users as $user) {
                $userId = $user->id;
                array_push($this->noteEtus, [
                    "user_id" => $userId,
                    "nom" => $user->nom, 
                    "prenom" => $user->prenom, 
                    "valeur" => "", 
                    "observation" => "",
                    "is_new" => true
                ]);
                $this->saveStatus[$userId] = null;
            }
        } else {
            // Utiliser les notes existantes
            $this->noteEtus = $notes->map(function($note) {
                return [
                    'id' => $note->id,
                    'user_id' => $note->user_id,
                    'nom' => $note->user->nom,
                    'prenom' => $note->user->prenom,
                    'valeur' => $note->valeur,
                    'observation' => $note->observation,
                    'is_new' => false,
                    'isEditable' => $note->historyNote->estModifiable
                ];
            })->toArray();
            
            $this->isEditable = $notes->first()->historyNote->estModifiable;
            
            // Ajouter les nouveaux étudiants non encore notés
            $existingUserIds = collect($this->noteEtus)->pluck('user_id')->toArray();
            
            foreach ($users as $user) {
                if (!in_array($user->id, $existingUserIds)) {
                    $userId = $user->id;
                    array_push($this->noteEtus, [
                        "user_id" => $userId,
                        "nom" => $user->nom, 
                        "prenom" => $user->prenom, 
                        "valeur" => "", 
                        "observation" => "",
                        "is_new" => true,
                        "isEditable" => $this->isEditable
                    ]);
                    $this->saveStatus[$userId] = null;
                }
            }
        }
        
        // Initialiser les statuts de sauvegarde
        foreach ($this->noteEtus as $etu) {
            $this->saveStatus[$etu['user_id']] = null;
        }
    }

    public function updatedNoteType()
    {
        $this->loadStudents();
    }

    public function updatedSearchTerm()
    {
        $this->loadStudents();
    }
    
    // Sauvegarde individuelle avec feedback visuel et minuteur
    public function updateNote($index)
    {
        $studentData = $this->noteEtus[$index];
        $userId = $studentData['user_id'];
        
        // Indiquer que la sauvegarde est en cours
        $this->saveStatus[$userId] = 'saving';
        
        try {
            // Cas 1: Note existante - Mettre à jour
            if (isset($studentData['id']) && !$studentData['is_new']) {
                Note::where('id', $studentData['id'])
                    ->update([
                        'valeur' => $studentData['valeur'],
                        'observation' => $studentData['observation'] ?? "",
                    ]);
            } 
            // Cas 2: Nouvelle note - Créer
            else {
                // Trouver l'historique de note existant pour cette matière/type
                $existingNote = Note::where('matiere_id', $this->currentMatiere->id)
                                    ->where('type_note_id', $this->noteType)
                                    ->first();
                
                $historyNoteId = $existingNote ? $existingNote->history_note_id : null;
                
                // Si pas d'historique trouvé, en créer un nouveau
                if (!$historyNoteId) {
                    $newHistory = HistoryNote::create([
                        "matiere_id" => $this->currentMatiere->id,
                        "type_note_id" => $this->noteType,
                        "user_id" => auth()->user()->id,
                        "estModifiable" => 1,
                    ]);
                    $historyNoteId = $newHistory->id;
                }
                
                // Créer la note pour cet étudiant
                $newNote = Note::create([
                    "type_note_id" => $this->noteType,
                    "valeur" => $studentData['valeur'] ?? 0,
                    "user_id" => $userId,
                    "matiere_id" => $this->currentMatiere->id,
                    "history_note_id" => $historyNoteId,
                    "observation" => $studentData['observation'] ?? "",
                ]);
                
                // Mettre à jour les données locales avec l'ID de la nouvelle note
                $this->noteEtus[$index]['id'] = $newNote->id;
                $this->noteEtus[$index]['is_new'] = false;
            }
            
            // Après un délai, indiquer que la sauvegarde est terminée
            $this->saveStatus[$userId] = 'saved';
            
            // Après 3 secondes, effacer le statut
            $this->dispatchBrowserEvent('startSaveStatusTimer', [
                'userId' => $userId,
                'timeout' => 3000
            ]);
        } catch (\Exception $e) {
            $this->saveStatus[$userId] = 'error';
            $this->dispatchBrowserEvent('showErrorMessage', [
                'message' => 'Erreur lors de la sauvegarde: ' . $e->getMessage()
            ]);
        }
    }
    
    // Réinitialiser le statut de sauvegarde après le timer
    public function resetSaveStatus($userId)
    {
        $this->saveStatus[$userId] = null;
    }
    
    // Appliquer une note à plusieurs étudiants sélectionnés
    public function applyBulkNote()
    {
        if ($this->bulkNote === null || empty($this->selectedStudents)) {
            return;
        }
        
        // Récupérer ou créer l'historique des notes
        $existingNote = Note::where('matiere_id', $this->currentMatiere->id)
                            ->where('type_note_id', $this->noteType)
                            ->first();
        
        $historyNoteId = null;
        
        if ($existingNote) {
            $historyNoteId = $existingNote->history_note_id;
        } else {
            $newHistory = HistoryNote::create([
                "matiere_id" => $this->currentMatiere->id,
                "type_note_id" => $this->noteType,
                "user_id" => auth()->user()->id,
                "estModifiable" => 1,
            ]);
            $historyNoteId = $newHistory->id;
        }
        
        // Appliquer la note en masse
        foreach ($this->selectedStudents as $index) {
            $this->noteEtus[$index]['valeur'] = $this->bulkNote;
            
            $studentData = $this->noteEtus[$index];
            $userId = $studentData['user_id'];
            
            // Note existante - Mettre à jour
            if (isset($studentData['id']) && !$studentData['is_new']) {
                Note::where('id', $studentData['id'])
                    ->update([
                        'valeur' => $this->bulkNote,
                        'observation' => $studentData['observation'] ?? "",
                    ]);
            } 
            // Nouvelle note - Créer
            else {
                $newNote = Note::create([
                    "type_note_id" => $this->noteType,
                    "valeur" => $this->bulkNote,
                    "user_id" => $userId,
                    "matiere_id" => $this->currentMatiere->id,
                    "history_note_id" => $historyNoteId,
                    "observation" => $studentData['observation'] ?? "",
                ]);
                
                $this->noteEtus[$index]['id'] = $newNote->id;
                $this->noteEtus[$index]['is_new'] = false;
            }
        }
        
        $this->bulkNote = null;
        $this->selectedStudents = [];
        
        $this->dispatchBrowserEvent('showSuccessMessage', [
            'message' => 'Notes appliquées en masse avec succès!'
        ]);
    }
    
    // Sélectionner/désélectionner un étudiant
    public function toggleSelectStudent($index)
    {
        if (in_array($index, $this->selectedStudents)) {
            $this->selectedStudents = array_diff($this->selectedStudents, [$index]);
        } else {
            $this->selectedStudents[] = $index;
        }
    }
    
    // Sélectionner/désélectionner tous les étudiants
    public function selectAllStudents()
    {
        if (count($this->selectedStudents) === count($this->noteEtus)) {
            $this->selectedStudents = [];
        } else {
            $this->selectedStudents = array_keys($this->noteEtus);
        }
    }
    
    // Basculer l'état des filtres
    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }
    
    // Réinitialiser tous les filtres
    public function resetFilters()
    {
        $this->query = '';
        $this->filtreParcours = '';
        $this->filtreNiveau = '';
        $this->filtreAnnee = '';
        $this->enseignantId = '';
    }
    
    // Basculer l'état de la sauvegarde automatique
    public function toggleAutoSave()
    {
        $this->autoSave = !$this->autoSave;
    }

}
