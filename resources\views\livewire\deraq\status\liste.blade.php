<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="h3 fw-bold mb-0">Status d'ajout des notes</h1>
            <div>
                <button type="button" wire:click="clearFilters" class="btn btn-sm btn-alt-secondary">
                    <i class="fa fa-fw fa-filter-circle-xmark"></i> Effacer filtres
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <!-- Filter Panel -->
    <div class="block block-rounded mb-2">
        <div class="block-header block-header-default">
            <h3 class="block-title">Filtres</h3>
            <div class="block-options">
                <button type="button" class="btn-block-option" data-toggle="block-option" data-action="content_toggle">
                    <i class="si si-arrow-up"></i>
                </button>
            </div>
        </div>
        <div class="block-content">
            <div class="row mb-3">
                <div class="col-sm-12 col-md-9">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <label class="form-label small" for="filtreParcours">Parcours</label>
                            <select wire:model="filtreParcours" id="filtreParcours" class="form-select form-select-sm">
                                <option value="">Tous les parcours</option>
                                @foreach ($parcours as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label class="form-label small" for="filtreNiveau">Niveau</label>
                            <select wire:model="filtreNiveau" id="filtreNiveau" class="form-select form-select-sm">
                                <option value="">Tous les niveaux</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label class="form-label small" for="filtreAnnee">Année</label>
                            <select wire:model="filtreAnnee" id="filtreAnnee" class="form-select form-select-sm">
                                <option value="">Toutes les années</option>
                                @foreach ($annees as $annee)
                                    <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label class="form-label small" for="filtreSemestre">Semestre</label>
                            <select wire:model="filtreSemestre" id="filtreSemestre" class="form-select form-select-sm">
                                <option value="">Tous les semestres</option>
                                @foreach ($semestres ?? [] as $semestre)
                                    <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-3">
                    <label class="form-label small" for="searchQuery">Recherche</label>
                    <div class="input-group">
                        <input type="search" wire:model.debounce.500ms="query" id="searchQuery" class="form-control form-control-sm" 
                            placeholder="Rechercher un cours...">
                        <span class="input-group-text">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Status table -->
    <div class="block block-rounded">
        <div class="block-content block-content-full">
            <div class="table-responsive">
                <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                <table class="table table-bordered table-striped table-vcenter">
                    <thead>
                        <tr>
                            <th style="width: 30%;">Matière</th>
                            <th class="text-center" style="width: 15%;">Parcours/Niveau</th>
                            <th class="text-center" style="width: 18%;">Partiel 1</th>
                            <th class="text-center" style="width: 18%;">Partiel 2</th>
                            <th class="text-center" style="width: 18%;">Examen</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($status as $stat)
                            <tr>
                                <td>
                                    <div class="fw-semibold">{{ $stat->nom }}</div>
                                    <div class="fs-sm text-muted">{{ $stat->code }}</div>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-primary">{{ $stat->ue->parcours->sigle }}</span>
                                    <span class="badge bg-info">{{ $stat->ue->niveau->nom }}</span>
                                </td>
                                
                                <!-- Partiel 1 Column -->
                                <td class="text-center">
                                    <div class="d-flex flex-column align-items-center">
                                        @if ($stat->note->isNotEmpty())
                                            @if ($stat->note->where('type_note_id', 1)->isEmpty())
                                                <div class="mb-1">
                                                    <i class="fa fa-fw fa-circle-xmark text-danger"></i>
                                                    <span class="fs-sm">Non disponible</span>
                                                </div>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" 
                                                        wire:click="ajoutNote({{ $stat->id }}, 1, 2)"
                                                        class="btn btn-sm btn-alt-secondary {{$processingNoteId == $stat->id && $processingTypeId == 1 ? 'disabled' : ''}}" 
                                                        wire:loading.attr="disabled" 
                                                        {{$stat->note->where('type_note_id', 2)->isEmpty() ? 'disabled' : ''}}>
                                                        
                                                        @if($processingNoteId == $stat->id && $processingTypeId == 1)
                                                            <span class="spinner-border spinner-border-sm" role="status"></span>
                                                        @else
                                                            <i class="fa fa-copy me-1"></i>
                                                        @endif
                                                        P2
                                                    </button>
                                                    <button type="button" 
                                                        wire:click="ajoutNote({{ $stat->id }}, 1, 3)"
                                                        class="btn btn-sm btn-alt-secondary {{$processingNoteId == $stat->id && $processingTypeId == 1 ? 'disabled' : ''}}" 
                                                        wire:loading.attr="disabled"
                                                        {{$stat->note->where('type_note_id', 3)->isEmpty() ? 'disabled' : ''}}>
                                                        
                                                        @if($processingNoteId == $stat->id && $processingTypeId == 1)
                                                            <span class="spinner-border spinner-border-sm" role="status"></span>
                                                        @else
                                                            <i class="fa fa-copy me-1"></i>
                                                        @endif
                                                        Ex
                                                    </button>
                                                </div>
                                            @else
                                                <div>
                                                    <i class="fa fa-fw fa-circle-check text-success"></i>
                                                    <span class="fs-sm">Disponible</span>
                                                </div>
                                                <div class="mt-1">
                                                    <span class="badge bg-success">{{ $stat->note->where('type_note_id', 1)->count() }} notes</span>
                                                </div>
                                            @endif
                                        @else
                                            <div>
                                                <i class="fa fa-fw fa-circle-xmark text-danger"></i>
                                                <span class="fs-sm">Pas de note</span>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                
                                <!-- Partiel 2 Column -->
                                <td class="text-center">
                                    <div class="d-flex flex-column align-items-center">
                                        @if ($stat->note->isNotEmpty())
                                            @if ($stat->note->where('type_note_id', 2)->isEmpty())
                                                <div class="mb-1">
                                                    <i class="fa fa-fw fa-circle-xmark text-danger"></i>
                                                    <span class="fs-sm">Non disponible</span>
                                                </div>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" 
                                                        wire:click="ajoutNote({{ $stat->id }}, 2, 1)"
                                                        class="btn btn-sm btn-alt-secondary {{$processingNoteId == $stat->id && $processingTypeId == 2 ? 'disabled' : ''}}" 
                                                        wire:loading.attr="disabled"
                                                        {{$stat->note->where('type_note_id', 1)->isEmpty() ? 'disabled' : ''}}>
                                                        
                                                        @if($processingNoteId == $stat->id && $processingTypeId == 2)
                                                            <span class="spinner-border spinner-border-sm" role="status"></span>
                                                        @else
                                                            <i class="fa fa-copy me-1"></i>
                                                        @endif
                                                        P1
                                                    </button>
                                                    <button type="button" 
                                                        wire:click="ajoutNote({{ $stat->id }}, 2, 3)"
                                                        class="btn btn-sm btn-alt-secondary {{$processingNoteId == $stat->id && $processingTypeId == 2 ? 'disabled' : ''}}" 
                                                        wire:loading.attr="disabled"
                                                        {{$stat->note->where('type_note_id', 3)->isEmpty() ? 'disabled' : ''}}>
                                                        
                                                        @if($processingNoteId == $stat->id && $processingTypeId == 2)
                                                            <span class="spinner-border spinner-border-sm" role="status"></span>
                                                        @else
                                                            <i class="fa fa-copy me-1"></i>
                                                        @endif
                                                        Ex
                                                    </button>
                                                </div>
                                            @else
                                                <div>
                                                    <i class="fa fa-fw fa-circle-check text-success"></i>
                                                    <span class="fs-sm">Disponible</span>
                                                </div>
                                                <div class="mt-1">
                                                    <span class="badge bg-success">{{ $stat->note->where('type_note_id', 2)->count() }} notes</span>
                                                </div>
                                            @endif
                                        @else
                                            <div>
                                                <i class="fa fa-fw fa-circle-xmark text-danger"></i>
                                                <span class="fs-sm">Pas de note</span>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                
                                <!-- Examen Column -->
                                <td class="text-center">
                                    <div class="d-flex flex-column align-items-center">
                                        @if ($stat->note->isNotEmpty())
                                            @if ($stat->note->where('type_note_id', 3)->isEmpty())
                                                <div class="mb-1">
                                                    <i class="fa fa-fw fa-circle-xmark text-danger"></i>
                                                    <span class="fs-sm">Non disponible</span>
                                                </div>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" 
                                                        wire:click="ajoutNote({{ $stat->id }}, 3, 1)"
                                                        class="btn btn-sm btn-alt-secondary {{$processingNoteId == $stat->id && $processingTypeId == 3 ? 'disabled' : ''}}" 
                                                        wire:loading.attr="disabled"
                                                        {{$stat->note->where('type_note_id', 1)->isEmpty() ? 'disabled' : ''}}>
                                                        
                                                        @if($processingNoteId == $stat->id && $processingTypeId == 3)
                                                            <span class="spinner-border spinner-border-sm" role="status"></span>
                                                        @else
                                                            <i class="fa fa-copy me-1"></i>
                                                        @endif
                                                        P1
                                                    </button>
                                                    <button type="button" 
                                                        wire:click="ajoutNote({{ $stat->id }}, 3, 2)"
                                                        class="btn btn-sm btn-alt-secondary {{$processingNoteId == $stat->id && $processingTypeId == 3 ? 'disabled' : ''}}" 
                                                        wire:loading.attr="disabled"
                                                        {{$stat->note->where('type_note_id', 2)->isEmpty() ? 'disabled' : ''}}>
                                                        
                                                        @if($processingNoteId == $stat->id && $processingTypeId == 3)
                                                            <span class="spinner-border spinner-border-sm" role="status"></span>
                                                        @else
                                                            <i class="fa fa-copy me-1"></i>
                                                        @endif
                                                        P2
                                                    </button>
                                                </div>
                                            @else
                                                <div>
                                                    <i class="fa fa-fw fa-circle-check text-success"></i>
                                                    <span class="fs-sm">Disponible</span>
                                                </div>
                                                <div class="mt-1">
                                                    <span class="badge bg-success">{{ $stat->note->where('type_note_id', 3)->count() }} notes</span>
                                                </div>
                                            @endif
                                        @else
                                            <div>
                                                <i class="fa fa-fw fa-circle-xmark text-danger"></i>
                                                <span class="fs-sm">Pas de note</span>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fa fa-search fa-2x mb-2"></i>
                                        <p>Aucun résultat trouvé pour cette recherche</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    Affichage de {{ $status->firstItem() ?? 0 }} à {{ $status->lastItem() ?? 0 }} sur {{ $status->total() }} résultats
                </div>
                <div>
                    {{ $status->links() }}
                </div>
            </div>
        </div>
    </div>
    <!-- END Dynamic Table Full -->
</div>
<!-- END Page Content -->