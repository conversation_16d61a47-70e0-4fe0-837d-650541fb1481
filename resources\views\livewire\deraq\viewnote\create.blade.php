 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">
<div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListNote()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListNote()">Liste des Historiques</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Historique supprimé
                    </li>
                </ol>
            </nav>
        </div>

     </div>
 </div>
 <!-- END Hero -->


 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         {{-- @dump($histories) --}}
         <div class="block-content block-content-full">
             <div class="row mb-3">
                 <div class="col-sm-12 col-md-6">
                     <div class="d-flex">
                         <div>
                             <label>
                                 <select wire:model="filtreParcours" class="form-select form-select-sm">
                                     <option selected value="">Filtre Parcours</option>
                                     @foreach ($parcours as $parcour)
                                         <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                     <option selected value="">Filtre Niveau</option>
                                     @foreach ($niveaux as $niveau)
                                         <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                     <option selected value="">Filtre Année</option>
                                     @foreach ($annees as $annee)
                                         <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreType" class="form-select form-select-sm">
                                     <option selected value="">Filtre Type</option>
                                     @foreach ($typenotes as $type)
                                         <option value="{{ $type->id }}">{{ $type->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         
                     </div>

                 </div>
                 <div class="col-sm-12 col-md-6 text-end">

                     <label>
                         <input type="search" wire:model="query" class="form-control form-control-sm"
                             placeholder="Search..">
                     </label>

                 </div>
             </div>
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter">
                 <thead>
                     <tr>
                         <th>Matière</th>
                         <th class="text-center d-none d-sm-table-cell">Correcteur</th>
                         <th class="text-center d-none d-sm-table-cell">Parcours</th>
                         <th class="text-center d-none d-sm-table-cell">Type</th>
                         <th class="text-center d-none d-sm-table-cell">Date de remise</th>
                         <th class="text-center" style="width: 150px;">Actions</th>
                     </tr>
                 </thead>
                 <tbody>

                     @foreach ($supNote as $history)
                         <tr>
                             <td class="fw-semibold">
                                 {{ $history->matieres->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $history->user->nom }} {{ $history->user->prenom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $history->matieres->ue->parcours->sigle }} {{ $history->matieres->ue->niveau->sigle }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $history->types->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $history->created_at }}
                             </td>
                             <td class="text-center">
                                 <div class="btn-group mb-2">
                                     <button type="button"
                                         wire:click="restoreHisto({{ $history->id }})"
                                         class="btn btn-sm btn-alt-secondary"
                                        >
                                         Restorer
                                     </button>
                                 </div>
                                 <button type="button" class="btn btn-sm btn-alt-secondary"
                                         wire:click="deleteDefHisto({{ $history->id }})" title="Delete">
                                         <i class="fa fa-fw fa-times"></i> Supprimer
                                     </button>
                             </td>
                         </tr>
                     @endforeach



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     {{ $supNote->links() }}
                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
