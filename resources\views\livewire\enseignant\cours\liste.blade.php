 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Cours
         </h1>

     </div>
 </div>
 <!-- END Hero -->


 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste de mes Cours
             </h3>
         </div>
         <div class="block-content block-content-full">
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter">
                 <thead>
                     <tr>
                         <th>Nom</th>
                         <th class="text-center d-none d-sm-table-cell">Parcours</th>
                         <th class="text-center d-none d-sm-table-cell">Niveau</th>
                         <th class="text-center">Année</th>
                         <th class="text-center" style="width: 110px;">Action</th>
                     </tr>
                 </thead>
                 <tbody>


                     @forelse ($cours as $cour)
                         <tr>
                             <td class="fw-semibold">
                                 {{ $cour->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $cour->ue->parcours->sigle }}
                             </td>

                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $cour->ue->niveau->nom }}
                             </td>
                             <td class="text-muted">
                                 {{ $cour->ue->annee->nom }}
                             </td>
                             <td class="text-center">
                                 @if ($cour->is_filled)
                                     <button class="btn btn-sm btn-alt-success">
                                         <i class="fa fa-check"></i>
                                         Notes
                                     </button>
                                 @else
                                     <a class="btn btn-sm btn-alt-secondary"
                                         href="{{ route('enseignant.gestions.cours.notes', ['coursId' => $cour->id]) }}"><i
                                             class="fa fa-plus"></i>
                                         Notes</a>
                                 @endif


                             </td>
                         </tr>

                     @empty
                         <tr>
                             <td colspan="4">
                                 <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                     role="alert">
                                     <div class="flex-grow-1 me-3">
                                         <p class="mb-0">
                                             Vous n'avez pas encore été soucrit à aucun cours!
                                         </p>
                                     </div>
                                     <div class="flex-shrink-0">
                                         <i class="fa fa-fw fa-exclamation-circle"></i>
                                     </div>

                                 </div>
                             </td>
                         </tr>
                     @endforelse





                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     {{ $cours->links() }}
                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
