<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoryNote;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\TypeNote;
use Livewire\Component;
use Livewire\WithPagination;

class NoteStatus extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $editNote = [];
    public $query;
    public $filtreAnnee;
    public $filtreParcours;
    public $filtreNiveau;
    
    // Add tracking of selected semester for better filtering
    public $filtreSemestre;
    
    // Track processing state for buttons
    public $processingNoteId = null;
    public $processingTypeId = null;
    
    // Add debounce to search to improve performance
    protected $updatesQueryString = ['query', 'filtreAnnee', 'filtreParcours', 'filtreNiveau', 'filtreSemestre'];
    
    // Improve performance by implementing listeners
    protected $listeners = ['refreshData' => '$refresh'];

    public function updatingQuery()
    {
        $this->resetPage();
    }
    
    public function updatingFiltreAnnee()
    {
        $this->resetPage();
    }
    
    public function updatingFiltreParcours()
    {
        $this->resetPage();
    }
    
    public function updatingFiltreNiveau()
    {
        $this->resetPage();
    }
    
    public function updatingFiltreSemestre()
    {
        $this->resetPage();
    }
    
    // Add method to clear all filters for better UX
    public function clearFilters()
    {
        $this->query = '';
        $this->filtreAnnee = '';
        $this->filtreParcours = '';
        $this->filtreNiveau = '';
        $this->filtreSemestre = '';
        $this->resetPage();
    }

    public function render()
    {
        $persQuery = Matiere::query()->with(['note', 'ue.parcours', 'ue.niveau', 'ue.annee']);

        if ($this->query != "") {
            $persQuery->where(function ($query) {
                $query->where('nom', 'like', '%' . $this->query . '%')
                    ->orWhere('code', 'like', '%' . $this->query . '%');
            });
        }
        
        if ($this->filtreParcours != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereParcourId($this->filtreParcours));
        }
        
        if ($this->filtreNiveau != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereNiveauId($this->filtreNiveau));
        }

        if ($this->filtreAnnee != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereAnneeUniversitaireId($this->filtreAnnee));
        }
        
        if ($this->filtreSemestre != "") {
            $persQuery->whereHas('ue', fn ($q) => $q->whereSemestreId($this->filtreSemestre));
        }
        
        // Improved eager loading to prevent N+1 query issues
        $annees = AnneeUniversitaire::orderBy('nom', 'desc')->get();
        $typeNotes = TypeNote::all();
        $parcours = Parcour::orderBy('sigle')->get();
        $niveaux = Niveau::orderBy('nom')->get();
        
        // Get semesters - assuming there's a Semestre model
        $semestres = \App\Models\Semestre::all() ?? collect();

        return view('livewire.deraq.status.index', [
            "status" => $persQuery->latest()->paginate(10),
            "annees" => $annees,
            "typeNotes" => $typeNotes,
            "parcours" => $parcours,
            "niveaux" => $niveaux,
            "semestres" => $semestres,
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function ajoutNote($matiere, $typeNoteActu, $typeNoteAjout)
    {
        // Set processing IDs to show spinner on the specific button
        $this->processingNoteId = $matiere;
        $this->processingTypeId = $typeNoteActu;
        
        $notes = Note::whereMatiereId($matiere)
            ->whereTypeNoteId($typeNoteAjout)
            ->get();

        if ($notes->isNotEmpty()) {
            try {
                $historyNote = HistoryNote::create([
                    "matiere_id" => $matiere,
                    "type_note_id" => $typeNoteActu,
                    "user_id" => auth()->user()->id,
                ]);

                foreach ($notes as $note) {
                    Note::create([
                        "type_note_id" => $typeNoteActu,
                        "valeur" => $note->valeur,
                        "user_id" => $note->user->id,
                        "matiere_id" => $matiere,
                        "history_note_id" => $historyNote->id,
                        "observation" => $note->observation,
                    ]);
                }

                $this->dispatchBrowserEvent("showSuccessMessage", [
                    "message" => "Notes copiées avec succès dans " . $this->getTypeNoteName($typeNoteActu),
                    "icon" => "fa fa-check-circle"
                ]);
            } catch (\Exception $e) {
                $this->dispatchBrowserEvent("showErrorMessage", [
                    "message" => "Erreur lors de la copie des notes: " . $e->getMessage(),
                    "icon" => "fa fa-exclamation-circle"
                ]);
            }
        } else {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Pas de notes à copier pour cette matière!",
                "icon" => "fa fa-exclamation-triangle"
            ]);
        }
        
        // Reset processing state
        $this->processingNoteId = null;
        $this->processingTypeId = null;
    }
    
    // Helper method to get note type name
    private function getTypeNoteName($typeNoteId)
    {
        $typeNames = [
            1 => 'Partiel 1',
            2 => 'Partiel 2',
            3 => 'Examen'
        ];
        
        return $typeNames[$typeNoteId] ?? 'Type inconnu';
    }
    
    // Method to handle bulk operations if needed
    public function bulkCopyNotes($fromType, $toType)
    {
        // Implementation would go here for a bulk operation
        // This would allow copying grades from one type to another for multiple subjects at once
    }
    
    // Method to check if a button is currently processing
    public function isProcessing($matiereId, $typeId)
    {
        return $this->processingNoteId == $matiereId && $this->processingTypeId == $typeId;
    }
}