@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    {{-- <script src="{{ asset('js/plugins/ckeditor5-classic/build/ckeditor.js') }}"></script> --}}

@endsection

<div wire:ignore.self>

    @if ($currentPage == PAGECREATEFORM)
        @include('livewire.deraq.domaine.create')
    @endif

    @if ($currentPage == PAGEEDITFORM)
        @include('livewire.deraq.domaine.edit')
    @endif

    @if ($currentPage == PAGELIST)
        @include('livewire.deraq.domaine.liste')
    @endif

</div>

<script>
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    })
</script>