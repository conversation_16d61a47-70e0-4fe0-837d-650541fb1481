@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    
    <!-- Select2 pour améliorer la sélection multiple -->
    <script src="{{ asset('js/plugins/select2/js/select2.full.min.js') }}"></script>
@endsection

@section('css')
    <link rel="stylesheet" href="{{ asset('js/plugins/select2/css/select2.min.css') }}">
    <style>
        /* Custom Select2 styles for better integration with Livewire */
        .select2-dropdown-fixed {
            z-index: 9999 !important; /* Ensure dropdown appears above other elements */
        }

        .select2-container--open {
            z-index: 9999 !important; /* Ensure container appears above other elements */
        }

        /* Improve Select2 styling for a more professional look */
        .select2-container--default .select2-selection--multiple {
            border-color: #d8dde5;
            border-radius: 0.25rem;
        }

        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border-color: #6c757d;
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #3B71CA;
            border-color: #3B71CA;
            color: #fff;
            border-radius: 0.2rem;
            padding: 2px 8px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: #fff;
            margin-right: 5px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #f8f9fa;
        }

        /* Print optimization for Select2 */
        @media print {
            .select2-container, .select2-dropdown, .select2-search, .select2-results {
                display: none !important;
            }
        }
    </style>
@endsection

<div wire:ignore.self>
    @include('livewire.deraq.resultat.liste')
</div>

<script>
    // Enhanced notification handler with animation and duration
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: event.detail.type || 'success',
            icon: event.detail.icon || 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!',
            animation: true, // Add animation for better visibility
            animationIn: 'fadeIn',
            animationOut: 'fadeOut',
            delay: 3000 // Show for 3 seconds
        });
    });

    // Enhanced error notification handler
    window.addEventListener("show-error", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'danger',
            icon: 'fa fa-times me-1',
            message: event.detail.message || 'Une erreur est survenue!',
            animation: true, // Add animation for better visibility
            animationIn: 'fadeIn',
            animationOut: 'fadeOut',
            delay: 4000 // Show for 4 seconds (longer for errors)
        });
    });

    // Add loading overlay for long operations
    window.addEventListener("loading-overlay", event => {
        const action = event.detail.action || 'show';
        const target = event.detail.target || 'body';

        if (action === 'show') {
            One.helpers('overlay', {
                element: document.querySelector(target),
                text: event.detail.message || 'Chargement en cours...',
                spinner: 'fa fa-fw fa-spinner fa-spin'
            });
        } else {
            One.helpers('overlay', {
                element: document.querySelector(target),
                action: 'close'
            });
        }
    });
    
    // Scroll to results
    window.addEventListener("scroll-to-results", () => {
        setTimeout(() => {
            const resultsSection = document.querySelector('.block-header-default');
            if (resultsSection) {
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            }
        }, 100);
    });

    // Select2 initialization
    document.addEventListener('livewire:load', function () {
        initSelect2();

        // Reinitialize Select2 when Livewire refreshes the DOM
        Livewire.hook('message.processed', (message, component) => {
            // Add a small delay to ensure DOM is fully updated
            setTimeout(() => {
                initSelect2();
            }, 50);
        });

        // Reset event
        window.addEventListener('reset-select2', event => {
            resetSelect2();
        });

        // Prevent Livewire from updating while Select2 dropdown is open
        window.addEventListener('livewire:before-dom-update', () => {
            // Check if any Select2 dropdown is open
            if ($('.select2-dropdown').is(':visible')) {
                // Close all open dropdowns before DOM update
                $('.select2-container').select2('close');
            }
        });
    });

    function initSelect2() {
        // Parcours multi-select
        if (document.getElementById('parcour-select')) {
            $('#parcour-select').select2({
                placeholder: 'Sélectionnez les parcours',
                allowClear: true,
                closeOnSelect: false, // Empêche la fermeture après sélection
                width: '100%',
                dropdownCssClass: 'select2-dropdown-fixed' // Add custom class for styling
            }).on('change', function (e) {
                // Don't update Livewire immediately - wait for blur event
                // This prevents the dropdown from disappearing after selection
            }).on('blur', function (e) {
                // Only sync with Livewire when focus leaves the select element
                @this.set('newResults.parcour_id', $(this).val() || []);
            }).on('select2:close', function (e) {
                // Alternative approach - update when dropdown closes
                setTimeout(() => {
                    @this.set('newResults.parcour_id', $(this).val() || []);
                }, 100);
            });
        }

        // Semestre multi-select
        if (document.getElementById('semestre-select')) {
            $('#semestre-select').select2({
                placeholder: 'Sélectionnez les semestres',
                allowClear: true,
                closeOnSelect: false, // Empêche la fermeture après sélection
                width: '100%',
                dropdownCssClass: 'select2-dropdown-fixed' // Add custom class for styling
            }).on('change', function (e) {
                // Don't update Livewire immediately - wait for blur event
                // This prevents the dropdown from disappearing after selection
            }).on('blur', function (e) {
                // Only sync with Livewire when focus leaves the select element
                @this.set('newResults.semestre_id', $(this).val() || []);
            }).on('select2:close', function (e) {
                // Alternative approach - update when dropdown closes
                setTimeout(() => {
                    @this.set('newResults.semestre_id', $(this).val() || []);
                }, 100);
            });
        }
    }
    
    function resetSelect2() {
        // Reset all Select2 elements
        if ($('#parcour-select').length) {
            $('#parcour-select').val(null).trigger('change');
        }
        if ($('#semestre-select').length) {
            $('#semestre-select').val(null).trigger('change');
        }
    }
</script>