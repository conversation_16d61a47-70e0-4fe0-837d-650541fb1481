.cke_emoji {
	overflow-y: hidden;
	height: 100%;
}

.cke_emoji-suggestion_item {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-family: sans-serif, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, "Trebuchet MS", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

.cke_emoji-suggestion_item span {
	font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

.cke_emoji-panel {
	width: 310px;
	height: 300px;
	overflow: hidden;
}

.cke_emoji-inner_panel {
	width: 100%;
}

.cke_emoji-panel_block a {
	display: inline-block;
	width: 100%;
	padding-top: 2px;
}

.cke_emoji-inner_panel > h2 {
	font-size: 2em;
}

/* TOP NAVIGATION */
.cke_emoji-navigation_icons {
	display: none;
}
.cke_emoji-inner_panel > nav {
	width: 100%;
	height: 24px;
	margin-top: 10px;
	margin-bottom: 6px;
	padding-bottom: 4px;
	border-bottom: 1px solid #d1d1d1;
}
.cke_emoji-inner_panel > nav > ul {
	margin-left: 10px;
	margin-right: 10px;
	margin-top: 8px;
	padding: 0;
	list-style-type: none;
	height: 24px;
}

.cke_emoji-inner_panel > nav li {
	display: inline-block;
	width: 24px;
	height: auto;
	margin: 0 6px;
	text-align: center;
}

.cke_browser_ie .cke_emoji-inner_panel > nav li {
	height: 22px;
}

.cke_emoji-inner_panel li svg {
	opacity: 0.4;
	width: 80%;
}

.cke_emoji-inner_panel li span {
	opacity: 0.4;
}

.cke_emoji-inner_panel li:hover svg, .cke_emoji-inner_panel li:hover span{
	opacity: 1;
}

.cke_emoji-inner_panel .active {
	border-bottom: 5px solid rgba(44, 195, 255, 1);
}

.cke_emoji-navigation_item span {
	width: 21px;
	height: 21px;
	display: inline-block;
}

/* SEARCHBOX */
.cke_emoji-search {
	position: relative;
	height: 25px;
	display: block;
	border: 1px solid #d1d1d1;
	margin-left: 10px;
	margin-right: 10px;
}

.cke_emoji-search .cke_emoji-search_loupe {
	position: absolute;
	top: 6px;
	left: 6px;
	display: inline-block;
	width: 14px;
	height: 14px;
	opacity: 0.4;
}

.cke_rtl .cke_emoji-search .cke_emoji-search_loupe {
	left: auto;
	right: 6px;
}

.cke_emoji-search span {
    background-repeat: no-repeat;
    background-position: -60px -15px;
	background-size: 75px 30px;
}

.cke_emoji-search input {
	-webkit-appearance: none;
	border: none;
	width: 100%;
	height: 100%;
	padding-left: 25px;
	padding-right: 10px;
	margin-left: 0
}

.cke_rtl .cke_emoji-search input {
	padding-left: 10px;
	padding-right: 25px;
	margin-right: 0;
}

/* EMOJI */
.cke_emoji-outer_emoji_block {
	height: 180px;
	overflow-x: hidden;
	overflow-y: auto;
	margin-top: 5px;
	margin-left: 10px;
	margin-right: 10px;
	padding-left: 2px;
	padding-right: 2px;
}

.cke_emoji-outer_emoji_block h2 {
	font-size: 1.3em;
	font-weight: 600;
	margin: 5px 0 3px 0;
}

.cke_emoji-outer_emoji_block ul {
	margin: 0 0 15px 0;
	padding: 0;
	list-style-type: none;
}

.cke_emoji-item {
	font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	list-style-type: none;
	display: inline-table;
	width: 36px;
	height: 36px;
	font-size: 1.8em;
	text-align: center;
}

.cke_emoji-item:hover {
	border-radius: 10%;
	background-color: rgba(44, 195, 255, 0.2);
}

.cke_emoji-item > a {
	text-decoration: none;
	display: table-cell;
	vertical-align: middle;
}

.cke_emoji-outer_emoji_block .hidden {
	display: none
}

/* STATUS BAR */
.cke_emoji-status_bar {
	height: 34px;
	padding-left: 10px;
	padding-right: 10px;
	padding-top: 3px;
	margin-top: 3px;
	border-top: 1px solid #d1d1d1;
	line-height: 1;
}

.cke_emoji-status_bar p {
	margin-top: 3px;
}

.cke_emoji-status_bar > div {
	display: inline-block;
	margin-top: 3px;
}

.cke_emoji-status_icon {
	font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	font-size: 2.2em;
	float: left;
	margin-right: 10px;
}

.cke_rtl .cke_emoji-status_icon {
	float: right;
	margin-right: 0px;
	margin-left: 10px;
}

.cke_emoji-panel_block p {
	margin-bottom: 0;
}

p.cke_emoji-status_description {
	font-weight: 600;
}

p.cke_emoji-status_full_name {
	font-size: 0.8em;
	color: #d1d1d1;
}

.cke_emoji-inner_panel a:focus, .cke_emoji-inner_panel input:focus {
	outline: 2px solid #139FF7;
}
