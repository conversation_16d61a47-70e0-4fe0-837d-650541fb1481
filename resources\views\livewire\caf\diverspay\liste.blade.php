<div class="bg-body-light">
    <div class="content content-full">

        <h1 class="h3 fw-bold mb-2">
            Paiments divers
        </h1>

    </div>
</div>
<div class="content">
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="addPay()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire d'ajout</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">

                    <div class="col-6">
                        <label class="form-label" for="example-select">Libellé <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newPay.libelle"
                            class="form-control @error('newPay.libelle') is-invalid @enderror" id="example-text-input"
                            name="address" placeholder="Entre<PERSON> le montant">

                        @error('newPay.libelle')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>


                    <div class="col-6">
                        <label class="form-label" for="example-select">Moyen de paiment <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('newPay.moyen') is-invalid @enderror"
                            wire:model="newPay.moyen" id="example-select1" name="example-select1">
                            <option selected value="">Choisissez</option>
                            @foreach ($moyens as $moyen)
                                <option value="{{ $moyen->id }}">{{ $moyen->nom }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-select">Année universitaire <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('newPay.annee_universitaire_id') is-invalid @enderror"
                            wire:model="newPay.annee_universitaire_id" id="example-select1" name="example-select1">
                            <option selected value="">Choisissez</option>
                            @foreach ($annee as $anne)
                                <option value="{{ $anne->id }}">{{ $anne->nom }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-select">Type de paiement <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('newPay.type_id') is-invalid @enderror"
                            wire:model="newPay.type_id" id="example-select1" name="example-select1">
                            <option selected value="">Choisissez</option>
                                <option value="5">Impression</option>
                                <option value="6">Photocopie</option>
                                <option value="31">Décaissement</option>
                                <option value="32">Renflouement de caisse</option>
                                <option value="33">Retour à la caisse</option>
                        </select>
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-select">Code <span class="text-danger">*</span></label>
                        <input type="text" wire:model="newPay.code"
                            class="form-control @error('newPay.code') is-invalid @enderror" id="example-text-input"
                            name="example-text-input" placeholder="Entrez le code du reçu">

                        @error('newPay.code')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>



                    <div class="col-6">
                        <label class="form-label" for="example-select">Montant <span
                                class="text-danger">*</span></label>
                        <input type="number" wire:model="newPay.montant"
                            class="form-control @error('newPay.montant') is-invalid @enderror" id="example-text-input"
                            name="address" placeholder="Entrez le montant">

                        @error('newPay.montant')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>



                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
