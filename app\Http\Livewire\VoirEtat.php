<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Domaine;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\TypePayment;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\User;
use Livewire\Component;

class VoirEtat extends Component
{
    public $currentPage = PAGELIST;

    public $newEtus = [];

    public $etus = [];
    public $selectedTypePayments = []; // Pour stocker les types de paiement sélectionnés

    public Parcour $current_parcours;
    public Niveau $current_niveau;
    public $current_type_payments = []; // Tableau pour stocker les types de paiement actuels
    public AnneeUniversitaire $current_annee;
    
    // Pour l'état de recherche et la pagination
    public $search = '';
    public $perPage = 10;
    public $sortField = 'nom';
    public $sortDirection = 'asc';

    protected $queryString = ['search', 'perPage', 'sortField', 'sortDirection'];

    public function mount()
    {
        $this->newEtus['etat'] = 0;
        $this->newEtus['parcour_id'] = 0;
        $this->newEtus['niveau_id'] = 0;
        $this->newEtus['type_payment_id'] = [];
        $this->newEtus['annee_universitaire_id'] = 0;
        $this->newEtus['domaine_id'] = 0;
    }

    public function render()
    {
        return view('livewire.administration.voiretat.index', [
            "parcours" => Parcour::all(),
            "domaines" => Domaine::all(),
            "niveaux" => Niveau::all(),
            "types" => TypePayment::all(),
            "annees" => AnneeUniversitaire::orderBy('nom', 'desc')->get()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {
            return [
                'editTypePayment.nom' => 'required',
            ];
        }

        return [
            'newEtus.niveau_id' => 'required|not_in:0',
            'newEtus.etat' => 'required|not_in:0',
            'newEtus.parcour_id' => 'required',
            'newEtus.type_payment_id' => 'required|array|min:1',
            'newEtus.annee_universitaire_id' => 'required|not_in:0',
        ];
    }

    public function goToListResult()
    {
        $this->currentPage = PAGELIST;
        $this->reset(['newEtus', 'etus', 'selectedTypePayments']);
        $this->mount();
    }

    public function goToEtat()
    {
        $validationAttributes = $this->validate();
        
        // Stocker les types de paiement sélectionnés
        $this->selectedTypePayments = $validationAttributes["newEtus"]["type_payment_id"];
        
        $this->generate(
            $validationAttributes["newEtus"]["parcour_id"],
            $validationAttributes["newEtus"]["niveau_id"],
            $validationAttributes["newEtus"]["type_payment_id"],
            $validationAttributes["newEtus"]["annee_universitaire_id"],
            $validationAttributes["newEtus"]["etat"],
            $validationAttributes["newEtus"]["domaine_id"] ?? null
        );
    }

    public function generate($parcour_id, $niveau_id, $type_payment_ids, $annee_universitaire_id, $etat, $domaine_id = null)
    {
        // Récupérer les informations des modèles
        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);
        
        // Récupérer les types de paiement sélectionnés
        $this->current_type_payments = TypePayment::whereIn('id', $type_payment_ids)->get();

        $query = User::query();

        // Jointure avec le modèle Info
        $query->withWhereHas('info', function($q) use ($annee_universitaire_id, $niveau_id, $domaine_id) {
            $q->whereAnneeUniversitaireId($annee_universitaire_id)
              ->whereNiveauId($niveau_id);
              
            if ($domaine_id) {
                $q->whereDomaineId($domaine_id);
            }
        });

        // Filtrer par parcours si ce n'est pas "Tous les parcours"
        if ($parcour_id != 100) {
            $query->whereParcourId($parcour_id);
        }

        // Filtrer selon l'état de paiement (payé ou non payé)
        if ($etat == 1) { // Non payé
            $query->whereDoesntHave('historique', function($q) use ($type_payment_ids, $annee_universitaire_id) {
                $q->whereIn('type_payment_id', $type_payment_ids)
                  ->whereAnneeUniversitaireId($annee_universitaire_id);
            });
        } elseif ($etat == 2) { // Payé
            $query->whereHas('historique', function($q) use ($type_payment_ids, $annee_universitaire_id) {
                $q->whereIn('type_payment_id', $type_payment_ids)
                  ->whereAnneeUniversitaireId($annee_universitaire_id);
            });
        }

        // Appliquer la recherche si elle existe
        if ($this->search) {
            $query->where(function($q) {
                $q->where('nom', 'like', '%' . $this->search . '%')
                  ->orWhere('prenom', 'like', '%' . $this->search . '%')
                  ->orWhere('telephone1', 'like', '%' . $this->search . '%')
                  ->orWhere('telephone2', 'like', '%' . $this->search . '%');
            });
        }

        // Tri
        $query->orderBy($this->sortField, $this->sortDirection);

        // Récupérer les données
        $this->etus = $query->get(['id', 'nom', 'prenom', 'telephone1', 'telephone2']);
    }

    // Méthodes pour le tri et la recherche
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        
        // Réappliquer la recherche avec le nouveau tri si des étudiants sont déjà affichés
        if (!empty($this->etus)) {
            $this->goToEtat();
        }
    }

    public function updatedSearch()
    {
        // Réappliquer la recherche si des étudiants sont déjà affichés
        if (!empty($this->etus)) {
            $this->goToEtat();
        }
    }

    public function updatedPerPage()
    {
        // Réappliquer la recherche avec la nouvelle pagination si des étudiants sont déjà affichés
        if (!empty($this->etus)) {
            $this->goToEtat();
        }
    }

    // Export vers Excel/CSV
    public function exportToExcel()
    {
        // Code d'export à implémenter selon la bibliothèque que vous utilisez
        // Par exemple, avec Laravel Excel
        $this->dispatchBrowserEvent('showSuccessMessage', ['message' => 'Export en cours...']);
    }

    // Méthode d'exportation vers PDF
public function exportToPDF()
{
    // Récupérer les données nécessaires pour le PDF
    $etudiants = $this->etus;
    $current_niveau = $this->current_niveau ?? null;
    $current_annee = $this->current_annee ?? null;
    $current_type_payments = $this->current_type_payments ?? [];
    $etat = $this->newEtus['etat'] ?? 0;
    
    // Obtenir le parcours actuel si sélectionné
    $parcour_id = $this->newEtus['parcour_id'] ?? 0;
    $current_parcours = $parcour_id != 0 && $parcour_id != 100 ? Parcour::find($parcour_id) : null;

    // Générer un nom de fichier unique
    $fileName = 'etat_paiement_' . time() . '.pdf';
    
    // Générer le PDF
    $pdf = PDF::loadView('pdf.etat', [
        'etudiants' => $etudiants,
        'current_niveau' => $current_niveau,
        'current_annee' => $current_annee,
        'current_parcours' => $current_parcours,
        'current_type_payments' => $current_type_payments,
        'etat' => $etat
    ]);
    
    // Optimiser le PDF pour le web
    $pdf->setPaper('a4', 'portrait');
    $pdf->setOptions([
        'isHtml5ParserEnabled' => true,
        'isRemoteEnabled' => true,
        'defaultFont' => 'sans-serif'
    ]);
    
    // Télécharger le PDF
    return response()->streamDownload(
        fn () => print($pdf->output()),
        $fileName,
        ['Content-Type' => 'application/pdf']
    );
}
}