<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListUser()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListUser()">Liste d'utilisateur</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Modification d'utilisateur
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <div class="row">
        <!-- Basic -->
        <div class="col-md-8">
            <form method="POST" role="form" wire:submit.prevent="updateUser()" enctype="multipart/form-data">
                <div class="block block-rounded">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">Formulaire d'ajout</h3>
                    </div>
                    <div class="block-content">
                        <div class="row g-4">
                            <div class="col-6">
                                <label class="form-label" for="example-text-input">Nom <span
                                        class="text-danger">*</span></label>
                                <input type="text" wire:model="editUser.nom"
                                    class="form-control @error('editUser.nom') is-invalid @enderror"
                                    id="example-text-input" name="firstname" placeholder="Text Input">

                                @error('editUser.nom')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-6">
                                <label class="form-label" for="example-text-input">Prénom <span
                                        class="text-danger">*</span></label>
                                <input type="text" wire:model="editUser.prenom"
                                    class="form-control @error('editUser.prenom') is-invalid @enderror"
                                    id="example-text-input" name="lastname" placeholder="Text Input">

                                @error('editUser.prenom')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="col-6">
                                <label class="form-label" for="example-select">Sexe <span
                                        class="text-danger">*</span></label>
                                <select class="form-select @error('editUser.sexe') is-invalid @enderror"
                                    wire:model="editUser.sexe" id="example-select" name="example-select">
                                    <option value="">Open this select menu</option>
                                    <option value="H">Homme</option>
                                    <option value="F">Femme</option>
                                </select>

                                @error('editUser.sexe')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="col-6">
                                <label class="form-label" for="example-email-input">Email <span
                                        class="text-danger">*</span></label>
                                <input type="email" wire:model="editUser.email"
                                    class="form-control @error('editUser.email') is-invalid @enderror"
                                    id="example-email-input" name="example-email-input" placeholder="Email Input">

                                @error('editUser.email')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="col-6">
                                <label class="form-label" for="example-text-input">Telephone1 <span
                                        class="text-danger">*</span></label>
                                <input type="text" wire:model="editUser.telephone1"
                                    class="form-control @error('editUser.telephone1') is-invalid @enderror"
                                    id="example-text-input" name="example-text-input" placeholder="Text Input">

                                @error('editUser.telephone1')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>



{{--                             
                            @if ($isEtu)
                                <div class="col-6">
                                    <label class="form-label" for="example-select1">Parcours(pour les
                                        étudiants)</label>
                                    <select class="form-select @error('editUser.parcour_id') is-invalid @enderror"
                                        wire:model="editUser.parcour_id" id="example-select1" name="example-select1">
                                        <option value="null">Open this select menu</option>
                                        @foreach ($parcours as $parcour)
                                            <option value="{{ $parcour->id }}">{{ $parcour->nom }}</option>
                                        @endforeach
                                    </select>

                                    @error('editUser.parcour_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="col-6">
                                    <label class="form-label" for="example-select1">Niveau(pour les étudiants)</label>
                                    <select class="form-select @error('editUser.niveau_id') is-invalid @enderror"
                                        wire:model="editUser.niveau_id" id="example-select1" name="example-select1">
                                        <option value="null">Open this select menu</option>
                                        @foreach ($niveaux as $niveau)
                                            <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                        @endforeach
                                    </select>

                                    @error('editUser.niveau_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            @endif --}}



                            <div>
                                <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="col-md-4">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">Roles</h3>
                    {{-- <div class="block-options">
                    <button type="button" class="btn-block-option">
                        <i class="si si-settings"></i>
                    </button>
                </div> --}}
                </div>
                <div class="block-content">
                    @foreach ($rolePermissions['roles'] as $role)
                        <div class="space-y-2">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" value="" wire:model.lazy="rolePermissions.roles.{{$loop->index}}.active"
                                    id="example-switch-default{{$role['role_id']}}" name="example-switch-default1" @if($role["active"]) checked @endif>
                                <label class="form-check-label"
                                    for="example-switch-default{{$role['role_id']}}">{{ $role['role_nom'] }}</label>
                            </div>
                        </div>
                    @endforeach
                    <button class="btn btn-primary mt-3" wire:click="updateRoleAndPermissions"><i class="fas fa-check"></i> Appliquer</button>
                </div>
                <div class="block-content">
                                <label class="form-label" for="example-text-input">Mot de passe</label>
                                <input type="text" wire:model="newPasswd" class="form-control"
                                    id="example-text-input" name="example-text-input" placeholder="Text Input">
                            
                    <button class="btn btn-primary mt-3" wire:click="resetPassword"><i class="fas fa-check"></i> Appliquer</button>
                </div>
            </div>

        </div>
    </div>
</div>
