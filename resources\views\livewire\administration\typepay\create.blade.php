<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
            <h1 class="h3 fw-bold mb-1">
                <i class="fa fa-plus-circle me-2 text-primary"></i>Nouveau type de paiement
            </h1>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="javascript:void(0)">Administration</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a class="link-fx" wire:click.prevent="goToListPay()" href="javascript:void(0)">Types de paiements</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Nouveau
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                <i class="fa fa-file-plus me-1"></i> Informations du type de paiement
            </h3>
        </div>
        <div class="block-content">
            <form wire:submit.prevent="addPay" class="space-y-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label" for="nom">Nom du type de paiement <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('newPay.nom') is-invalid @enderror" 
                                id="nom" wire:model="newPay.nom" placeholder="Ex: Frais de scolarité">
                            @error('newPay.nom')
                                <div class="invalid-feedback animated fadeIn">{{ $message }}</div>
                            @enderror
                            <div class="form-text text-muted">
                                Nom unique qui identifie ce type de paiement
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label">Description (optionnelle)</label>
                            <textarea class="form-control" wire:model="newPay.description" 
                                    rows="2" placeholder="Description courte..."></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" 
                                    id="is_mandatory" wire:model="newPay.is_mandatory">
                                <label class="form-check-label" for="is_mandatory">
                                    Paiement obligatoire pour tous les étudiants
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info d-flex" role="alert">
                    <div class="flex-shrink-0">
                        <i class="fa fa-info-circle fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="mb-0">
                            <strong>Note importante :</strong> Après avoir créé le type de paiement, vous pourrez définir les tarifs par niveau et par année universitaire en modifiant ce type de paiement.
                        </p>
                    </div>
                </div>
                
                <div class="d-flex justify-content-end mb-4">
                    <button type="button" wire:click.prevent="goToListPay()" class="btn btn-alt-secondary me-2">
                        <i class="fa fa-arrow-left me-1"></i> Annuler
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-check me-1"></i> Créer le type de paiement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>