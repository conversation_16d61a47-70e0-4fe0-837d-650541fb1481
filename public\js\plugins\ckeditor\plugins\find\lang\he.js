CKEDITOR.plugins.setLang("find","he",{find:"חי<PERSON><PERSON><PERSON>",findOptions:"אפ<PERSON>רויות חיפוש",findWhat:"חיפוש מחרוזת:",matchCase:"הבחנה בין אותיות רשיות לקטנות (Case)",matchCyclic:"התאמה מחזורית",matchWord:"התאמה למילה המלאה",notFoundMsg:"הטקסט המבוקש לא נמצא.",replace:"החלפה",replaceAll:"החלפה בכל העמוד",replaceSuccessMsg:"%1 טקסטים הוחלפו.",replaceWith:"החל<PERSON>ה במחרוזת:",title:"חיפוש והחלפה"});