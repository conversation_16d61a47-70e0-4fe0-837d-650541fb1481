<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $items = [
            ["email"=>"tcadmin", "password" => Hash::make("Imsaa21?!"), "nom" => "IMSAA", "prenom" => "admin"],
            ["email"=>"teach", "password" => Hash::make("password"), "nom" => "Teach", "prenom" => "teach"],
        ];
    
        foreach ($items as $item) {
            User::create($item);
        }
    }
}
