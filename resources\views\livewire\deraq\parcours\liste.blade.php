 <!-- Hero -->
    <div class="bg-body-light">
        <div class="content content-full">
            
                    <h1 class="h3 fw-bold mb-2">
                        Gestion des Parcours
                    </h1>
            
        </div>
    </div>
    <!-- END Hero -->


    <!-- Page Content -->
    <div class="content">

        <!-- Dynamic Table Full -->
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    Liste des Parcours
                </h3>
                <div class="block-options">
                    <a class="btn btn-sm btn-primary me-1" wire:click.prevent="goToAddParcours()">
                        <i class="fa fa-plus me-1"></i> Nouveau Parcours
                    </a>
                </div>
            </div>
            <div class="block-content block-content-full">
                <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                <table class="table table-bordered table-striped table-vcenter">
                    <thead>
                        <tr>
                            <th class="text-center" style="width: 50px;">#</th>
                            <th>Sigle</th>
                            <th class="d-none d-sm-table-cell">Nom</th>
                            <th class="d-none d-sm-table-cell" style="width: 25%;">Domaine</th>
                            <th class="text-center" style="width: 100px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach ($parcours as $parcour)
                            
                                <tr>
                                    <td class="text-center">{{ $parcour->id }}</td>
                                    <td class="fw-semibold">
                                        {{ $parcour->sigle }}
                                    </td>
                                    <td class="text-muted d-none d-sm-table-cell">
                                        {{ $parcour->nom }}
                                    </td>
                                    <td class="text-muted d-none d-sm-table-cell">
                                        {{ $parcour->mention->nom }}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <button type="button" wire:click="goToEditParcours({{$parcour->id}})" class="btn btn-sm btn-alt-secondary"
                                                 title="Edit">
                                                <i class="fa fa-fw fa-pencil-alt"></i>
                                            </button>
                                            <button type="button" wire:click="deleteParcours({{$parcour->id}})" class="btn btn-sm btn-alt-secondary"
                                                 title="Delete">
                                                <i class="fa fa-fw fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                           
                        @endforeach



                    </tbody>
                </table>
            </div>
        </div>
        <!-- END Dynamic Table Full -->


    </div>
    <!-- END Page Content -->
