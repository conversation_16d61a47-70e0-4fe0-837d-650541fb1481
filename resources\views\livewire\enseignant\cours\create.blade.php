 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Cours
         </h1>

     </div>
 </div>
 <!-- END Hero -->


 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste de mes Cours
             </h3>
         </div>
         <div class="block-content block-content-full">
             <select class="form-select @error('noteType.type') is-invalid @enderror" wire:model="noteType.type"
                 id="example-select1" name="example-select1">
                 {{-- <option selected value="null">Open this select menu</option> --}}
                 @foreach ($typeNotes as $typeNote)
                     <option value="{{ $typeNote->id }}">{{ $typeNote->nom }}</option>
                 @endforeach
             </select>

             @error('noteType.type')
                 <span class="text-danger">{{ $message }}</span>
             @enderror
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter">
                 <thead>
                     <tr>
                         <th>Nom et Prenom</th>
                         <th class="text-center" style="width: 200px;">Note</th>
                     </tr>
                 </thead>
                 <tbody>
                    
                     @foreach ($noteEtus as $noteEtu)
                         <tr>
                             <td class="fw-semibold">
                                 {{ $noteEtu['nom'] }} {{ $noteEtu['prenom'] }}
                             </td>
                             <td class="text-muted">
                                 <input type="text" wire:model="noteEtus.{{ $loop->index }}.note"
                                     class="form-control @error('noteEtus.{{ $loop->index }}.note') is-invalid @enderror"
                                     id="example-text-input" name="firstname" placeholder="Note">

                                 @error('noteEtus.{{ $loop->index }}.note')
                                     <span class="text-danger">{{ $message }}</span>
                                 @enderror
                             </td>

                         </tr>
                     @endforeach



                 </tbody>
             </table>

             <button class="btn btn-primary mt-3" wire:click="addNote()"><i class="fas fa-check"></i> Appliquer</button>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
