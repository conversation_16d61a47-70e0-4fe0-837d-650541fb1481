@section('css')
    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="{{ asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css') }}">
@endsection

@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    
    <script src="{{ asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
@endsection



<div wire:ignore.self>


    @if ($currentPage == PAGECREATEFORM)
        @include('livewire.utilisateurs.create')
    @endif

    @if ($currentPage == PAGEEDITFORM)
        @include('livewire.utilisateurs.edit')
    @endif

    @if ($currentPage == PAGELIST)
        @include('livewire.utilisateurs.liste')
    @endif

</div>


<script>
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    })
</script>

<script>
    window.addEventListener("helperDatePicker", event => {
        One.helpersOnLoad(['jq-datepicker']);
    })
</script>