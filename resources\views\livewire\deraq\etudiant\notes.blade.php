@section('css')
<style>
    .note-form {
        background: #fff;
        border-radius: 0.75rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e3e6f0;
        transition: all 0.3s ease;
    }

    .note-form:hover {
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        transform: translateY(-2px);
    }

    .notes-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        border: none;
        padding: 1rem 0.75rem;
    }

    .notes-table {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }

    .back-button {
        transition: all 0.3s ease;
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
    }

    .back-button:hover {
        transform: translateX(-5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .note-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e3e6f0;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .note-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .note-score {
        font-size: 1.5rem;
        font-weight: 700;
        padding: 0.75rem 1rem;
        border-radius: 0.75rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 80px;
        position: relative;
        overflow: hidden;
    }

    .note-score::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 100%);
        pointer-events: none;
    }

    .note-excellent {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .note-good {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .note-average {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }

    .note-poor {
        background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        color: white;
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0%, 100% { transform: rotate(0deg); }
        50% { transform: rotate(180deg); }
    }

    .filter-chip {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 25px;
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .filter-chip:hover, .filter-chip.active {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    }

    .floating-add-btn {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
        z-index: 1000;
    }

    .floating-add-btn:hover {
        transform: scale(1.1) rotate(90deg);
        box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
    }

    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* Animations */
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .slide-in-right {
        animation: slideInRight 0.5s ease-out;
    }

    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .note-card {
            margin-bottom: 1rem;
        }

        .floating-add-btn {
            bottom: 1rem;
            right: 1rem;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .stats-card {
            margin-bottom: 1rem;
        }

        .note-score {
            font-size: 1.2rem;
            min-width: 60px;
            padding: 0.5rem 0.75rem;
        }
    }
</style>
@endsection

<div>
    <!-- Hero Section Modernisé -->
    <div class="bg-body-light mb-4">
        <div class="content content-full">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <button class="btn btn-alt-secondary back-button mb-3" wire:click="backToList">
                        <i class="fa fa-arrow-left me-2"></i> Retour à la liste
                    </button>
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar avatar-lg me-3">
                            <img src="{{ asset('media/avatars/avatar0.jpg') }}" alt="Avatar" class="rounded-circle">
                        </div>
                        <div>
                            <h1 class="h3 fw-bold mb-1">{{ $selectedStudentName }}</h1>
                            <div class="d-flex align-items-center text-muted">
                                <i class="fa fa-graduation-cap me-2"></i>
                                <span class="me-3">{{ $current_parcour?->sigle }} - {{ $current_niveau?->nom }}</span>
                                <i class="fa fa-calendar me-2"></i>
                                <span>{{ $current_annee?->nom }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    @if($noteActionMode === 'list')
                    <div class="d-flex gap-2 justify-content-end">
                        <button class="btn btn-outline-success" wire:click="generateTranscriptFromNotes" title="Générer le relevé de notes">
                            <i class="fa fa-file-pdf me-1"></i> Relevé PDF
                        </button>
                        <button class="btn btn-outline-primary" wire:click="exportNotes">
                            <i class="fa fa-download me-1"></i> Exporter
                        </button>
                        <button class="btn btn-primary" wire:click="showCreateNoteForm">
                            <i class="fa fa-plus me-1"></i> Ajouter une note
                        </button>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Statistiques rapides -->
            @if($noteActionMode === 'list' && count($notes) > 0)
            <div class="row g-3 mt-2">
                <div class="col-md-3">
                    <div class="stats-card fade-in">
                        <i class="fa fa-chart-line fa-2x mb-2 opacity-75"></i>
                        <h3 class="h4 mb-1">{{ number_format($this->calculateAverage(), 2) }}/20</h3>
                        <small>Moyenne générale</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card fade-in" style="animation-delay: 0.1s;">
                        <i class="fa fa-clipboard-list fa-2x mb-2 opacity-75"></i>
                        <h3 class="h4 mb-1">{{ count($notes) }}</h3>
                        <small>Notes enregistrées</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card fade-in" style="animation-delay: 0.2s;">
                        <i class="fa fa-trophy fa-2x mb-2 opacity-75"></i>
                        <h3 class="h4 mb-1">{{ $this->getBestNote() }}/20</h3>
                        <small>Meilleure note</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card fade-in" style="animation-delay: 0.3s;">
                        <i class="fa fa-chart-pie fa-2x mb-2 opacity-75"></i>
                        <h3 class="h4 mb-1">{{ $this->getSuccessRate() }}%</h3>
                        <small>Taux de réussite</small>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Filtres modernisés -->
    @if($noteActionMode === 'list')
    <div class="content mb-4">
        <div class="note-card">
            <div class="card-body p-3">
                <div class="row g-3 align-items-center">
                    <div class="col-md-3">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fa fa-filter me-1 text-primary"></i>Filtrer par semestre
                        </label>
                        <select class="form-select" wire:model="selectedSemestreId" wire:change="filterBySemestre">
                            <option value="">Tous les semestres</option>
                            @foreach($semestres as $sem)
                                <option value="{{ $sem->id }}">{{ $sem->nom }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fa fa-search me-1 text-primary"></i>Recherche
                        </label>
                        <input type="search" class="form-control" wire:model.debounce.300ms="searchQuery"
                               placeholder="Matière, type de note...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fa fa-sort me-1 text-primary"></i>Trier par
                        </label>
                        <select class="form-select" wire:model="sortBy" wire:change="applySorting">
                            <option value="created_at_desc">Plus récent</option>
                            <option value="created_at_asc">Plus ancien</option>
                            <option value="valeur_desc">Note décroissante</option>
                            <option value="valeur_asc">Note croissante</option>
                            <option value="matiere_asc">Matière A-Z</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fa fa-eye me-1 text-primary"></i>Affichage
                        </label>
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-primary {{ $viewMode === 'table' ? 'active' : '' }}"
                                    wire:click="$set('viewMode', 'table')" title="Vue tableau">
                                <i class="fa fa-table"></i>
                            </button>
                            <button type="button" class="btn btn-outline-primary {{ $viewMode === 'cards' ? 'active' : '' }}"
                                    wire:click="$set('viewMode', 'cards')" title="Vue cartes">
                                <i class="fa fa-th-large"></i>
                            </button>
                            <button type="button" class="btn btn-outline-primary {{ $viewMode === 'chart' ? 'active' : '' }}"
                                    wire:click="$set('viewMode', 'chart')" title="Vue graphique">
                                <i class="fa fa-chart-bar"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Filtres rapides par type de note -->
                @if(count($noteTypes) > 0)
                <div class="mt-3 pt-3 border-top">
                    <label class="form-label fw-semibold mb-2">
                        <i class="fa fa-tags me-1 text-primary"></i>Filtres rapides
                    </label>
                    <div class="d-flex flex-wrap gap-2">
                        <button class="filter-chip {{ empty($selectedNoteType) ? 'active' : '' }}"
                                wire:click="$set('selectedNoteType', '')">
                            Toutes les notes
                        </button>
                        @foreach($noteTypes as $type)
                            <button class="filter-chip {{ $selectedNoteType == $type->id ? 'active' : '' }}"
                                    wire:click="$set('selectedNoteType', {{ $type->id }})">
                                {{ $type->nom }}
                            </button>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    <div class="content">
        <div class="row">
            <!-- Le formulaire est maintenant dans le modal -->

            <!-- Affichage des notes modernisé -->
            <div class="col-12">
                    @if($viewMode === 'table')
                        <!-- Vue tableau modernisée -->
                        <div class="note-card fade-in">
                            <div class="card-header bg-transparent border-0 pb-0">
                                <h3 class="card-title fw-bold">
                                    <i class="fa fa-clipboard-list me-2 text-primary"></i>
                                    Liste des notes
                                    <span class="badge bg-primary ms-2">{{ count($notes) }}</span>
                                </h3>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover notes-table mb-0">
                                        <thead>
                                            <tr>
                                                <th>Matière</th>
                                                <th>Type</th>
                                                <th class="text-center">Note/20</th>
                                                <th>Observation</th>
                                                <th class="text-center" style="width: 120px;">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($notes as $index => $note)
                                                <tr class="fade-in" style="animation-delay: {{ $index * 0.05 }}s;">
                                                    <td class="fw-semibold">
                                                        <i class="fa fa-book me-2 text-muted"></i>
                                                        {{ $note->matiere->nom ?? '-' }}
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">{{ $note->typeNote->nom ?? '-' }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        @php
                                                            $noteClass = '';
                                                            if($note->valeur >= 16) $noteClass = 'note-excellent';
                                                            elseif($note->valeur >= 14) $noteClass = 'note-good';
                                                            elseif($note->valeur >= 10) $noteClass = 'note-average';
                                                            else $noteClass = 'note-poor';
                                                        @endphp
                                                        <span class="note-score {{ $noteClass }}">
                                                            {{ number_format($note->valeur, 2) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if($note->observation)
                                                            <span class="text-muted" title="{{ $note->observation }}">
                                                                <i class="fa fa-comment me-1"></i>
                                                                {{ Str::limit($note->observation, 30) }}
                                                            </span>
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td class="text-center">
                                                        <div class="btn-group">
                                                            <button class="btn btn-sm btn-outline-primary"
                                                                wire:click="showEditNoteForm({{ $note->id }})"
                                                                title="Modifier">
                                                                <i class="fa fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger"
                                                                wire:click="deleteNote({{ $note->id }})"
                                                                onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette note ?')"
                                                                title="Supprimer">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center py-5">
                                                        <div class="empty-state">
                                                            <i class="fa fa-clipboard-list"></i>
                                                            <h5 class="mt-3 mb-2">Aucune note enregistrée</h5>
                                                            <p class="text-muted mb-3">Commencez par ajouter une première note pour cet étudiant.</p>
                                                            <button class="btn btn-primary" wire:click="showCreateNoteForm">
                                                                <i class="fa fa-plus me-1"></i> Ajouter une note
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @elseif($viewMode === 'cards')
                        <!-- Vue cartes -->
                        <div class="row g-3">
                            @forelse($notes as $index => $note)
                                <div class="col-md-6 col-lg-4">
                                    <div class="note-card fade-in" style="animation-delay: {{ $index * 0.1 }}s;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div>
                                                    <h6 class="card-title fw-bold mb-1">
                                                        <i class="fa fa-book me-1 text-primary"></i>
                                                        {{ $note->matiere->nom ?? '-' }}
                                                    </h6>
                                                    <span class="badge bg-info">{{ $note->typeNote->nom ?? '-' }}</span>
                                                </div>
                                                @php
                                                    $noteClass = '';
                                                    if($note->valeur >= 16) $noteClass = 'note-excellent';
                                                    elseif($note->valeur >= 14) $noteClass = 'note-good';
                                                    elseif($note->valeur >= 10) $noteClass = 'note-average';
                                                    else $noteClass = 'note-poor';
                                                @endphp
                                                <div class="note-score {{ $noteClass }}">
                                                    {{ number_format($note->valeur, 2) }}
                                                </div>
                                            </div>

                                            @if($note->observation)
                                                <div class="mb-3">
                                                    <small class="text-muted d-block mb-1">
                                                        <i class="fa fa-comment me-1"></i>Observation
                                                    </small>
                                                    <p class="text-muted mb-0">{{ $note->observation }}</p>
                                                </div>
                                            @endif

                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fa fa-calendar me-1"></i>
                                                    {{ $note->created_at->format('d/m/Y') }}
                                                </small>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-outline-primary"
                                                        wire:click="showEditNoteForm({{ $note->id }})"
                                                        title="Modifier">
                                                        <i class="fa fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                        wire:click="deleteNote({{ $note->id }})"
                                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette note ?')"
                                                        title="Supprimer">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="col-12">
                                    <div class="note-card">
                                        <div class="card-body text-center py-5">
                                            <div class="empty-state">
                                                <i class="fa fa-clipboard-list"></i>
                                                <h5 class="mt-3 mb-2">Aucune note enregistrée</h5>
                                                <p class="text-muted mb-3">Commencez par ajouter une première note pour cet étudiant.</p>
                                                <button class="btn btn-primary" wire:click="showCreateNoteForm">
                                                    <i class="fa fa-plus me-1"></i> Ajouter une note
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    @elseif($viewMode === 'chart')
                        <!-- Vue graphique -->
                        @include('components.notes-charts')
                    @endif
            </div>
        </div>
    </div>

    <!-- Bouton flottant pour ajouter une note -->
    <button class="floating-add-btn" wire:click="showCreateNoteForm" title="Ajouter une note">
        <i class="fa fa-plus"></i>
    </button>

    <!-- Inclure le modal de formulaire -->
    @include('components.note-form-modal')

    <!-- Inclure les composants de notifications -->
    @include('components.toast-notifications')
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les graphiques si on est en mode chart
    @if($viewMode === 'chart' && count($notes) > 0)
        const notesData = @json($notes);
        if (typeof updateNotesCharts === 'function') {
            updateNotesCharts(notesData);
        }
    @endif

    // Raccourcis clavier pour la gestion des notes
    document.addEventListener('keydown', function(e) {
        // Ctrl+N pour nouvelle note
        if (e.ctrlKey && e.key === 'n' && !e.target.matches('input, textarea, select')) {
            e.preventDefault();
            @this.call('showCreateNoteForm');
        }

        // Ctrl+E pour export
        if (e.ctrlKey && e.key === 'e' && !e.target.matches('input, textarea, select')) {
            e.preventDefault();
            @this.call('exportNotes');
        }

        // Ctrl+F pour focus sur la recherche
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('input[wire\\:model\\.debounce\\.300ms="searchQuery"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });

    // Auto-refresh des graphiques quand les données changent
    Livewire.on('notesUpdated', function() {
        setTimeout(function() {
            if (typeof updateNotesCharts === 'function' && @this.viewMode === 'chart') {
                const notesData = @this.notes;
                updateNotesCharts(notesData);
            }
        }, 100);
    });

    // Le modal est maintenant géré entièrement par Livewire
    // Plus besoin de gestion Bootstrap Modal

    // Sauvegarde automatique des préférences
    Livewire.on('viewModeChanged', function(mode) {
        localStorage.setItem('notes_view_mode', mode);
    });

    // Restaurer les préférences
    const savedViewMode = localStorage.getItem('notes_view_mode');
    if (savedViewMode && savedViewMode !== @this.viewMode) {
        @this.set('viewMode', savedViewMode);
    }
});

// Gestion des notifications spécifiques aux notes - maintenant utilise les événements standards
// Les événements showSuccessMessage et showErrorMessage sont déjà gérés par le système global
    showToast('error', event.detail.message || 'Erreur lors de l\'enregistrement de la note');
});

window.addEventListener('showNoteInfo', event => {
    showToast('info', event.detail.message);
});

// Fonction pour la confirmation de suppression
window.addEventListener('confirmDelete', event => {
    if (confirm(event.detail.message)) {
        @this.call('deleteNoteConfirmed');
    }
});

// Gestion du statut de sauvegarde
window.addEventListener('clearSaveStatus', event => {
    setTimeout(function() {
        const statusElement = document.getElementById('saveStatus');
        if (statusElement) {
            statusElement.style.display = 'none';
        }
    }, 2000);
});

// Animation d'entrée pour les nouvelles notes
function animateNewNote() {
    const newNotes = document.querySelectorAll('.note-card:not(.animated)');
    newNotes.forEach((note, index) => {
        note.classList.add('animated');
        note.style.animationDelay = (index * 0.1) + 's';
        note.classList.add('fade-in');
    });
}

// Observer pour détecter les nouvelles notes
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            animateNewNote();
        }
    });
});

// Démarrer l'observation
const notesContainer = document.querySelector('.notes-charts-container, .table-responsive');
if (notesContainer) {
    observer.observe(notesContainer, {
        childList: true,
        subtree: true
    });
}
</script>