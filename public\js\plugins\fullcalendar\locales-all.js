[].push.apply(FullCalendar.globalLocales, function () {
  'use strict';

  var l0 = {
    code: "af",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Vorige",
      next: "Volgende",
      today: "Vandag",
      year: "Jaar",
      month: "Maand",
      week: "Week",
      day: "Dag",
      list: "Agenda"
    },
    allDayText: "He<PERSON>ag",
    moreLinkText: "Addisionele",
    noEventsText: "Daar is geen gebeurtenisse nie"
  };

  var l1 = {
    code: "ar-dz",
    week: {
      dow: 0,
      doy: 4
    },
    direction: "rtl",
    buttonText: {
      prev: "\u0627\u0644\u0633\u0627\u0628\u0642",
      next: "\u0627\u0644\u062A\u0627\u0644\u064A",
      today: "\u0627\u0644\u064A\u0648\u0645",
      month: "\u0634\u0647\u0631",
      week: "\u0623\u0633\u0628\u0648\u0639",
      day: "\u064A\u0648\u0645",
      list: "\u0623\u062C\u0646\u062F\u0629"
    },
    weekText: "\u0623\u0633\u0628\u0648\u0639",
    allDayText: "\u0627\u0644\u064A\u0648\u0645 \u0643\u0644\u0647",
    moreLinkText: "\u0623\u062E\u0631\u0649",
    noEventsText: "\u0623\u064A \u0623\u062D\u062F\u0627\u062B \u0644\u0639\u0631\u0636"
  };

  var l2 = {
    code: "ar-kw",
    week: {
      dow: 0,
      doy: 12
    },
    direction: "rtl",
    buttonText: {
      prev: "\u0627\u0644\u0633\u0627\u0628\u0642",
      next: "\u0627\u0644\u062A\u0627\u0644\u064A",
      today: "\u0627\u0644\u064A\u0648\u0645",
      month: "\u0634\u0647\u0631",
      week: "\u0623\u0633\u0628\u0648\u0639",
      day: "\u064A\u0648\u0645",
      list: "\u0623\u062C\u0646\u062F\u0629"
    },
    weekText: "\u0623\u0633\u0628\u0648\u0639",
    allDayText: "\u0627\u0644\u064A\u0648\u0645 \u0643\u0644\u0647",
    moreLinkText: "\u0623\u062E\u0631\u0649",
    noEventsText: "\u0623\u064A \u0623\u062D\u062F\u0627\u062B \u0644\u0639\u0631\u0636"
  };

  var l3 = {
    code: "ar-ly",
    week: {
      dow: 6,
      doy: 12
    },
    direction: "rtl",
    buttonText: {
      prev: "\u0627\u0644\u0633\u0627\u0628\u0642",
      next: "\u0627\u0644\u062A\u0627\u0644\u064A",
      today: "\u0627\u0644\u064A\u0648\u0645",
      month: "\u0634\u0647\u0631",
      week: "\u0623\u0633\u0628\u0648\u0639",
      day: "\u064A\u0648\u0645",
      list: "\u0623\u062C\u0646\u062F\u0629"
    },
    weekText: "\u0623\u0633\u0628\u0648\u0639",
    allDayText: "\u0627\u0644\u064A\u0648\u0645 \u0643\u0644\u0647",
    moreLinkText: "\u0623\u062E\u0631\u0649",
    noEventsText: "\u0623\u064A \u0623\u062D\u062F\u0627\u062B \u0644\u0639\u0631\u0636"
  };

  var l4 = {
    code: "ar-ma",
    week: {
      dow: 6,
      doy: 12
    },
    direction: "rtl",
    buttonText: {
      prev: "\u0627\u0644\u0633\u0627\u0628\u0642",
      next: "\u0627\u0644\u062A\u0627\u0644\u064A",
      today: "\u0627\u0644\u064A\u0648\u0645",
      month: "\u0634\u0647\u0631",
      week: "\u0623\u0633\u0628\u0648\u0639",
      day: "\u064A\u0648\u0645",
      list: "\u0623\u062C\u0646\u062F\u0629"
    },
    weekText: "\u0623\u0633\u0628\u0648\u0639",
    allDayText: "\u0627\u0644\u064A\u0648\u0645 \u0643\u0644\u0647",
    moreLinkText: "\u0623\u062E\u0631\u0649",
    noEventsText: "\u0623\u064A \u0623\u062D\u062F\u0627\u062B \u0644\u0639\u0631\u0636"
  };

  var l5 = {
    code: "ar-sa",
    week: {
      dow: 0,
      doy: 6
    },
    direction: "rtl",
    buttonText: {
      prev: "\u0627\u0644\u0633\u0627\u0628\u0642",
      next: "\u0627\u0644\u062A\u0627\u0644\u064A",
      today: "\u0627\u0644\u064A\u0648\u0645",
      month: "\u0634\u0647\u0631",
      week: "\u0623\u0633\u0628\u0648\u0639",
      day: "\u064A\u0648\u0645",
      list: "\u0623\u062C\u0646\u062F\u0629"
    },
    weekText: "\u0623\u0633\u0628\u0648\u0639",
    allDayText: "\u0627\u0644\u064A\u0648\u0645 \u0643\u0644\u0647",
    moreLinkText: "\u0623\u062E\u0631\u0649",
    noEventsText: "\u0623\u064A \u0623\u062D\u062F\u0627\u062B \u0644\u0639\u0631\u0636"
  };

  var l6 = {
    code: "ar-tn",
    week: {
      dow: 1,
      doy: 4
    },
    direction: "rtl",
    buttonText: {
      prev: "\u0627\u0644\u0633\u0627\u0628\u0642",
      next: "\u0627\u0644\u062A\u0627\u0644\u064A",
      today: "\u0627\u0644\u064A\u0648\u0645",
      month: "\u0634\u0647\u0631",
      week: "\u0623\u0633\u0628\u0648\u0639",
      day: "\u064A\u0648\u0645",
      list: "\u0623\u062C\u0646\u062F\u0629"
    },
    weekText: "\u0623\u0633\u0628\u0648\u0639",
    allDayText: "\u0627\u0644\u064A\u0648\u0645 \u0643\u0644\u0647",
    moreLinkText: "\u0623\u062E\u0631\u0649",
    noEventsText: "\u0623\u064A \u0623\u062D\u062F\u0627\u062B \u0644\u0639\u0631\u0636"
  };

  var l7 = {
    code: "ar",
    week: {
      dow: 6,
      doy: 12
    },
    direction: "rtl",
    buttonText: {
      prev: "\u0627\u0644\u0633\u0627\u0628\u0642",
      next: "\u0627\u0644\u062A\u0627\u0644\u064A",
      today: "\u0627\u0644\u064A\u0648\u0645",
      month: "\u0634\u0647\u0631",
      week: "\u0623\u0633\u0628\u0648\u0639",
      day: "\u064A\u0648\u0645",
      list: "\u0623\u062C\u0646\u062F\u0629"
    },
    weekText: "\u0623\u0633\u0628\u0648\u0639",
    allDayText: "\u0627\u0644\u064A\u0648\u0645 \u0643\u0644\u0647",
    moreLinkText: "\u0623\u062E\u0631\u0649",
    noEventsText: "\u0623\u064A \u0623\u062D\u062F\u0627\u062B \u0644\u0639\u0631\u0636"
  };

  var l8 = {
    code: "az",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u018Fvv\u0259l",
      next: "Sonra",
      today: "Bu G\xFCn",
      month: "Ay",
      week: "H\u0259ft\u0259",
      day: "G\xFCn",
      list: "G\xFCnd\u0259m"
    },
    weekText: "H\u0259ft\u0259",
    allDayText: "B\xFCt\xFCn G\xFCn",
    moreLinkText: function(n) {
      return "+ daha \xE7ox " + n;
    },
    noEventsText: "G\xF6st\u0259rm\u0259k \xFC\xE7\xFCn hadis\u0259 yoxdur"
  };

  var l9 = {
    code: "bg",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "\u043D\u0430\u0437\u0430\u0434",
      next: "\u043D\u0430\u043F\u0440\u0435\u0434",
      today: "\u0434\u043D\u0435\u0441",
      month: "\u041C\u0435\u0441\u0435\u0446",
      week: "\u0421\u0435\u0434\u043C\u0438\u0446\u0430",
      day: "\u0414\u0435\u043D",
      list: "\u0413\u0440\u0430\u0444\u0438\u043A"
    },
    allDayText: "\u0426\u044F\u043B \u0434\u0435\u043D",
    moreLinkText: function(n) {
      return "+\u043E\u0449\u0435 " + n;
    },
    noEventsText: "\u041D\u044F\u043C\u0430 \u0441\u044A\u0431\u0438\u0442\u0438\u044F \u0437\u0430 \u043F\u043E\u043A\u0430\u0437\u0432\u0430\u043D\u0435"
  };

  var l10 = {
    code: "bn",
    week: {
      dow: 0,
      doy: 6
    },
    buttonText: {
      prev: "\u09AA\u09C7\u099B\u09A8\u09C7",
      next: "\u09B8\u09BE\u09AE\u09A8\u09C7",
      today: "\u0986\u099C",
      month: "\u09AE\u09BE\u09B8",
      week: "\u09B8\u09AA\u09CD\u09A4\u09BE\u09B9",
      day: "\u09A6\u09BF\u09A8",
      list: "\u09A4\u09BE\u09B2\u09BF\u0995\u09BE"
    },
    weekText: "\u09B8\u09AA\u09CD\u09A4\u09BE\u09B9",
    allDayText: "\u09B8\u09BE\u09B0\u09BE\u09A6\u09BF\u09A8",
    moreLinkText: function(n) {
      return "+\u0985\u09A8\u09CD\u09AF\u09BE\u09A8\u09CD\u09AF " + n;
    },
    noEventsText: "\u0995\u09CB\u09A8\u09CB \u0987\u09AD\u09C7\u09A8\u09CD\u099F \u09A8\u09C7\u0987"
  };

  var l11 = {
    code: "bs",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "Pro\u0161li",
      next: "Sljede\u0107i",
      today: "Danas",
      month: "Mjesec",
      week: "Sedmica",
      day: "Dan",
      list: "Raspored"
    },
    weekText: "Sed",
    allDayText: "Cijeli dan",
    moreLinkText: function(n) {
      return "+ jo\u0161 " + n;
    },
    noEventsText: "Nema doga\u0111aja za prikazivanje"
  };

  var l12 = {
    code: "ca",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Anterior",
      next: "Seg\xFCent",
      today: "Avui",
      month: "Mes",
      week: "Setmana",
      day: "Dia",
      list: "Agenda"
    },
    weekText: "Set",
    allDayText: "Tot el dia",
    moreLinkText: "m\xE9s",
    noEventsText: "No hi ha esdeveniments per mostrar"
  };

  var l13 = {
    code: "cs",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "D\u0159\xEDve",
      next: "Pozd\u011Bji",
      today: "Nyn\xED",
      month: "M\u011Bs\xEDc",
      week: "T\xFDden",
      day: "Den",
      list: "Agenda"
    },
    weekText: "T\xFDd",
    allDayText: "Cel\xFD den",
    moreLinkText: function(n) {
      return "+dal\u0161\xED: " + n;
    },
    noEventsText: "\u017D\xE1dn\xE9 akce k zobrazen\xED"
  };

  var l14 = {
    code: "cy",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Blaenorol",
      next: "Nesaf",
      today: "Heddiw",
      year: "Blwyddyn",
      month: "Mis",
      week: "Wythnos",
      day: "Dydd",
      list: "Rhestr"
    },
    weekText: "Wythnos",
    allDayText: "Trwy'r dydd",
    moreLinkText: "Mwy",
    noEventsText: "Dim digwyddiadau"
  };

  var l15 = {
    code: "da",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Forrige",
      next: "N\xE6ste",
      today: "I dag",
      month: "M\xE5ned",
      week: "Uge",
      day: "Dag",
      list: "Agenda"
    },
    weekText: "Uge",
    allDayText: "Hele dagen",
    moreLinkText: "flere",
    noEventsText: "Ingen arrangementer at vise"
  };

  function affix$1(buttonText) {
    return buttonText === "Tag" || buttonText === "Monat" ? "r" : buttonText === "Jahr" ? "s" : "";
  }
  var l16 = {
    code: "de-at",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Zur\xFCck",
      next: "Vor",
      today: "Heute",
      year: "Jahr",
      month: "Monat",
      week: "Woche",
      day: "Tag",
      list: "Termin\xFCbersicht"
    },
    weekText: "KW",
    weekTextLong: "Woche",
    allDayText: "Ganzt\xE4gig",
    moreLinkText: function(n) {
      return "+ weitere " + n;
    },
    noEventsText: "Keine Ereignisse anzuzeigen",
    buttonHints: {
      prev: function(buttonText) {
        return "Vorherige".concat(affix$1(buttonText), " ").concat(buttonText);
      },
      next: function(buttonText) {
        return "N\xE4chste".concat(affix$1(buttonText), " ").concat(buttonText);
      },
      today: function(buttonText) {
        if (buttonText === "Tag") {
          return "Heute";
        }
        return "Diese".concat(affix$1(buttonText), " ").concat(buttonText);
      }
    },
    viewHint: function(buttonText) {
      var glue = buttonText === "Woche" ? "n" : buttonText === "Monat" ? "s" : "es";
      return buttonText + glue + "ansicht";
    },
    navLinkHint: "Gehe zu $0",
    moreLinkHint: function(eventCnt) {
      return "Zeige " + (eventCnt === 1 ? "ein weiteres Ereignis" : eventCnt + " weitere Ereignisse");
    },
    closeHint: "Schlie\xDFen",
    timeHint: "Uhrzeit",
    eventHint: "Ereignis"
  };

  function affix(buttonText) {
    return buttonText === "Tag" || buttonText === "Monat" ? "r" : buttonText === "Jahr" ? "s" : "";
  }
  var l17 = {
    code: "de",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Zur\xFCck",
      next: "Vor",
      today: "Heute",
      year: "Jahr",
      month: "Monat",
      week: "Woche",
      day: "Tag",
      list: "Termin\xFCbersicht"
    },
    weekText: "KW",
    weekTextLong: "Woche",
    allDayText: "Ganzt\xE4gig",
    moreLinkText: function(n) {
      return "+ weitere " + n;
    },
    noEventsText: "Keine Ereignisse anzuzeigen",
    buttonHints: {
      prev: function(buttonText) {
        return "Vorherige".concat(affix(buttonText), " ").concat(buttonText);
      },
      next: function(buttonText) {
        return "N\xE4chste".concat(affix(buttonText), " ").concat(buttonText);
      },
      today: function(buttonText) {
        if (buttonText === "Tag") {
          return "Heute";
        }
        return "Diese".concat(affix(buttonText), " ").concat(buttonText);
      }
    },
    viewHint: function(buttonText) {
      var glue = buttonText === "Woche" ? "n" : buttonText === "Monat" ? "s" : "es";
      return buttonText + glue + "ansicht";
    },
    navLinkHint: "Gehe zu $0",
    moreLinkHint: function(eventCnt) {
      return "Zeige " + (eventCnt === 1 ? "ein weiteres Ereignis" : eventCnt + " weitere Ereignisse");
    },
    closeHint: "Schlie\xDFen",
    timeHint: "Uhrzeit",
    eventHint: "Ereignis"
  };

  var l18 = {
    code: "el",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u03A0\u03C1\u03BF\u03B7\u03B3\u03BF\u03CD\u03BC\u03B5\u03BD\u03BF\u03C2",
      next: "\u0395\u03C0\u03CC\u03BC\u03B5\u03BD\u03BF\u03C2",
      today: "\u03A3\u03AE\u03BC\u03B5\u03C1\u03B1",
      month: "\u039C\u03AE\u03BD\u03B1\u03C2",
      week: "\u0395\u03B2\u03B4\u03BF\u03BC\u03AC\u03B4\u03B1",
      day: "\u0397\u03BC\u03AD\u03C1\u03B1",
      list: "\u0391\u03C4\u03B6\u03AD\u03BD\u03C4\u03B1"
    },
    weekText: "\u0395\u03B2\u03B4",
    allDayText: "\u039F\u03BB\u03BF\u03AE\u03BC\u03B5\u03C1\u03BF",
    moreLinkText: "\u03C0\u03B5\u03C1\u03B9\u03C3\u03C3\u03CC\u03C4\u03B5\u03C1\u03B1",
    noEventsText: "\u0394\u03B5\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03C5\u03BD \u03B3\u03B5\u03B3\u03BF\u03BD\u03CC\u03C4\u03B1 \u03C0\u03C1\u03BF\u03C2 \u03B5\u03BC\u03C6\u03AC\u03BD\u03B9\u03C3\u03B7"
  };

  var l19 = {
    code: "en-au",
    week: {
      dow: 1,
      doy: 4
    },
    buttonHints: {
      prev: "Previous $0",
      next: "Next $0",
      today: "This $0"
    },
    viewHint: "$0 view",
    navLinkHint: "Go to $0",
    moreLinkHint: function(eventCnt) {
      return "Show ".concat(eventCnt, " more event").concat(eventCnt === 1 ? "" : "s");
    }
  };

  var l20 = {
    code: "en-gb",
    week: {
      dow: 1,
      doy: 4
    },
    buttonHints: {
      prev: "Previous $0",
      next: "Next $0",
      today: "This $0"
    },
    viewHint: "$0 view",
    navLinkHint: "Go to $0",
    moreLinkHint: function(eventCnt) {
      return "Show ".concat(eventCnt, " more event").concat(eventCnt === 1 ? "" : "s");
    }
  };

  var l21 = {
    code: "en-nz",
    week: {
      dow: 1,
      doy: 4
    },
    buttonHints: {
      prev: "Previous $0",
      next: "Next $0",
      today: "This $0"
    },
    viewHint: "$0 view",
    navLinkHint: "Go to $0",
    moreLinkHint: function(eventCnt) {
      return "Show ".concat(eventCnt, " more event").concat(eventCnt === 1 ? "" : "s");
    }
  };

  var l22 = {
    code: "eo",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Anta\u016Da",
      next: "Sekva",
      today: "Hodia\u016D",
      month: "Monato",
      week: "Semajno",
      day: "Tago",
      list: "Tagordo"
    },
    weekText: "Sm",
    allDayText: "Tuta tago",
    moreLinkText: "pli",
    noEventsText: "Neniuj eventoj por montri"
  };

  var l23 = {
    code: "es",
    week: {
      dow: 0,
      doy: 6
    },
    buttonText: {
      prev: "Ant",
      next: "Sig",
      today: "Hoy",
      month: "Mes",
      week: "Semana",
      day: "D\xEDa",
      list: "Agenda"
    },
    weekText: "Sm",
    allDayText: "Todo el d\xEDa",
    moreLinkText: "m\xE1s",
    noEventsText: "No hay eventos para mostrar"
  };

  var l24 = {
    code: "es",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Ant",
      next: "Sig",
      today: "Hoy",
      month: "Mes",
      week: "Semana",
      day: "D\xEDa",
      list: "Agenda"
    },
    buttonHints: {
      prev: "$0 antes",
      next: "$0 siguiente",
      today: function(buttonText) {
        return buttonText === "D\xEDa" ? "Hoy" : (buttonText === "Semana" ? "Esta" : "Este") + " " + buttonText.toLocaleLowerCase();
      }
    },
    viewHint: function(buttonText) {
      return "Vista " + (buttonText === "Semana" ? "de la" : "del") + " " + buttonText.toLocaleLowerCase();
    },
    weekText: "Sm",
    weekTextLong: "Semana",
    allDayText: "Todo el d\xEDa",
    moreLinkText: "m\xE1s",
    moreLinkHint: function(eventCnt) {
      return "Mostrar ".concat(eventCnt, " eventos m\xE1s");
    },
    noEventsText: "No hay eventos para mostrar",
    navLinkHint: "Ir al $0",
    closeHint: "Cerrar",
    timeHint: "La hora",
    eventHint: "Evento"
  };

  var l25 = {
    code: "et",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Eelnev",
      next: "J\xE4rgnev",
      today: "T\xE4na",
      month: "Kuu",
      week: "N\xE4dal",
      day: "P\xE4ev",
      list: "P\xE4evakord"
    },
    weekText: "n\xE4d",
    allDayText: "Kogu p\xE4ev",
    moreLinkText: function(n) {
      return "+ veel " + n;
    },
    noEventsText: "Kuvamiseks puuduvad s\xFCndmused"
  };

  var l26 = {
    code: "eu",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "Aur",
      next: "Hur",
      today: "Gaur",
      month: "Hilabetea",
      week: "Astea",
      day: "Eguna",
      list: "Agenda"
    },
    weekText: "As",
    allDayText: "Egun osoa",
    moreLinkText: "gehiago",
    noEventsText: "Ez dago ekitaldirik erakusteko"
  };

  var l27 = {
    code: "fa",
    week: {
      dow: 6,
      doy: 12
    },
    direction: "rtl",
    buttonText: {
      prev: "\u0642\u0628\u0644\u06CC",
      next: "\u0628\u0639\u062F\u06CC",
      today: "\u0627\u0645\u0631\u0648\u0632",
      month: "\u0645\u0627\u0647",
      week: "\u0647\u0641\u062A\u0647",
      day: "\u0631\u0648\u0632",
      list: "\u0628\u0631\u0646\u0627\u0645\u0647"
    },
    weekText: "\u0647\u0641",
    allDayText: "\u062A\u0645\u0627\u0645 \u0631\u0648\u0632",
    moreLinkText: function(n) {
      return "\u0628\u06CC\u0634 \u0627\u0632 " + n;
    },
    noEventsText: "\u0647\u06CC\u0686 \u0631\u0648\u06CC\u062F\u0627\u062F\u06CC \u0628\u0647 \u0646\u0645\u0627\u06CC\u0634"
  };

  var l28 = {
    code: "fi",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Edellinen",
      next: "Seuraava",
      today: "T\xE4n\xE4\xE4n",
      month: "Kuukausi",
      week: "Viikko",
      day: "P\xE4iv\xE4",
      list: "Tapahtumat"
    },
    weekText: "Vk",
    allDayText: "Koko p\xE4iv\xE4",
    moreLinkText: "lis\xE4\xE4",
    noEventsText: "Ei n\xE4ytett\xE4vi\xE4 tapahtumia"
  };

  var l29 = {
    code: "fr",
    buttonText: {
      prev: "Pr\xE9c\xE9dent",
      next: "Suivant",
      today: "Aujourd'hui",
      year: "Ann\xE9e",
      month: "Mois",
      week: "Semaine",
      day: "Jour",
      list: "Mon planning"
    },
    weekText: "Sem.",
    allDayText: "Toute la journ\xE9e",
    moreLinkText: "en plus",
    noEventsText: "Aucun \xE9v\xE9nement \xE0 afficher"
  };

  var l30 = {
    code: "fr-ch",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Pr\xE9c\xE9dent",
      next: "Suivant",
      today: "Courant",
      year: "Ann\xE9e",
      month: "Mois",
      week: "Semaine",
      day: "Jour",
      list: "Mon planning"
    },
    weekText: "Sm",
    allDayText: "Toute la journ\xE9e",
    moreLinkText: "en plus",
    noEventsText: "Aucun \xE9v\xE9nement \xE0 afficher"
  };

  var l31 = {
    code: "fr",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Pr\xE9c\xE9dent",
      next: "Suivant",
      today: "Aujourd'hui",
      year: "Ann\xE9e",
      month: "Mois",
      week: "Semaine",
      day: "Jour",
      list: "Planning"
    },
    weekText: "Sem.",
    allDayText: "Toute la journ\xE9e",
    moreLinkText: "en plus",
    noEventsText: "Aucun \xE9v\xE9nement \xE0 afficher"
  };

  var l32 = {
    code: "gl",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Ant",
      next: "Seg",
      today: "Hoxe",
      month: "Mes",
      week: "Semana",
      day: "D\xEDa",
      list: "Axenda"
    },
    weekText: "Sm",
    allDayText: "Todo o d\xEDa",
    moreLinkText: "m\xE1is",
    noEventsText: "Non hai eventos para amosar"
  };

  var l33 = {
    code: "he",
    direction: "rtl",
    buttonText: {
      prev: "\u05D4\u05E7\u05D5\u05D3\u05DD",
      next: "\u05D4\u05D1\u05D0",
      today: "\u05D4\u05D9\u05D5\u05DD",
      month: "\u05D7\u05D5\u05D3\u05E9",
      week: "\u05E9\u05D1\u05D5\u05E2",
      day: "\u05D9\u05D5\u05DD",
      list: "\u05E1\u05D3\u05E8 \u05D9\u05D5\u05DD"
    },
    allDayText: "\u05DB\u05DC \u05D4\u05D9\u05D5\u05DD",
    moreLinkText: "\u05D0\u05D7\u05E8",
    noEventsText: "\u05D0\u05D9\u05DF \u05D0\u05D9\u05E8\u05D5\u05E2\u05D9\u05DD \u05DC\u05D4\u05E6\u05D2\u05D4",
    weekText: "\u05E9\u05D1\u05D5\u05E2"
  };

  var l34 = {
    code: "hi",
    week: {
      dow: 0,
      doy: 6
    },
    buttonText: {
      prev: "\u092A\u093F\u091B\u0932\u093E",
      next: "\u0905\u0917\u0932\u093E",
      today: "\u0906\u091C",
      month: "\u092E\u0939\u0940\u0928\u093E",
      week: "\u0938\u092A\u094D\u0924\u093E\u0939",
      day: "\u0926\u093F\u0928",
      list: "\u0915\u093E\u0930\u094D\u092F\u0938\u0942\u091A\u0940"
    },
    weekText: "\u0939\u092B\u094D\u0924\u093E",
    allDayText: "\u0938\u092D\u0940 \u0926\u093F\u0928",
    moreLinkText: function(n) {
      return "+\u0905\u0927\u093F\u0915 " + n;
    },
    noEventsText: "\u0915\u094B\u0908 \u0918\u091F\u0928\u093E\u0913\u0902 \u0915\u094B \u092A\u094D\u0930\u0926\u0930\u094D\u0936\u093F\u0924 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F"
  };

  var l35 = {
    code: "hr",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "Prija\u0161nji",
      next: "Sljede\u0107i",
      today: "Danas",
      month: "Mjesec",
      week: "Tjedan",
      day: "Dan",
      list: "Raspored"
    },
    weekText: "Tje",
    allDayText: "Cijeli dan",
    moreLinkText: function(n) {
      return "+ jo\u0161 " + n;
    },
    noEventsText: "Nema doga\u0111aja za prikaz"
  };

  var l36 = {
    code: "hu",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "vissza",
      next: "el\u0151re",
      today: "ma",
      month: "H\xF3nap",
      week: "H\xE9t",
      day: "Nap",
      list: "Lista"
    },
    weekText: "H\xE9t",
    allDayText: "Eg\xE9sz nap",
    moreLinkText: "tov\xE1bbi",
    noEventsText: "Nincs megjelen\xEDthet\u0151 esem\xE9ny"
  };

  var l37 = {
    code: "hy-am",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u0546\u0561\u056D\u0578\u0580\u0564",
      next: "\u0540\u0561\u057B\u0578\u0580\u0564",
      today: "\u0531\u0575\u057D\u0585\u0580",
      month: "\u0531\u0574\u056B\u057D",
      week: "\u0547\u0561\u0562\u0561\u0569",
      day: "\u0555\u0580",
      list: "\u0555\u0580\u057E\u0561 \u0581\u0578\u0582\u0581\u0561\u056F"
    },
    weekText: "\u0547\u0561\u0562",
    allDayText: "\u0531\u0574\u0562\u0578\u0572\u057B \u0585\u0580",
    moreLinkText: function(n) {
      return "+ \u0587\u057D " + n;
    },
    noEventsText: "\u0532\u0561\u0581\u0561\u056F\u0561\u0575\u0578\u0582\u0574 \u0567 \u056B\u0580\u0561\u0564\u0561\u0580\u0571\u0578\u0582\u0569\u0575\u0578\u0582\u0576\u0568 \u0581\u0578\u0582\u0581\u0561\u0564\u0580\u0565\u056C\u0578\u0582"
  };

  var l38 = {
    code: "id",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "mundur",
      next: "maju",
      today: "hari ini",
      month: "Bulan",
      week: "Minggu",
      day: "Hari",
      list: "Agenda"
    },
    weekText: "Mg",
    allDayText: "Sehari penuh",
    moreLinkText: "lebih",
    noEventsText: "Tidak ada acara untuk ditampilkan"
  };

  var l39 = {
    code: "is",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Fyrri",
      next: "N\xE6sti",
      today: "\xCD dag",
      month: "M\xE1nu\xF0ur",
      week: "Vika",
      day: "Dagur",
      list: "Dagskr\xE1"
    },
    weekText: "Vika",
    allDayText: "Allan daginn",
    moreLinkText: "meira",
    noEventsText: "Engir vi\xF0bur\xF0ir til a\xF0 s\xFDna"
  };

  var l40 = {
    code: "it",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Prec",
      next: "Succ",
      today: "Oggi",
      month: "Mese",
      week: "Settimana",
      day: "Giorno",
      list: "Agenda"
    },
    weekText: "Sm",
    allDayText: "Tutto il giorno",
    moreLinkText: function(n) {
      return "+altri " + n;
    },
    noEventsText: "Non ci sono eventi da visualizzare"
  };

  var l41 = {
    code: "ja",
    buttonText: {
      prev: "\u524D",
      next: "\u6B21",
      today: "\u4ECA\u65E5",
      month: "\u6708",
      week: "\u9031",
      day: "\u65E5",
      list: "\u4E88\u5B9A\u30EA\u30B9\u30C8"
    },
    weekText: "\u9031",
    allDayText: "\u7D42\u65E5",
    moreLinkText: function(n) {
      return "\u4ED6 " + n + " \u4EF6";
    },
    noEventsText: "\u8868\u793A\u3059\u308B\u4E88\u5B9A\u306F\u3042\u308A\u307E\u305B\u3093"
  };

  var l42 = {
    code: "ka",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "\u10EC\u10D8\u10DC\u10D0",
      next: "\u10E8\u10D4\u10DB\u10D3\u10D4\u10D2\u10D8",
      today: "\u10D3\u10E6\u10D4\u10E1",
      month: "\u10D7\u10D5\u10D4",
      week: "\u10D9\u10D5\u10D8\u10E0\u10D0",
      day: "\u10D3\u10E6\u10D4",
      list: "\u10D3\u10E6\u10D8\u10E1 \u10EC\u10D4\u10E1\u10E0\u10D8\u10D2\u10D8"
    },
    weekText: "\u10D9\u10D5",
    allDayText: "\u10DB\u10D7\u10D4\u10DA\u10D8 \u10D3\u10E6\u10D4",
    moreLinkText: function(n) {
      return "+ \u10D9\u10D8\u10D3\u10D4\u10D5 " + n;
    },
    noEventsText: "\u10E6\u10DD\u10DC\u10D8\u10E1\u10EB\u10D8\u10D4\u10D1\u10D4\u10D1\u10D8 \u10D0\u10E0 \u10D0\u10E0\u10D8\u10E1"
  };

  var l43 = {
    code: "kk",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "\u0410\u043B\u0434\u044B\u04A3\u0493\u044B",
      next: "\u041A\u0435\u043B\u0435\u0441\u0456",
      today: "\u0411\u04AF\u0433\u0456\u043D",
      month: "\u0410\u0439",
      week: "\u0410\u043F\u0442\u0430",
      day: "\u041A\u04AF\u043D",
      list: "\u041A\u04AF\u043D \u0442\u04D9\u0440\u0442\u0456\u0431\u0456"
    },
    weekText: "\u041D\u0435",
    allDayText: "\u041A\u04AF\u043D\u0456 \u0431\u043E\u0439\u044B",
    moreLinkText: function(n) {
      return "+ \u0442\u0430\u0493\u044B " + n;
    },
    noEventsText: "\u041A\u04E9\u0440\u0441\u0435\u0442\u0443 \u04AF\u0448\u0456\u043D \u043E\u049B\u0438\u0493\u0430\u043B\u0430\u0440 \u0436\u043E\u049B"
  };

  var l44 = {
    code: "km",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u1798\u17BB\u1793",
      next: "\u1794\u1793\u17D2\u1791\u17B6\u1794\u17CB",
      today: "\u1790\u17D2\u1784\u17C3\u1793\u17C1\u17C7",
      year: "\u1786\u17D2\u1793\u17B6\u17C6",
      month: "\u1781\u17C2",
      week: "\u179F\u1794\u17D2\u178F\u17B6\u17A0\u17CD",
      day: "\u1790\u17D2\u1784\u17C3",
      list: "\u1794\u1789\u17D2\u1787\u17B8"
    },
    weekText: "\u179F\u1794\u17D2\u178F\u17B6\u17A0\u17CD",
    allDayText: "\u1796\u17C1\u1789\u1798\u17BD\u1799\u1790\u17D2\u1784\u17C3",
    moreLinkText: "\u1785\u17D2\u179A\u17BE\u1793\u1791\u17C0\u178F",
    noEventsText: "\u1782\u17D2\u1798\u17B6\u1793\u1796\u17D2\u179A\u17B9\u178F\u17D2\u178F\u17B7\u1780\u17B6\u179A\u178E\u17CD\u178F\u17D2\u179A\u17BC\u179C\u1794\u1784\u17D2\u17A0\u17B6\u1789"
  };

  var l45 = {
    code: "ko",
    buttonText: {
      prev: "\uC774\uC804\uB2EC",
      next: "\uB2E4\uC74C\uB2EC",
      today: "\uC624\uB298",
      month: "\uC6D4",
      week: "\uC8FC",
      day: "\uC77C",
      list: "\uC77C\uC815\uBAA9\uB85D"
    },
    weekText: "\uC8FC",
    allDayText: "\uC885\uC77C",
    moreLinkText: "\uAC1C",
    noEventsText: "\uC77C\uC815\uC774 \uC5C6\uC2B5\uB2C8\uB2E4"
  };

  var l46 = {
    code: "ku",
    week: {
      dow: 6,
      doy: 12
    },
    direction: "rtl",
    buttonText: {
      prev: "\u067E\u06CE\u0634\u062A\u0631",
      next: "\u062F\u0648\u0627\u062A\u0631",
      today: "\u0626\u06D5\u0645\u0695\u0648",
      month: "\u0645\u0627\u0646\u06AF",
      week: "\u0647\u06D5\u0641\u062A\u06D5",
      day: "\u0695\u06C6\u0698",
      list: "\u0628\u06D5\u0631\u0646\u0627\u0645\u06D5"
    },
    weekText: "\u0647\u06D5\u0641\u062A\u06D5",
    allDayText: "\u0647\u06D5\u0645\u0648\u0648 \u0695\u06C6\u0698\u06D5\u06A9\u06D5",
    moreLinkText: "\u0632\u06CC\u0627\u062A\u0631",
    noEventsText: "\u0647\u06CC\u0686 \u0695\u0648\u0648\u062F\u0627\u0648\u06CE\u0643 \u0646\u06CC\u06D5"
  };

  var l47 = {
    code: "lb",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Zr\xE9ck",
      next: "Weider",
      today: "Haut",
      month: "Mount",
      week: "Woch",
      day: "Dag",
      list: "Terminiwwersiicht"
    },
    weekText: "W",
    allDayText: "Ganzen Dag",
    moreLinkText: "m\xE9i",
    noEventsText: "Nee Evenementer ze affich\xE9ieren"
  };

  var l48 = {
    code: "lt",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Atgal",
      next: "Pirmyn",
      today: "\u0160iandien",
      month: "M\u0117nuo",
      week: "Savait\u0117",
      day: "Diena",
      list: "Darbotvark\u0117"
    },
    weekText: "SAV",
    allDayText: "Vis\u0105 dien\u0105",
    moreLinkText: "daugiau",
    noEventsText: "N\u0117ra \u012Fvyki\u0173 rodyti"
  };

  var l49 = {
    code: "lv",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Iepr.",
      next: "N\u0101k.",
      today: "\u0160odien",
      month: "M\u0113nesis",
      week: "Ned\u0113\u013Ca",
      day: "Diena",
      list: "Dienas k\u0101rt\u012Bba"
    },
    weekText: "Ned.",
    allDayText: "Visu dienu",
    moreLinkText: function(n) {
      return "+v\u0113l " + n;
    },
    noEventsText: "Nav notikumu"
  };

  var l50 = {
    code: "mk",
    buttonText: {
      prev: "\u043F\u0440\u0435\u0442\u0445\u043E\u0434\u043D\u043E",
      next: "\u0441\u043B\u0435\u0434\u043D\u043E",
      today: "\u0414\u0435\u043D\u0435\u0441",
      month: "\u041C\u0435\u0441\u0435\u0446",
      week: "\u041D\u0435\u0434\u0435\u043B\u0430",
      day: "\u0414\u0435\u043D",
      list: "\u0413\u0440\u0430\u0444\u0438\u043A"
    },
    weekText: "\u0421\u0435\u0434",
    allDayText: "\u0426\u0435\u043B \u0434\u0435\u043D",
    moreLinkText: function(n) {
      return "+\u043F\u043E\u0432\u0435\u045C\u0435 " + n;
    },
    noEventsText: "\u041D\u0435\u043C\u0430 \u043D\u0430\u0441\u0442\u0430\u043D\u0438 \u0437\u0430 \u043F\u0440\u0438\u043A\u0430\u0436\u0443\u0432\u0430\u045A\u0435"
  };

  var l51 = {
    code: "ms",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "Sebelum",
      next: "Selepas",
      today: "hari ini",
      month: "Bulan",
      week: "Minggu",
      day: "Hari",
      list: "Agenda"
    },
    weekText: "Mg",
    allDayText: "Sepanjang hari",
    moreLinkText: function(n) {
      return "masih ada " + n + " acara";
    },
    noEventsText: "Tiada peristiwa untuk dipaparkan"
  };

  var l52 = {
    code: "nb",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Forrige",
      next: "Neste",
      today: "I dag",
      month: "M\xE5ned",
      week: "Uke",
      day: "Dag",
      list: "Agenda"
    },
    weekText: "Uke",
    weekTextLong: "Uke",
    allDayText: "Hele dagen",
    moreLinkText: "til",
    noEventsText: "Ingen hendelser \xE5 vise",
    buttonHints: {
      prev: "Forrige $0",
      next: "Neste $0",
      today: "N\xE5v\xE6rende $0"
    },
    viewHint: "$0 visning",
    navLinkHint: "G\xE5 til $0",
    moreLinkHint: function(eventCnt) {
      return "Vis ".concat(eventCnt, " flere hendelse").concat(eventCnt === 1 ? "" : "r");
    }
  };

  var l53 = {
    code: "ne",
    week: {
      dow: 7,
      doy: 1
    },
    buttonText: {
      prev: "\u0905\u0918\u093F\u0932\u094D\u0932\u094B",
      next: "\u0905\u0930\u094D\u0915\u094B",
      today: "\u0906\u091C",
      month: "\u092E\u0939\u093F\u0928\u093E",
      week: "\u0939\u092A\u094D\u0924\u093E",
      day: "\u0926\u093F\u0928",
      list: "\u0938\u0942\u091A\u0940"
    },
    weekText: "\u0939\u092A\u094D\u0924\u093E",
    allDayText: "\u0926\u093F\u0928\u092D\u0930\u093F",
    moreLinkText: "\u0925\u092A \u0932\u093F\u0902\u0915",
    noEventsText: "\u0926\u0947\u0916\u093E\u0909\u0928\u0915\u094B \u0932\u093E\u0917\u093F \u0915\u0941\u0928\u0948 \u0918\u091F\u0928\u093E\u0939\u0930\u0942 \u091B\u0948\u0928\u0928\u094D"
  };

  var l54 = {
    code: "nl",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Vorige",
      next: "Volgende",
      today: "Vandaag",
      year: "Jaar",
      month: "Maand",
      week: "Week",
      day: "Dag",
      list: "Agenda"
    },
    allDayText: "Hele dag",
    moreLinkText: "extra",
    noEventsText: "Geen evenementen om te laten zien"
  };

  var l55 = {
    code: "nn",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "F\xF8rre",
      next: "Neste",
      today: "I dag",
      month: "M\xE5nad",
      week: "Veke",
      day: "Dag",
      list: "Agenda"
    },
    weekText: "Veke",
    allDayText: "Heile dagen",
    moreLinkText: "til",
    noEventsText: "Ingen hendelser \xE5 vise"
  };

  var l56 = {
    code: "pl",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Poprzedni",
      next: "Nast\u0119pny",
      today: "Dzi\u015B",
      month: "Miesi\u0105c",
      week: "Tydzie\u0144",
      day: "Dzie\u0144",
      list: "Plan dnia"
    },
    weekText: "Tydz",
    allDayText: "Ca\u0142y dzie\u0144",
    moreLinkText: "wi\u0119cej",
    noEventsText: "Brak wydarze\u0144 do wy\u015Bwietlenia"
  };

  var l57 = {
    code: "pt-br",
    buttonText: {
      prev: "Anterior",
      next: "Pr\xF3ximo",
      today: "Hoje",
      month: "M\xEAs",
      week: "Semana",
      day: "Dia",
      list: "Lista"
    },
    weekText: "Sm",
    allDayText: "dia inteiro",
    moreLinkText: function(n) {
      return "mais +" + n;
    },
    noEventsText: "N\xE3o h\xE1 eventos para mostrar"
  };

  var l58 = {
    code: "pt",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Anterior",
      next: "Seguinte",
      today: "Hoje",
      month: "M\xEAs",
      week: "Semana",
      day: "Dia",
      list: "Agenda"
    },
    weekText: "Sem",
    allDayText: "Todo o dia",
    moreLinkText: "mais",
    noEventsText: "N\xE3o h\xE1 eventos para mostrar"
  };

  var l59 = {
    code: "ro",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "precedent\u0103",
      next: "urm\u0103toare",
      today: "Azi",
      month: "Lun\u0103",
      week: "S\u0103pt\u0103m\xE2n\u0103",
      day: "Zi",
      list: "Agend\u0103"
    },
    weekText: "S\u0103pt",
    allDayText: "Toat\u0103 ziua",
    moreLinkText: function(n) {
      return "+alte " + n;
    },
    noEventsText: "Nu exist\u0103 evenimente de afi\u0219at"
  };

  var l60 = {
    code: "ru",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u041F\u0440\u0435\u0434",
      next: "\u0421\u043B\u0435\u0434",
      today: "\u0421\u0435\u0433\u043E\u0434\u043D\u044F",
      month: "\u041C\u0435\u0441\u044F\u0446",
      week: "\u041D\u0435\u0434\u0435\u043B\u044F",
      day: "\u0414\u0435\u043D\u044C",
      list: "\u041F\u043E\u0432\u0435\u0441\u0442\u043A\u0430 \u0434\u043D\u044F"
    },
    weekText: "\u041D\u0435\u0434",
    allDayText: "\u0412\u0435\u0441\u044C \u0434\u0435\u043D\u044C",
    moreLinkText: function(n) {
      return "+ \u0435\u0449\u0451 " + n;
    },
    noEventsText: "\u041D\u0435\u0442 \u0441\u043E\u0431\u044B\u0442\u0438\u0439 \u0434\u043B\u044F \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F"
  };

  var l61 = {
    code: "si-lk",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u0DB4\u0DD9\u0DBB",
      next: "\u0DB4\u0DC3\u0DD4",
      today: "\u0D85\u0DAF",
      month: "\u0DB8\u0DCF\u0DC3\u0DBA",
      week: "\u0DC3\u0DAD\u0DD2\u0DBA",
      day: "\u0DAF\u0DC0\u0DC3",
      list: "\u0DBD\u0DD0\u0DBA\u0DD2\u0DC3\u0DCA\u0DAD\u0DD4\u0DC0"
    },
    weekText: "\u0DC3\u0DAD\u0DD2",
    allDayText: "\u0DC3\u0DD2\u0DBA\u0DBD\u0DD4",
    moreLinkText: "\u0DAD\u0DC0\u0DAD\u0DCA",
    noEventsText: "\u0DB8\u0DD4\u0D9A\u0DD4\u0DAD\u0DCA \u0DB1\u0DD0\u0DAD"
  };

  var l62 = {
    code: "sk",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Predch\xE1dzaj\xFAci",
      next: "Nasleduj\xFAci",
      today: "Dnes",
      month: "Mesiac",
      week: "T\xFD\u017Ede\u0148",
      day: "De\u0148",
      list: "Rozvrh"
    },
    weekText: "Ty",
    allDayText: "Cel\xFD de\u0148",
    moreLinkText: function(n) {
      return "+\u010Fal\u0161ie: " + n;
    },
    noEventsText: "\u017Diadne akcie na zobrazenie"
  };

  var l63 = {
    code: "sl",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "Prej\u0161nji",
      next: "Naslednji",
      today: "Trenutni",
      month: "Mesec",
      week: "Teden",
      day: "Dan",
      list: "Dnevni red"
    },
    weekText: "Teden",
    allDayText: "Ves dan",
    moreLinkText: "ve\u010D",
    noEventsText: "Ni dogodkov za prikaz"
  };

  var l64 = {
    code: "sm",
    buttonText: {
      prev: "Talu ai",
      next: "Mulimuli atu",
      today: "Aso nei",
      month: "Masina",
      week: "Vaiaso",
      day: "Aso",
      list: "Faasologa"
    },
    weekText: "Vaiaso",
    allDayText: "Aso atoa",
    moreLinkText: "sili atu",
    noEventsText: "Leai ni mea na tutupu"
  };

  var l65 = {
    code: "sq",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "mbrapa",
      next: "P\xEBrpara",
      today: "sot",
      month: "Muaj",
      week: "Jav\xEB",
      day: "Dit\xEB",
      list: "List\xEB"
    },
    weekText: "Ja",
    allDayText: "Gjith\xEB dit\xEBn",
    moreLinkText: function(n) {
      return "+m\xEB tep\xEBr " + n;
    },
    noEventsText: "Nuk ka evente p\xEBr t\xEB shfaqur"
  };

  var l66 = {
    code: "sr-cyrl",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "\u041F\u0440\u0435\u0442\u0445\u043E\u0434\u043D\u0430",
      next: "\u0441\u043B\u0435\u0434\u0435\u045B\u0438",
      today: "\u0414\u0430\u043D\u0430\u0441",
      month: "\u041C\u0435\u0441\u0435\u0446",
      week: "\u041D\u0435\u0434\u0435\u0459\u0430",
      day: "\u0414\u0430\u043D",
      list: "\u041F\u043B\u0430\u043D\u0435\u0440"
    },
    weekText: "\u0421\u0435\u0434",
    allDayText: "\u0426\u0435\u043E \u0434\u0430\u043D",
    moreLinkText: function(n) {
      return "+ \u0458\u043E\u0448 " + n;
    },
    noEventsText: "\u041D\u0435\u043C\u0430 \u0434\u043E\u0433\u0430\u0452\u0430\u0458\u0430 \u0437\u0430 \u043F\u0440\u0438\u043A\u0430\u0437"
  };

  var l67 = {
    code: "sr",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "Prethodna",
      next: "Sledec\u0301i",
      today: "Danas",
      month: "M\u0435s\u0435c",
      week: "N\u0435d\u0435lja",
      day: "Dan",
      list: "Plan\u0435r"
    },
    weekText: "Sed",
    allDayText: "C\u0435o dan",
    moreLinkText: function(n) {
      return "+ jo\u0161 " + n;
    },
    noEventsText: "N\u0435ma doga\u0111aja za prikaz"
  };

  var l68 = {
    code: "sv",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "F\xF6rra",
      next: "N\xE4sta",
      today: "Idag",
      month: "M\xE5nad",
      week: "Vecka",
      day: "Dag",
      list: "Program"
    },
    buttonHints: {
      prev: function(buttonText) {
        return "F\xF6reg\xE5ende ".concat(buttonText.toLocaleLowerCase());
      },
      next: function(buttonText) {
        return "N\xE4sta ".concat(buttonText.toLocaleLowerCase());
      },
      today: function(buttonText) {
        return (buttonText === "Program" ? "Detta" : "Denna") + " " + buttonText.toLocaleLowerCase();
      }
    },
    viewHint: "$0 vy",
    navLinkHint: "G\xE5 till $0",
    moreLinkHint: function(eventCnt) {
      return "Visa ytterligare ".concat(eventCnt, " h\xE4ndelse").concat(eventCnt === 1 ? "" : "r");
    },
    weekText: "v.",
    weekTextLong: "Vecka",
    allDayText: "Heldag",
    moreLinkText: "till",
    noEventsText: "Inga h\xE4ndelser att visa",
    closeHint: "St\xE4ng",
    timeHint: "Klockan",
    eventHint: "H\xE4ndelse"
  };

  var l69 = {
    code: "ta-in",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u0BAE\u0BC1\u0BA8\u0BCD\u0BA4\u0BC8\u0BAF",
      next: "\u0B85\u0B9F\u0BC1\u0BA4\u0BCD\u0BA4\u0BA4\u0BC1",
      today: "\u0B87\u0BA9\u0BCD\u0BB1\u0BC1",
      month: "\u0BAE\u0BBE\u0BA4\u0BAE\u0BCD",
      week: "\u0BB5\u0BBE\u0BB0\u0BAE\u0BCD",
      day: "\u0BA8\u0BBE\u0BB3\u0BCD",
      list: "\u0BA4\u0BBF\u0BA9\u0B9A\u0BB0\u0BBF \u0B85\u0B9F\u0BCD\u0B9F\u0BB5\u0BA3\u0BC8"
    },
    weekText: "\u0BB5\u0BBE\u0BB0\u0BAE\u0BCD",
    allDayText: "\u0BA8\u0BBE\u0BB3\u0BCD \u0BAE\u0BC1\u0BB4\u0BC1\u0BB5\u0BA4\u0BC1\u0BAE\u0BCD",
    moreLinkText: function(n) {
      return "+ \u0BAE\u0BC7\u0BB2\u0BC1\u0BAE\u0BCD " + n;
    },
    noEventsText: "\u0B95\u0BBE\u0BA3\u0BCD\u0BAA\u0BBF\u0B95\u0BCD\u0B95 \u0BA8\u0BBF\u0B95\u0BB4\u0BCD\u0BB5\u0BC1\u0B95\u0BB3\u0BCD \u0B87\u0BB2\u0BCD\u0BB2\u0BC8"
  };

  var l70 = {
    code: "th",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",
      next: "\u0E16\u0E31\u0E14\u0E44\u0E1B",
      prevYear: "\u0E1B\u0E35\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",
      nextYear: "\u0E1B\u0E35\u0E16\u0E31\u0E14\u0E44\u0E1B",
      year: "\u0E1B\u0E35",
      today: "\u0E27\u0E31\u0E19\u0E19\u0E35\u0E49",
      month: "\u0E40\u0E14\u0E37\u0E2D\u0E19",
      week: "\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C",
      day: "\u0E27\u0E31\u0E19",
      list: "\u0E01\u0E33\u0E2B\u0E19\u0E14\u0E01\u0E32\u0E23"
    },
    weekText: "\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C",
    allDayText: "\u0E15\u0E25\u0E2D\u0E14\u0E27\u0E31\u0E19",
    moreLinkText: "\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E40\u0E15\u0E34\u0E21",
    noEventsText: "\u0E44\u0E21\u0E48\u0E21\u0E35\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21\u0E17\u0E35\u0E48\u0E08\u0E30\u0E41\u0E2A\u0E14\u0E07"
  };

  var l71 = {
    code: "tr",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "geri",
      next: "ileri",
      today: "bug\xFCn",
      month: "Ay",
      week: "Hafta",
      day: "G\xFCn",
      list: "Ajanda"
    },
    weekText: "Hf",
    allDayText: "T\xFCm g\xFCn",
    moreLinkText: "daha fazla",
    noEventsText: "G\xF6sterilecek etkinlik yok"
  };

  var l72 = {
    code: "ug",
    buttonText: {
      month: "\u0626\u0627\u064A",
      week: "\u06BE\u06D5\u067E\u062A\u06D5",
      day: "\u0643\u06C8\u0646",
      list: "\u0643\u06C8\u0646\u062A\u06D5\u0631\u062A\u0649\u067E"
    },
    allDayText: "\u067E\u06C8\u062A\u06C8\u0646 \u0643\u06C8\u0646"
  };

  var l73 = {
    code: "uk",
    week: {
      dow: 1,
      doy: 7
    },
    buttonText: {
      prev: "\u041F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u0456\u0439",
      next: "\u0434\u0430\u043B\u0456",
      today: "\u0421\u044C\u043E\u0433\u043E\u0434\u043D\u0456",
      month: "\u041C\u0456\u0441\u044F\u0446\u044C",
      week: "\u0422\u0438\u0436\u0434\u0435\u043D\u044C",
      day: "\u0414\u0435\u043D\u044C",
      list: "\u041F\u043E\u0440\u044F\u0434\u043E\u043A \u0434\u0435\u043D\u043D\u0438\u0439"
    },
    weekText: "\u0422\u0438\u0436",
    allDayText: "\u0423\u0432\u0435\u0441\u044C \u0434\u0435\u043D\u044C",
    moreLinkText: function(n) {
      return "+\u0449\u0435 " + n + "...";
    },
    noEventsText: "\u041D\u0435\u043C\u0430\u0454 \u043F\u043E\u0434\u0456\u0439 \u0434\u043B\u044F \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F"
  };

  var l74 = {
    code: "uz",
    buttonText: {
      month: "Oy",
      week: "Xafta",
      day: "Kun",
      list: "Kun tartibi"
    },
    allDayText: "Kun bo'yi",
    moreLinkText: function(n) {
      return "+ yana " + n;
    },
    noEventsText: "Ko'rsatish uchun voqealar yo'q"
  };

  var l75 = {
    code: "vi",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "Tr\u01B0\u1EDBc",
      next: "Ti\u1EBFp",
      today: "H\xF4m nay",
      month: "Th\xE1ng",
      week: "Tu\xE2\u0300n",
      day: "Ng\xE0y",
      list: "L\u1ECBch bi\u1EC3u"
    },
    weekText: "Tu",
    allDayText: "C\u1EA3 ng\xE0y",
    moreLinkText: function(n) {
      return "+ th\xEAm " + n;
    },
    noEventsText: "Kh\xF4ng c\xF3 s\u1EF1 ki\u1EC7n \u0111\u1EC3 hi\u1EC3n th\u1ECB"
  };

  var l76 = {
    code: "zh-cn",
    week: {
      dow: 1,
      doy: 4
    },
    buttonText: {
      prev: "\u4E0A\u6708",
      next: "\u4E0B\u6708",
      today: "\u4ECA\u5929",
      month: "\u6708",
      week: "\u5468",
      day: "\u65E5",
      list: "\u65E5\u7A0B"
    },
    weekText: "\u5468",
    allDayText: "\u5168\u5929",
    moreLinkText: function(n) {
      return "\u53E6\u5916 " + n + " \u4E2A";
    },
    noEventsText: "\u6CA1\u6709\u4E8B\u4EF6\u663E\u793A"
  };

  var l77 = {
    code: "zh-tw",
    buttonText: {
      prev: "\u4E0A\u6708",
      next: "\u4E0B\u6708",
      today: "\u4ECA\u5929",
      month: "\u6708",
      week: "\u9031",
      day: "\u5929",
      list: "\u6D3B\u52D5\u5217\u8868"
    },
    weekText: "\u5468",
    allDayText: "\u6574\u5929",
    moreLinkText: "\u986F\u793A\u66F4\u591A",
    noEventsText: "\u6CA1\u6709\u4EFB\u4F55\u6D3B\u52D5"
  };

  var localesAll = [
    l0,
    l1,
    l2,
    l3,
    l4,
    l5,
    l6,
    l7,
    l8,
    l9,
    l10,
    l11,
    l12,
    l13,
    l14,
    l15,
    l16,
    l17,
    l18,
    l19,
    l20,
    l21,
    l22,
    l23,
    l24,
    l25,
    l26,
    l27,
    l28,
    l29,
    l30,
    l31,
    l32,
    l33,
    l34,
    l35,
    l36,
    l37,
    l38,
    l39,
    l40,
    l41,
    l42,
    l43,
    l44,
    l45,
    l46,
    l47,
    l48,
    l49,
    l50,
    l51,
    l52,
    l53,
    l54,
    l55,
    l56,
    l57,
    l58,
    l59,
    l60,
    l61,
    l62,
    l63,
    l64,
    l65,
    l66,
    l67,
    l68,
    l69,
    l70,
    l71,
    l72,
    l73,
    l74,
    l75,
    l76,
    l77
  ];

  return localesAll;

}());
