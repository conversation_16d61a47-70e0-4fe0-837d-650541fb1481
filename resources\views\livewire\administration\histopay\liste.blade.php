<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">

        <h1 class="h3 fw-bold mb-2">
            Historique des payments
        </h1>

    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">

    <!-- Dynamic Table Full -->
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                Liste des payments
            </h3>
            @can('superadmin')
                <div class="block-options">
                    @php
                        $solde = number_format($totDebit - $totCredit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
                    @endphp
                    <h5 class="block-title">
                        Total Solde : {{ $solde }}
                    </h5>
                </div>
            @endcan
        </div>
        <div class="block-content block-content-full">

            <div class="row mb-3">
                <div class="col-sm-12 col-md-6">
                    <div class="d-flex">
                        <div class="me-3">
                            <button type="button" wire:click="filter()" class="btn btn-md btn-alt-primary">Filtrer
                            </button>
                        </div>
                        @if ($filtre)
                            <div class="input-daterange input-group" data-date-format="yyyy/mm/dd" data-week-start="1"
                                data-autoclose="true" data-today-highlight="true">
                                <input type="text" wire:model="startDate"
                                    onchange='Livewire.emit("selectDateStart", this.value)' class="form-control"
                                    id="example-daterange1" name="example-daterange1" placeholder="Depuis"
                                    data-week-start="1" data-autoclose="true" data-today-highlight="true"
                                    readonly="readonly">
                                <span class="input-group-text fw-semibold">
                                    <i class="fa fa-fw fa-arrow-right"></i>
                                </span>
                                <input type="text" wire:model="endDate"
                                    onchange='Livewire.emit("selectDateEnd", this.value)' class="form-control"
                                    id="example-daterange2" name="example-daterange2" placeholder="Jusqu'à"
                                    data-week-start="1" data-autoclose="true" data-today-highlight="true"
                                    readonly="readonly">
                            </div>
                        @endif


                        <div class="me-3">
                            <label>
                                <select wire:model="filtreNonValide" class="form-select form-select-sm">
                                    <option selected value="">Filtre Validation</option>
                                    <option value="0">Non Validé</option>
                                    <option value="1">Validé</option>
                                </select>
                            </label>
                        </div>

                        <div class="me-3">
                             <label>
                                 <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                     <option selected value="">Filtre Année</option>
                                     @foreach ($annees as $annee)
                                         <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                    </div>



                </div>
                <div class="col-sm-12 col-md-6 text-end">

                    <label>
                        <input type="search" wire:model="query" class="form-control form-control-sm"
                            placeholder="Search..">
                    </label>

                </div>
            </div>


            <div class="table-responsive mb-3">
                <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                <table class="table table-bordered table-striped table-vcenter">
                    <thead>
                        <tr>
                            <th class="text-center">Code</th>
                            <th class="text-center">Libéllés</th>
                            <th class="text-center">Mode</th>
                            <th class="text-center">Débit</th>
                            <th class="text-center">Crédit</th>
                            <th class="text-center">Observation</th>
                            <th class="text-center">Scolarité</th>
                            <th class="text-center" style="width: 100px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $debit = 0;
                            $credit = 0;
                        @endphp
                        @foreach ($pays as $pay)
                            <tr>
                                <td class="text-center fw-semibold">
                                    {{ $pay->code }}
                                </td>
                                <td class="text-center">
                                    @if ($pay->user != null)
                                    {{ $pay->user->nom }} {{ $pay->user->prenom }}: {{ $pay->payment->nom }}
                                        @if ($pay->libelle != null)
                                            - {{ $pay->libelle }}
                                        @endif
                                    @endif
                                </td>
                                <td class="text-center">
                                    {{ $pay->moyen->nom }}
                                </td>
                                <td class="text-center">
                                    @if ($pay->encaissement->id == 1)
                                        @php
                                            $debit += $pay->montant;
                                        @endphp
                                        {{ $pay->prixForHumans }}
                                    @else
                                        0 Ar
                                    @endif

                                </td>
                                <td class="text-center">
                                    @if ($pay->encaissement->id == 2)
                                        @php
                                            $credit += $pay->montant;
                                        @endphp
                                        {{ $pay->prixForHumans }}
                                    @else
                                        0 Ar
                                    @endif
                                </td>
                                <td class="text-center">
                                    {{ $pay->created_at }}
                                </td>
                                <td class="text-center">
                                    @if ($pay->is_valid_sec)
                                        <i class="fa fa-fw fa-circle-check text-success"></i>
                                    @else
                                        <i class="fa fa-fw fa-circle-xmark text-danger"></i>
                                    @endif
                                </td>

                                <td class="text-center">
                                    @if ($pay->is_valid)
                                        <i class="fa fa-fw fa-circle-check text-success"></i>
                                        @can('superadmin')
                                            <button type="button" class="btn btn-sm btn-alt-secondary"
                                                wire:click="goToEditPay({{ $pay->id }})">
                                                Modifier
                                            </button>
                                        @endcan
                                    @else
                                        <div class="btn-group mb-2">
                                            <button type="button" class="btn btn-sm btn-alt-secondary"
                                                wire:click="goToEditPay({{ $pay->id }})">
                                                Modifier
                                            </button>
                                        </div>
                                        @can('superadmin')
                                            <button type="button" wire:click="deletePay({{ $pay->id }})"
                                                onclick="confirm('Êtes-vous sûrs?') || event.stopImmediatePropagation()"
                                                class="btn btn-sm btn-alt-secondary">Supprimer
                                            </button>

                                            <button type="button" wire:click="valider({{ $pay->id }})"
                                                class="btn btn-sm btn-alt-secondary">Valider
                                            </button>
                                        @endcan
                                    @endif


                                </td>
                            </tr>
                        @endforeach



                    </tbody>
                </table>

                <nav aria-label="Photos Search Navigation">
                    <ul class="pagination pagination-sm justify-content-end mt-2">
                        {{ $pays->links() }}
                    </ul>
                </nav>
            </div>
            @php
                $td = number_format($debit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
                $tc = number_format($credit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
                $t = number_format($debit - $credit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
            @endphp
            <div class="text-center">
                <p>
                    Total débit: {{ $td }}
                </p>
                <p>
                    Total crédit: {{ $tc }}
                </p>
                <p>
                    Total: {{ $t }}
                </p>
            </div>



        </div>
    </div>
    <!-- END Dynamic Table Full -->


</div>
<!-- END Page Content -->
