@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>

@endsection

<div wire:ignore.self>

    @if ($currentPage == PAGECREATEFORM)
        @include('livewire.deraq.status.create')
    @endif

    @if ($currentPage == PAGEEDITFORM)
        @include('livewire.deraq.status.edit')
    @endif

    @if ($currentPage == PAGELIST)
        @include('livewire.deraq.status.liste')
    @endif

</div>

<script>
   // Enhanced notification system for NoteStatus
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any UI components that need Javascript setup
    initializeCollapsibleBlocks();
    initializeTooltips();
    
    // Livewire event handlers for notifications
    window.addEventListener("showSuccessMessage", event => {
        showNotification('success', event.detail.message, event.detail.icon || 'fa fa-check me-1');
    });

    window.addEventListener("showErrorMessage", event => {
        showNotification('danger', event.detail.message, event.detail.icon || 'fa fa-times me-1');
    });
    
    window.addEventListener("showInfoMessage", event => {
        showNotification('info', event.detail.message, event.detail.icon || 'fa fa-info-circle me-1');
    });
    
    window.addEventListener("showWarningMessage", event => {
        showNotification('warning', event.detail.message, event.detail.icon || 'fa fa-exclamation-triangle me-1');
    });

    // Function to show notifications with enhanced styling and behavior
    function showNotification(type, message, icon) {
        // Make sure One.helpers is available
        if (typeof One !== 'undefined' && One.helpers) {
            One.helpersOnLoad(['jq-notify']);
            One.helpers('jq-notify', {
                type: type,
                icon: icon,
                message: message || 'Opération effectuée!',
                position: 'top-right', // Position in the top right corner
                timeout: 4000 // Auto-hide after 4 seconds
            });
        } else {
            // Fallback if One framework is not available
            console.log(`${type.toUpperCase()}: ${message}`);
            alert(message);
        }
    }
    
    // Initialize collapsible blocks
    function initializeCollapsibleBlocks() {
        document.querySelectorAll('[data-action="content_toggle"]').forEach(button => {
            button.addEventListener('click', function() {
                const blockId = this.closest('.block').id;
                const content = this.closest('.block').querySelector('.block-content');
                
                if (content) {
                    if (content.style.display === 'none') {
                        content.style.display = '';
                        this.querySelector('i').classList.remove('si-arrow-down');
                        this.querySelector('i').classList.add('si-arrow-up');
                    } else {
                        content.style.display = 'none';
                        this.querySelector('i').classList.remove('si-arrow-up');
                        this.querySelector('i').classList.add('si-arrow-down');
                    }
                }
            });
        });
    }
    
    // Initialize tooltips for the UI
    function initializeTooltips() {
        // Check if Bootstrap's tooltip function is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.forEach(function(tooltipTriggerEl) {
                new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }
    
    // Improve filter panel experience
    const filterButtons = document.querySelectorAll('.form-select');
    filterButtons.forEach(button => {
        button.addEventListener('change', function() {
            // Add a loading indicator to the table while Livewire updates
            const tableContainer = document.querySelector('.table-responsive');
            if (tableContainer) {
                tableContainer.classList.add('opacity-50');
                setTimeout(() => {
                    tableContainer.classList.remove('opacity-50');
                }, 500);
            }
        });
    });
});
</script>





