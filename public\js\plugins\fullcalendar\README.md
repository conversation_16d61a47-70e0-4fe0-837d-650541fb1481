
# FullCalendar Standard Bundle

FullCalendar is a full-sized drag & drop event calendar. This packages is an easily consumable combination of standard plugins. It makes the root namespace available as the `FullCalendar` browser global. [View the docs &raquo;](https://fullcalendar.io/docs/getting-started)

This `fullcalendar` package bundles together these plugins:

- [@fullcalendar/core](https://www.npmjs.com/package/@fullcalendar/core)
- [@fullcalendar/interaction](https://www.npmjs.com/package/@fullcalendar/interaction)
- [@fullcalendar/daygrid](https://www.npmjs.com/package/@fullcalendar/daygrid)
- [@fullcalendar/timegrid](https://www.npmjs.com/package/@fullcalendar/timegrid)
- [@fullcalendar/list](https://www.npmjs.com/package/@fullcalendar/list)
- [@fullcalendar/bootstrap](https://www.npmjs.com/package/@fullcalendar/bootstrap)
- [@fullcalendar/google-calendar](https://www.npmjs.com/package/@fullcalendar/google-calendar)
