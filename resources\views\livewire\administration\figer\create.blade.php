<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListRattrapage()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListRattrapage()">Générer Rattrapage</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Liste des étudiants en rattrapage
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<!-- Page Content -->
<div class="content">

    <!-- Dynamic Table Full -->
    <div class="block block-rounded">
        
        <div class="block-content block-content-full">
            <div class="table-responsive">
                <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                <table class="table table-bordered table-vcenter">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">Unité d’Enseignement</th>
                            <th scope="col" class="text-center">Elément Constitutif</th>
                            <th scope="col" class="text-center">Note</th>
                            <th scope="col" class="text-center">Moy UE</th>
                        </tr>
                    </thead>
                    <tbody>

                        {{-- @foreach ($notes as $note)
                           
                                <tr>
                                <td class="text-center">{{ $note["nom"] }}</td>
                                @foreach ($note['notes'] as $no)
                                    <td class="text-center">{{ $no["ue"] }}</td>
                                    <td class="text-center">{{ $no["moy"] }}</td>
                                @endforeach
                                
                                
                            </tr>
                            
                            
                        @endforeach --}}

                        @forelse ($notes as $etu)
                            <tr>
                                <td colspan="4" class="text-center" style="background-color: #C4BC96">
                                    {{ $etu['nom'] }} {{ $etu['prenom'] }}
                                </td>
                            </tr>
                            @foreach ($etu['ue'] as $ec)
                                <tr>

                                    <td rowspan="{{ count($ec['matiere']) }}" class="text-center">
                                        {{ $ec['nom'] }}
                                    </td>
                                    <td class="text-center">
                                        {{ $ec['matiere'][0]['nom'] }} </td>
                                    <td class="text-center"
                                        @if ($ec['matiere'][0]['note'] < 5) style="background-color: #FF3333" @endif>
                                        {{ $ec['matiere'][0]['note'] }} </td>
                                    <td rowspan="{{ count($ec['matiere']) }}" class="text-center">
                                        {{ $ec['moy'] }}
                                    </td>

                                </tr>
                                @for ($i = 1; $i < count($ec['matiere']); $i++)
                                    <tr>
                                        <td class="text-center">
                                            {{ $ec['matiere'][$i]['nom'] }} </td>
                                        <td class="text-center"
                                            @if ($ec['matiere'][$i]['note'] < 5) style="background-color: #FF3333" @endif>
                                            {{ $ec['matiere'][$i]['note'] }} </td>
                                    </tr>
                                @endfor
                            @endforeach



                        @empty
                            <tr>
                                <td colspan="4">
                                    <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                        role="alert">
                                        <div class="flex-grow-1 me-3">
                                            <p class="mb-0">
                                                Impossible de générer. Pas encore de note ajouté !!
                                            </p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-fw fa-exclamation-circle"></i>
                                        </div>

                                    </div>
                                </td>
                            </tr>
                        @endforelse



                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- END Dynamic Table Full -->


</div>
<!-- END Page Content -->
