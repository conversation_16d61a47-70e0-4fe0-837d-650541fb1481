<div>
    <!-- En-tête -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 fw-bold mb-1">
                        <button type="button" class="btn btn-sm btn-alt-secondary me-2" wire:click="backToCourses">
                            <i class="fa fa-arrow-left"></i>
                        </button>
                        Notes: {{ $matiere->nom }}
                    </h1>
                    <p class="text-muted mb-0">
                        {{ $matiere->ue->parcours->sigle }} - {{ $matiere->ue->niveau->nom }}
                    </p>
                </div>
                <div class="d-flex align-items-center">
                    <div class="form-check form-switch me-3">
                        <input class="form-check-input" type="checkbox" wire:model="autoSave" id="autoSaveSwitch">
                        <label class="form-check-label" for="autoSaveSwitch">
                            Sauvegarde auto
                        </label>
                    </div>
                    <button type="button" class="btn btn-primary" wire:click="applyBulkNote"
                        @if (!$selectedStudents || count($selectedStudents) === 0 || $bulkNote === null) disabled @endif>
                        <i class="fa fa-save me-1"></i> Appliquer les notes sélectionnées
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="content">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <div class="block-options">
                    <div class="row g-2 align-items-center">
                        <div class="col-auto">
                            <select wire:model="noteType" class="form-select form-select-sm">
                                @foreach ($typeNotes as $type)
                                    <option value="{{ $type->id }}">{{ $type->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-auto">
                            <div class="input-group input-group-sm">
                                <input type="search" wire:model="searchTerm" class="form-control form-control-sm"
                                    placeholder="Rechercher un étudiant...">
                                <span class="input-group-text">
                                    <i class="fa fa-search"></i>
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="input-group input-group-sm">
                                <input type="number" wire:model="bulkNote" class="form-control form-control-sm"
                                    placeholder="Note en masse" min="0" max="20" step="0.25">
                                <span class="input-group-text">/ 20</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="block-content">
                <!-- Tableau des notes -->
                <div class="table-responsive">
                    <table class="table table-hover table-vcenter">
                        <thead>
                            <tr>
                                <th style="width: 40px;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" wire:click="selectAllStudents">
                                    </div>
                                </th>
                                <th>Étudiant</th>
                                <th style="width: 130px;">Note</th>
                                <th>Observation</th>
                                <th style="width: 80px;">Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($noteEtus as $index => $noteEtu)
                                <tr @if (in_array($index, $selectedStudents)) class="table-primary" @endif>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                wire:click="toggleSelectStudent({{ $index }})"
                                                @if (in_array($index, $selectedStudents)) checked @endif>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-semibold">{{ $noteEtu['nom'] }} {{ $noteEtu['prenom'] }}</div>
                                    </td>
                                    <td>
                                        <div
                                            class="input-group input-group-sm @if (!$isEditable || (isset($noteEtu['isEditable']) && !$noteEtu['isEditable'])) disabled @endif">
                                            <input type="number" class="form-control"
                                                wire:model.lazy="noteEtus.{{ $index }}.valeur"
                                                wire:change="@if ($autoSave) updateNote({{ $index }}) @endif"
                                                min="0" max="20" step="0.25"
                                                @if (!$isEditable || (isset($noteEtu['isEditable']) && !$noteEtu['isEditable'])) disabled @endif>
                                            <span class="input-group-text">/20</span>
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm"
                                            wire:model.lazy="noteEtus.{{ $index }}.observation"
                                            wire:change="@if ($autoSave) updateNote({{ $index }}) @endif"
                                            placeholder="Observation..."
                                            @if (!$isEditable || (isset($noteEtu['isEditable']) && !$noteEtu['isEditable'])) disabled @endif>
                                    </td>
                                    <td class="text-center">
                                        @if (!$autoSave)
                                            <button type="button" class="btn btn-sm btn-alt-primary"
                                                wire:click="updateNote({{ $index }})"
                                                @if (!$isEditable || (isset($noteEtu['isEditable']) && !$noteEtu['isEditable'])) disabled @endif>
                                                <i class="fa fa-save"></i>
                                            </button>
                                        @else
                                            @if (isset($saveStatus[$noteEtu['user_id']]))
                                                @if ($saveStatus[$noteEtu['user_id']] === 'saving')
                                                    <span class="badge bg-warning"><i
                                                            class="fa fa-spinner fa-spin"></i></span>
                                                @elseif ($saveStatus[$noteEtu['user_id']] === 'saved')
                                                    <span class="badge bg-success"><i class="fa fa-check"></i></span>
                                                @elseif ($saveStatus[$noteEtu['user_id']] === 'error')
                                                    <span class="badge bg-danger"><i class="fa fa-times"></i></span>
                                                @endif
                                            @endif
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fa fa-graduation-cap fa-2x mb-2"></i>
                                            <p>Aucun étudiant trouvé pour ce cours.</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination si nécessaire -->
                {{-- @if (count($noteEtus) > $perPage)
                    <div class="d-flex justify-content-center mt-4">
                        {{ $noteEtus->links() }}
                    </div>
                @endif --}}
            </div>
        </div>

        <!-- Légende des statuts -->
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Légende</h3>
            </div>
            <div class="block-content">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-warning me-2"><i class="fa fa-spinner fa-spin"></i></span>
                            <span>Sauvegarde en cours</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-success me-2"><i class="fa fa-check"></i></span>
                            <span>Sauvegarde réussie</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-danger me-2"><i class="fa fa-times"></i></span>
                            <span>Erreur de sauvegarde</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts spécifiques à l'éditeur -->
    <script>
        document.addEventListener('livewire:load', function() {
            // Initialiser les outils JS nécessaires
            One.helpersOnLoad(['jq-notify']);

            // Raccourcis clavier pour la navigation entre les cellules
            document.addEventListener('keydown', function(e) {
                if (e.target.tagName === 'INPUT' && e.target.type === 'number') {
                    // Touche Tab pour naviguer normalement
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const inputs = Array.from(document.querySelectorAll('input[type="number"]'));
                        const currentIndex = inputs.indexOf(e.target);
                        const nextInput = inputs[currentIndex + 1];

                        if (nextInput) {
                            nextInput.focus();
                            nextInput.select();
                        }
                    }
                }
            });
        });
    </script>
</div>
