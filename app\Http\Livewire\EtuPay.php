<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoriquePayment;
use App\Models\MoyenPayment;
use App\Models\Niveau;
use App\Models\TypePayment;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class EtuPay extends Component
{
    public $currentPage = PAGEEDITFORM;

    public $newPay = [];
    public $paymentSummary = [];
    public $totalPaid = 0;
    public $totalRequired = 0;

    public User $current_user;
    public Niveau $current_niveau;
    public AnneeUniversitaire $current_annee;

    protected $listeners = ['refreshComponent' => '$refresh'];

    public function mount($userId, $niveauId, $anneeId)
    {
        $this->current_user = User::findOrFail($userId);
        $this->current_niveau = Niveau::findOrFail($niveauId);
        $this->current_annee = AnneeUniversitaire::findOrFail($anneeId);

        $this->populate();
        $this->calculateSummary();
    }

    public function render()
    {
        return view('livewire.caf.payment.index', [
            "moyens" => MoyenPayment::all(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        return [
            'newPay.*.code' => 'required',
            'newPay.*.montant' => 'required|numeric',
            'newPay.*.moyen' => 'required|exists:moyen_payments,id',
        ];
    }

    /**
     * Calculate payment summary statistics
     */
    public function calculateSummary()
    {
        $this->totalPaid = 0;
        $this->totalRequired = 0;

        foreach ($this->newPay as $payment) {
            $paid = HistoriquePayment::where('user_id', $this->current_user->id)
                ->where('annee_universitaire_id', $this->current_annee->id)
                ->where('type_payment_id', $payment['type_id'])
                ->sum('montant');

            $this->totalPaid += $paid;
            $this->totalRequired += $payment['max'];
            
            $this->paymentSummary[$payment['type_id']] = [
                'paid' => $paid,
                'required' => $payment['max'],
                'name' => $payment['type_nom'],
                'percentage' => $payment['max'] > 0 ? ($paid / $payment['max']) * 100 : 0,
                'completed' => $paid >= $payment['max']
            ];
        }
    }

    /**
     * Populate payment types data
     */
    public function populate()
    {
        // Optimize query to fetch payment types in a single query
        $paymentTypes = TypePayment::with(['niveau' => function($query) {
            $query->where('niveau_id', $this->current_niveau->id)
                  ->where('annee_universitaire_id', $this->current_annee->id);
        }])->get();

        $this->newPay = [];
        
        foreach ($paymentTypes as $type) {
            // Only add payment types that are associated with the current niveau
            $niveauRelation = $type->niveau->where('id', $this->current_niveau->id)->first();
            
            if ($niveauRelation) {
                $this->newPay[] = [
                    "type_id" => $type->id,
                    "type_nom" => $type->nom,
                    "max" => $niveauRelation->pivot->prix ?? 0,
                    "code" => "",
                    "montant" => "",
                    "moyen" => ""
                ];
            }
        }
    }

    /**
     * Handle payment validation
     */
    public function valider($index)
    {
        $validateArr = [
            "newPay.". $index .".code" => 'required',
            "newPay.". $index .".montant" => "required|numeric|max:". $this->newPay["$index"]["max"],
            "newPay.". $index .".moyen" => 'required|exists:moyen_payments,id',
        ];
        
        $this->validate($validateArr);

        try {
            DB::beginTransaction();
            
            $this->current_user->historique()->create([
                "moyen_payment_id" => $this->newPay["$index"]["moyen"],
                "montant" => $this->newPay["$index"]["montant"],
                "code" => $this->newPay["$index"]["code"],
                "type_encaissement_id" => 1,
                "annee_universitaire_id" => $this->current_annee->id,
                "type_payment_id" => $this->newPay["$index"]["type_id"],
            ]);
            
            DB::commit();
            
            // Reset the form fields
            $this->newPay["$index"]["code"] = "";
            $this->newPay["$index"]["montant"] = "";
            $this->newPay["$index"]["moyen"] = "";
            
            // Recalculate payment summary
            $this->calculateSummary();
            
            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "Paiement enregistré avec succès!"
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de l'enregistrement du paiement: " . $e->getMessage()
            ]);
        }
    }
}