var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Polish = {
    weekdays: {
        shorthand: ["Nd", "Pn", "Wt", "Śr", "<PERSON><PERSON>", "Pt", "So"],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "Ponied<PERSON>ł<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Czwartek",
            "Piątek",
            "Sobota",
        ],
    },
    months: {
        shorthand: [
            "St<PERSON>",
            "Lut",
            "Mar",
            "<PERSON>wi",
            "<PERSON>",
            "<PERSON><PERSON>",
            "Lip",
            "<PERSON>e",
            "<PERSON>rz",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>ru",
        ],
        longhand: [
            "Stycz<PERSON>ń",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Kwiec<PERSON>ń",
            "Maj",
            "Czerwiec",
            "Lipiec",
            "Sierpień",
            "Wrzesień",
            "Październik",
            "Listopad",
            "Grudzień",
        ],
    },
    rangeSeparator: " do ",
    weekAbbreviation: "tydz.",
    scrollTitle: "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby zwi<PERSON><PERSON><PERSON>",
    toggleTitle: "<PERSON><PERSON><PERSON><PERSON>, aby prze<PERSON>",
    firstDayOfWeek: 1,
    time_24hr: true,
    ordinal: function () {
        return ".";
    },
};
fp.l10ns.pl = Polish;
export default fp.l10ns;
