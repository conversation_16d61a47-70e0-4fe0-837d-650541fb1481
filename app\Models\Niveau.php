<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Niveau extends Model
{
    use HasFactory, SoftDeletes;

    public function user(){
        return $this->hasMany(User::class);
    }

    public function ue(){
        return $this->hasMany(Ue::class);
    }

    public function semestre(){
        return $this->hasMany(Semestre::class);
    }

    public function matiere(){
        return $this->hasMany(Matiere::class);
    }

    public function typePay(){
        return $this->belongsToMany(TypePayment::class);
    }
}
