@section('css')
    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="{{ asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css') }}">
    <style>
        .edit-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1050;
            overflow-y: auto;
        }

        .edit-modal.show {
            display: block;
        }

        .edit-modal-content {
            background-color: #fff;
            margin: 50px auto;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            max-width: 700px;
            position: relative;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-card {
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            transition: transform 0.2s;
        }

        .summary-card:hover {
            transform: translateY(-3px);
        }

        .card-debit {
            background-color: #edfaf2;
            border-left: 4px solid #48bb78;
        }

        .card-credit {
            background-color: #fdf2f2;
            border-left: 4px solid #f56565;
        }

        .card-solde {
            background-color: #ebf4ff;
            border-left: 4px solid #4299e1;
        }

        .summary-card h4 {
            font-size: 0.9rem;
            margin-bottom: 5px;
            color: #4a5568;
        }

        .summary-card h2 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0;
        }

        .filter-bar {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .badge-status {
            padding: 5px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-valid {
            background-color: rgb(3, 161, 93);
            color: #057a55;
        }

        .badge-invalid {
            background-color: rgb(231, 33, 33);
            color: #e02424;
        }

        .action-buttons button {
            margin: 2px;
        }

        /* Style pour les onglets si on ajoute plus de sections */
        .custom-tab {
            border-bottom: 3px solid transparent;
            padding: 10px 15px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .custom-tab.active {
            border-bottom-color: #4299e1;
            color: #2b6cb0;
        }

        .table-responsive {
            overflow-x: auto;
            border-radius: 5px;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        table th {
            background-color: #f8f9fa;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.5px;
        }

        table td,
        table th {
            padding: 12px 15px;
            vertical-align: middle;
        }

        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 1.5rem;
            cursor: pointer;
            opacity: 0.5;
            transition: opacity 0.2s;
        }

        .close-btn:hover {
            opacity: 1;
        }
    </style>
@endsection

@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    <script src="{{ asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
@endsection

<div wire:ignore.self>
    <!-- Hero -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 fw-bold mb-0">Historique des paiements</h1>
                {{-- <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-alt-secondary" wire:click="exportCSV" title="Exporter">
                        <i class="fa fa-fw fa-file-export"></i> Exporter
                    </button>
                    <button type="button" class="btn btn-alt-secondary" onclick="window.print()" title="Imprimer">
                        <i class="fa fa-fw fa-print"></i> Imprimer
                    </button>
                </div> --}}
            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        @can('superadmin')
            <!-- Summary Cards -->
            <div class="summary-cards">
                <div class="summary-card card-debit">
                    <h4>Total Débit</h4>
                    <h2>{{ number_format($totDebit, 0, ',', ' ') }} {{ env('CURRENCY', 'Ar') }}</h2>
                </div>
                <div class="summary-card card-credit">
                    <h4>Total Crédit</h4>
                    <h2>{{ number_format($totCredit, 0, ',', ' ') }} {{ env('CURRENCY', 'Ar') }}</h2>
                </div>
                <div class="summary-card card-solde">
                    <h4>Solde</h4>
                    <h2>{{ number_format($totDebit - $totCredit, 0, ',', ' ') }} {{ env('CURRENCY', 'Ar') }}</h2>
                </div>
            </div>
        @endcan

        <!-- Filters -->
        <div class="filter-bar mb-3">
            <div class="row g-2 align-items-end">
                <div class="col-md-2">
                    <label class="form-label mb-1">Date de début</label>
                    <input type="date" class="form-control form-control-sm" wire:model="startDate">
                </div>
                <div class="col-md-2">
                    <label class="form-label mb-1">Date de fin</label>
                    <input type="date" class="form-control form-control-sm" wire:model="endDate">
                </div>
                <div class="col-md-2">
                    <label class="form-label mb-1">Statut</label>
                    <select wire:model="filtreNonValide" class="form-select form-select-sm">
                        <option value="">Tous</option>
                        <option value="0">Non Validé</option>
                        <option value="1">Validé</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label mb-1">Année</label>
                    <select wire:model="filtreAnnee" class="form-select form-select-sm">
                        <option value="">Toutes</option>
                        @foreach ($annees as $annee)
                            <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label mb-1">Recherche</label>
                    <input type="search" wire:model="query" class="form-control form-control-sm"
                        placeholder="Code, nom, prénom...">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button class="btn btn-sm btn-alt-secondary w-100" wire:click="resetFilters">
                        <i class="fa fa-times"></i> Réinitialiser
                    </button>
                </div>
            </div>
            <div class="mt-2">
                @if ($startDate || $endDate || $filtreNonValide !== '' || $filtreAnnee || $query)
                    <div class="d-flex flex-wrap gap-2">
                        @if ($startDate)
                            <span class="badge bg-info">Début: {{ $startDate }}</span>
                        @endif
                        @if ($endDate)
                            <span class="badge bg-info">Fin: {{ $endDate }}</span>
                        @endif
                        @if ($filtreNonValide !== '')
                            <span class="badge bg-info">
                                Statut: {{ $filtreNonValide ? 'Validé' : 'Non validé' }}
                            </span>
                        @endif
                        @if ($filtreAnnee)
                            <span class="badge bg-info">
                                Année: {{ $annees->firstWhere('id', $filtreAnnee)?->nom }}
                            </span>
                        @endif
                        @if ($query)
                            <span class="badge bg-info">Recherche: {{ $query }}</span>
                        @endif
                    </div>
                @endif
            </div>
        </div>
        <div wire:loading class="text-center my-2">
            <span class="spinner-border spinner-border-sm"></span> Chargement...
        </div>

        <!-- Payment History Table -->
        <div class="block block-rounded">
            <div class="block-content block-content-full">
                <div class="table-responsive">
                    <table class="table table-hover table-vcenter">
                        <thead>
                            <tr>
                                <th class="text-center" style="width: 100px;">Code</th>
                                <th>Libellés</th>
                                <th class="text-center">Mode</th>
                                <th class="text-center">Débit</th>
                                <th class="text-center">Crédit</th>
                                <th class="text-center">Date</th>
                                <th class="text-center">Statut</th>
                                <th class="text-center" style="width: 150px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $debit = 0;
                                $credit = 0;
                            @endphp
                            @foreach ($pays as $pay)
                                <tr>
                                    <td class="fw-semibold text-center">
                                        {{ $pay->code }}
                                    </td>
                                    <td>
                                        @if ($pay->user != null)
                                            <div class="fw-semibold">{{ $pay->user->nom }} {{ $pay->user->prenom }}
                                            </div>
                                            <div class="fs-sm text-muted">{{ $pay->payment->nom }}
                                                @if ($pay->libelle != null)
                                                    - {{ $pay->libelle }}
                                                @endif
                                            </div>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">{{ $pay->moyen->nom }}</span>
                                    </td>
                                    <td class="text-center">
                                        @if ($pay->encaissement->id == 1)
                                            @php
                                                $debit += $pay->montant;
                                            @endphp
                                            <span class="fw-semibold text-success">{{ $pay->prixForHumans }}</span>
                                        @else
                                            <span class="text-muted">0 {{ env('CURRENCY', 'Ar') }}</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if ($pay->encaissement->id == 2)
                                            @php
                                                $credit += $pay->montant;
                                            @endphp
                                            <span class="fw-semibold text-danger">{{ $pay->prixForHumans }}</span>
                                        @else
                                            <span class="text-muted">0 {{ env('CURRENCY', 'Ar') }}</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <span
                                            class="fs-sm">{{ \Carbon\Carbon::parse($pay->created_at)->format('d/m/Y H:i') }}</span>
                                    </td>
                                    <td class="text-center">
                                        @if ($pay->is_valid)
                                            <span class="badge badge-status badge-valid">
                                                <i class="fa fa-check me-1"></i> Validé
                                            </span>
                                        @else
                                            <span class="badge badge-status badge-invalid">
                                                <i class="fa fa-times me-1"></i> Non validé
                                            </span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-alt-secondary"
                                                wire:click="goToEditPay({{ $pay->id }})" title="Modifier">
                                                <i class="fa fa-pencil"></i>
                                            </button>

                                            @if (!$pay->is_valid)
                                                @can('superadmin')
                                                    <button type="button" class="btn btn-sm btn-alt-success"
                                                        wire:click="valider({{ $pay->id }})" title="Valider">
                                                        <i class="fa fa-check"></i>
                                                    </button>

                                                    <button type="button" class="btn btn-sm btn-alt-danger"
                                                        wire:click="deletePay({{ $pay->id }})"
                                                        onclick="confirm('Êtes-vous sûrs de vouloir supprimer ce paiement?') || event.stopImmediatePropagation()"
                                                        title="Supprimer">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                @endcan
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach

                            @if (count($pays) == 0)
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fa fa-search fa-2x mb-2"></i>
                                            <p>Aucun résultat trouvé</p>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        </tbody>
                        <tfoot>
                            <tr class="bg-body-light">
                                <td colspan="3" class="text-end fw-semibold">Totaux:</td>
                                <td class="text-center fw-bold text-success">{{ number_format($debit, 0, ',', ' ') }}
                                    {{ env('CURRENCY', 'Ar') }}</td>
                                <td class="text-center fw-bold text-danger">{{ number_format($credit, 0, ',', ' ') }}
                                    {{ env('CURRENCY', 'Ar') }}</td>
                                <td colspan="3" class="text-center fw-bold">
                                    Solde: {{ number_format($debit - $credit, 0, ',', ' ') }}
                                    {{ env('CURRENCY', 'Ar') }}
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <div class="d-flex justify-content-center mt-3">
                    {{ $pays->links() }}
                </div>
            </div>
        </div>
    </div>
    <!-- END Page Content -->

    <!-- Edit Payment Modal -->
    <div class="edit-modal {{ $currentPage == PAGEEDITFORM ? 'show' : '' }}">
        <div class="edit-modal-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="h4 mb-0">Modifier le paiement</h3>
                <span class="close-btn" wire:click="goToListPay">&times;</span>
            </div>

            @if ($currentPage == PAGEEDITFORM)
                <form wire:submit.prevent="updatePay">
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <div class="block block-rounded bg-body-light p-3">
                                <div class="fw-semibold">Étudiant:</div>
                                <div class="fs-4">{{ $current_user->nom }} {{ $current_user->prenom }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="code">Code</label>
                            <input type="text" class="form-control @error('editPay.code') is-invalid @enderror"
                                id="code" wire:model.defer="editPay.code">
                            @error('editPay.code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="date">Date</label>
                            <input type="datetime-local"
                                class="form-control @error('editPay.created_at') is-invalid @enderror" id="date"
                                wire:model.defer="editPay.created_at">
                            @error('editPay.created_at')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="moyen">Mode de paiement</label>
                            <select class="form-select @error('editPay.moyen_payment_id') is-invalid @enderror"
                                id="moyen" wire:model.defer="editPay.moyen_payment_id">
                                <option value="">Sélectionner un mode</option>
                                @foreach ($moyens as $moyen)
                                    <option value="{{ $moyen->id }}">{{ $moyen->nom }}</option>
                                @endforeach
                            </select>
                            @error('editPay.moyen_payment_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="type">Type de paiement</label>
                            <select class="form-select @error('editPay.type_payment_id') is-invalid @enderror"
                                id="type" wire:model.defer="editPay.type_payment_id">
                                <option value="">Sélectionner un type</option>
                                @foreach ($types as $type)
                                    <option value="{{ $type->id }}">{{ $type->nom }}</option>
                                @endforeach
                            </select>
                            @error('editPay.type_payment_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-12 mb-3">
                            <label class="form-label" for="montant">Montant</label>
                            <div class="input-group">
                                <input type="number"
                                    class="form-control @error('editPay.montant') is-invalid @enderror" id="montant"
                                    wire:model.defer="editPay.montant">
                                <span class="input-group-text">{{ env('CURRENCY', 'Ar') }}</span>
                                @error('editPay.montant')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-alt-secondary" wire:click="goToListPay">
                            <i class="fa fa-times me-1"></i> Annuler
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save me-1"></i> Enregistrer les modifications
                        </button>
                    </div>
                </form>
            @endif
        </div>
    </div>
</div>

<script>
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    });

    window.addEventListener("helperDatePicker", event => {
        One.helpersOnLoad(['jq-datepicker']);
    });
</script>
