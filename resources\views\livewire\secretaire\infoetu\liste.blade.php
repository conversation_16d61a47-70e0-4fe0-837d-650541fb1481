 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Etudiants
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">
     <div class="row">
         <div class="col-md-8">
             <!-- Dynamic Table Full -->
             <div class="block block-rounded">
                 <div class="block-header block-header-default">
                     <h3 class="block-title">
                         Liste des Etudiants
                     </h3>
                     <div class="block-options">
                         <a class="btn btn-sm btn-primary me-1" wire:click="goToAddUser()">
                             <i class="fa fa-fw fa-user-plus me-1"></i> Nouvel Etudiant
                         </a>
                     </div>
                 </div>
                 <div class="block-content block-content-full">
                     <div class="row">
                         <div class="col-sm-12 col-md-6">
                             <div class="d-flex">
                                 {{-- <div>
                             <label>
                                 <select wire:model="filtreParcours" class="form-select form-select-sm">
                                     <option selected value="">Filtre Parcours</option>
                                     @foreach ($parcours as $parcour)
                                         <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div> --}}
                                 <div>
                                     <label>
                                         <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                             <option selected value="">Filtre Niveau</option>
                                             @foreach ($niveaux as $niveau)
                                                 <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                             @endforeach
                                         </select>
                                     </label>
                                 </div>
                                 <div>
                                     <label>
                                         <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                             <option selected value="">Filtre Année</option>
                                             @foreach ($annees as $annee)
                                                 <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                             @endforeach
                                         </select>
                                     </label>
                                 </div>


                             </div>

                         </div>
                         <div class="col-sm-12 col-md-6 text-end">

                             <label>
                                 <input type="search" wire:model="query" class="form-control form-control-sm"
                                     placeholder="Search..">
                             </label>

                         </div>
                     </div>
                     <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                     <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                         <thead>
                             <tr>
                                 <th>Nom et Prénom</th>
                                 <th>Niveau</th>
                                 <th>Année Universitaire</th>
                                 <th class="text-center" style="width: 120px;">Actions</th>
                             </tr>
                         </thead>
                         <tbody>

                             @foreach ($etus as $etu)
                                 <tr>
                                     {{-- @dump($etu) --}}
                                     <td class="fw-semibold">
                                         {{ $etu->user->nom }} {{ $etu->user->prenom }}
                                     </td>
                                     {{-- <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->parcours->sigle }}
                             </td> --}}
                                     <td class="text-muted">
                                         {{-- @dump($etu->niveau ) --}}
                                         @if ($etu->niveau == null)
                                             Pas de niveau
                                         @else
                                             {{ $etu->niveau->nom }}
                                         @endif
                                     </td>
                                     <td class="text-muted">

                                         {{ $etu->annee->nom }}


                                     </td>
                                     <td class="text-center">
                                         <div class="btn-group mb-2">
                                             @if ($etu->parcours == null)
                                                 <button type="button" class="btn btn-sm btn-alt-secondary"
                                                     wire:click="goToCreateUser({{ $etu->user->id }})" title="Edit">
                                                     <i class="fa fa-fw fa-pencil-alt"></i>Remplir
                                                 </button>
                                             @else
                                                 <button type="button" class="btn btn-sm btn-alt-success"
                                                     wire:click="goToEditUser({{ $etu->user->id }})">
                                                     <i class="fa fa-check"></i> Modifier
                                             @endif

                                             {{-- <button type="button" class="btn btn-sm btn-alt-secondary"
                                         wire:click="deleteUser({{ $etu->user->id }})" title="Delete">
                                         <i class="fa fa-fw fa-times"></i>
                                     </button> --}}
                                         </div>


                                     </td>
                                 </tr>
                             @endforeach



                         </tbody>
                     </table>

                     <nav aria-label="Photos Search Navigation">
                         <ul class="pagination pagination-sm justify-content-end mt-2">
                             {{ $etus->links() }}
                         </ul>
                     </nav>
                 </div>
             </div>
             <!-- END Dynamic Table Full -->


         </div>

         <div class="col-md-4">
             <div class="block block-rounded">
                 <div class="block-header block-header-default align-items-center">
                     <h3 class="block-title">Etat</h3>
                 </div>
                 <div class="block-content">
                 <p class="text-end fw-bold">Total Etudiant: {{ $totalEtuCount }}</p>
                     <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                         <thead>
                             <tr>
                                 <th>Parcours</th>
                                 <th>Nombre Etudiants</th>
                             </tr>
                         </thead>
                         <tbody>
                             @forelse($etuStatus as $parcour)
                                 <tr>
                                     <td>{{ $parcour->nom }}</td>
                                     <td>{{ $parcour->etu_count }}</td>
                                 </tr>
                             @empty
                                 <tr>
                                     <td colspan="2">No Parcours found</td>
                                 </tr>
                             @endforelse
                         </tbody>
                     </table>

                 </div>
             </div>

         </div>
     </div>
 </div>
 <!-- END Page Content -->
