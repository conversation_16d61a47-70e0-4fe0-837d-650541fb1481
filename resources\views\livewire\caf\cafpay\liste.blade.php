<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">

        <h1 class="h3 fw-bold mb-2">
            Payment du jour
        </h1>

    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">

    <!-- Dynamic Table Full -->
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                Liste des payments
            </h3>
            <div class="block-options">
                 <a class="btn btn-sm btn-primary me-1" href="{{ route('caf.caisse.generate-pdf') }}">
                    Imprimer payment du jour
                 </a>
             </div>
        </div>
        <div class="block-content block-content-full">

            <div class="text-end mb-3">

                <label>
                    <input type="search" wire:model="query" class="form-control form-control-sm" placeholder="Search..">
                </label>

            </div>


            <div class="table-responsive mb-3">
                <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                <table class="table table-bordered table-striped table-vcenter">
                    <thead>
                        <tr>
                            <th class="text-center">Code</th>
                            <th class="text-center">Libéllés</th>
                            <th class="text-center">Mode</th>
                            <th class="text-center">Débit</th>
                            <th class="text-center">Crédit</th>
                            <th class="text-center">Observation</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $debit = 0;
                            $credit = 0;
                        @endphp
                        @foreach ($pays as $pay)
                            <tr>
                                <td class="text-center fw-semibold">
                                    {{ $pay->code }}
                                </td>
                                <td class="text-center">
                                    {{ $pay->user->nom }} {{ $pay->user->prenom }} : {{ $pay->payment->nom }}
                                </td>
                                <td class="text-center">
                                    {{ $pay->moyen->nom }}
                                </td>
                                <td class="text-center">
                                    @if ($pay->encaissement->id == 1)
                                        @php
                                            $debit += $pay->montant;
                                        @endphp
                                        {{ $pay->prixForHumans }}
                                    @else
                                        0 Ar
                                    @endif

                                </td>
                                <td class="text-center">
                                    @if ($pay->encaissement->id == 2)
                                        @php
                                            $credit += $pay->montant;
                                        @endphp
                                        {{ $pay->prixForHumans }}
                                    @else
                                        0 Ar
                                    @endif
                                </td>
                                <td class="text-center">
                                    {{ $pay->created_at }}
                                </td>
                                
                            </tr>
                        @endforeach



                    </tbody>
                </table>
            </div>
            @php
                $td = number_format($debit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
                $tc = number_format($credit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
                $t = number_format($debit - $credit, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
            @endphp
            <div class="text-center">
                <p>
                    Total débit: {{ $td }}
                </p>
                <p>
                    Total crédit: {{ $tc }}
                </p>
                <p>
                    Total payment du jour : {{ $t }}
                </p>
            </div>
        </div>
    </div>
    <!-- END Dynamic Table Full -->


</div>
<!-- END Page Content -->
