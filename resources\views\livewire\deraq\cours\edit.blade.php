<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListCours()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListCours()">Liste des Cours</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Creation de Cours
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="updateCours()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire d'ajout de Cours</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom<span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editCours.nom"
                            class="form-control @error('editCours.nom') is-invalid @enderror" id="example-text-input"
                            name="firstname" placeholder="Text Input">

                        @error('editCours.nom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Code<span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editCours.code"
                            class="form-control @error('editCours.code') is-invalid @enderror" id="example-text-input"
                             placeholder="Text Input">

                        @error('editCours.code')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    


                    <div class="col-6">
                        <label class="form-label" for="example-select">Enseignant <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editCours.user_id') is-invalid @enderror"
                            wire:model="editCours.user_id" id="example-select" name="example-select">
                            <option selected value="0">Open this select menu</option>
                            @foreach ($enseignants as $enseignant)
                                <option value="{{ $enseignant->id }}">{{ $enseignant->nom }} {{ $enseignant->prenom }}</option>
                            @endforeach
                        </select>

                        @error('editCours.user_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-select">UE <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editCours.ue_id') is-invalid @enderror"
                            wire:model="editCours.ue_id" id="example-select" name="example-select">
                            <option selected value="0">Open this select menu</option>
                            @foreach ($ues as $ue)
                                <option value="{{ $ue->id }}">{{ $ue->nom }}</option>
                            @endforeach
                        </select>

                        @error('editCours.user_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    

                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
