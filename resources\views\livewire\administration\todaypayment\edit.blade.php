<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListPay()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListPay()">Liste des paiments</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Edition Historique de payment
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="updatePay()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire d'édition</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Etudiant : </label>
                        {{ $current_user->nom }} {{ $current_user->prenom }}
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Paiment<span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editPay.type_payment_id') is-invalid @enderror"
                            wire:model="editPay.type_payment_id" id="example-select1" name="example-select1">
                            <option selected value="null">Type de paiment</option>
                            @foreach ($types as $type)
                                <option value="{{ $type->id }}">{{ $type->nom }}
                                </option>
                            @endforeach
                        </select>

                        @error('editPay.type_payment_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Moyen<span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editPay.moyen_payment_id') is-invalid @enderror"
                            wire:model="editPay.moyen_payment_id" id="example-select1" name="example-select1">
                            <option selected value="null">Moyen de paiment</option>
                            @foreach ($moyens as $moyen)
                                <option value="{{ $moyen->id }}">{{ $moyen->nom }}
                                </option>
                            @endforeach
                        </select>

                        @error('editPay.moyen_payment_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Code<span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editPay.code"
                            class="form-control @error('editPay.code') is-invalid @enderror" id="example-text-input"
                            name="firstname" placeholder="Text Input">

                        @error('editPay.code')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Montant<span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editPay.montant"
                            class="form-control @error('editPay.montant') is-invalid @enderror" id="example-text-input"
                            name="lastname" placeholder="Text Input">

                        @error('editPay.montant')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>



                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
