 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Information sur les étudiants
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Etudiants
             </h3>

         </div>
         <div class="block-content block-content-full">

             <div class="table-responsive">
                 <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                 <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                     <thead>
                         <tr>
                             <th class="text-center" style="width: 50px;">#</th>
                             <th class="text-center">Téléphone</th>
                             <th class="text-center">Nom</th>
                             <th class="text-center">Prénom</th>
                             <th class="text-center">Date de naissance</th>
                             <th class="text-center">Lieu de naissance</th>
                             <th class="text-center">Adresse</th>
                             <th class="text-center">Matricule</th>
                             <th class="text-center">Domaine</th>
                             <th class="text-center">Parcours</th>
                             <th class="text-center">Niveau</th>
                             <th class="text-center">Nom_père</th>
                             <th class="text-center">Nom_mère</th>
                         </tr>
                     </thead>
                     <tbody>

                         @foreach ($etus as $etu)
                             <tr>
                                 <td class="text-center">{{ $loop->iteration }}</td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu->telephone1 }} @if ($etu->telephone2 != null)
                                         {{ $etu->telephone2 }}
                                     @endif
                                 </td>
                                 <td class="fw-semibold">
                                     {{ $etu->nom }}
                                 </td>
                                 <td class="fw-semibold">
                                     {{ $etu->prenom }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu->date_naissance }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu->lieu_naissance }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu->adresse }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu->matricule }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     @if ($etu->parcours == null)
                                         Pas de parcours
                                     @else
                                         {{ $etu->parcours->mention->domaine->nom }}
                                     @endif
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     @if ($etu->parcours == null)
                                         Pas de parcours
                                     @else
                                         {{ $etu->parcours->sigle }}
                                     @endif
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                         {{ $etu->niveau->sigle }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu->nom_pere }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu->nom_mere }}
                                 </td>
                                 {{--                              
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->niveau->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->annee->nom }}
                             </td> --}}

                             </tr>
                         @endforeach



                     </tbody>
                 </table>


             </div>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
