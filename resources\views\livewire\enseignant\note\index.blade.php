@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
@endsection

<div>
    <!-- Hero -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h4 fw-bold mb-2">
                    Gestion des notes - {{ $current_matiere->nom }} ({{ $current_matiere->ue->niveau->nom }})
                </h1>
                
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-alt-primary" wire:click="addNote" wire:loading.attr="disabled">
                        <i class="fa fa-save me-1" wire:loading.remove wire:target="addNote"></i>
                        <div wire:loading wire:target="addNote" class="spinner-border spinner-border-sm text-light me-2" role="status"></div>
                        Enregistrer toutes les notes
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Dynamic Table Full -->
        <div class="block block-rounded">
            <div class="block-content block-content-full">
                <div class="row mb-4">
                    <!-- Type de note -->
                    <div class="col-md-3">
                        <label class="form-label" for="type-note">Type d'évaluation</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fa fa-list-alt"></i>
                            </span>
                            <select class="form-select @error('noteType') is-invalid @enderror" 
                                    wire:model="noteType" id="type-note">
                                @foreach ($typeNotes as $typeNote)
                                    <option value="{{ $typeNote->id }}">{{ $typeNote->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <!-- Recherche -->
                    <div class="col-md-4">
                        <label class="form-label" for="search-student">Rechercher un étudiant</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fa fa-search"></i>
                            </span>
                            <input type="text" class="form-control" 
                                   wire:model.debounce.300ms="searchTerm" 
                                   id="search-student" placeholder="Nom ou prénom...">
                        </div>
                    </div>
                    
                    <!-- Application en masse -->
                    <div class="col-md-5">
                        <label class="form-label">Application en masse</label>
                        <div class="input-group">
                            <input type="number" class="form-control" wire:model.defer="bulkNote" 
                                   placeholder="Note à appliquer" min="0" max="20" step="0.25">
                            <button type="button" class="btn btn-primary" 
                                    wire:click="applyBulkNote"
                                    @if(empty($selectedStudents)) disabled @endif>
                                <i class="fa fa-check me-1"></i> Appliquer
                            </button>
                            <button type="button" class="btn btn-secondary"
                                    wire:click="selectAllStudents">
                                @if(count($selectedStudents) === count($noteEtus))
                                    <i class="fa fa-square-o me-1"></i> Désélectionner tout
                                @else
                                    <i class="fa fa-check-square-o me-1"></i> Sélectionner tout
                                @endif
                            </button>
                        </div>
                        <div class="form-text">
                            {{ count($selectedStudents) }} étudiant(s) sélectionné(s)
                        </div>
                    </div>
                </div>

                <!-- Liste des étudiants -->
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-vcenter table-hover">
                        <thead>
                            <tr>
                                <th style="width: 42px;">
                                    <div class="form-check d-inline-block">
                                        <input class="form-check-input" type="checkbox" 
                                                @if(count($selectedStudents) === count($noteEtus) && count($noteEtus) > 0) checked @endif
                                                wire:click="selectAllStudents">
                                    </div>
                                </th>
                                <th>Étudiant</th>
                                <th class="text-center" style="width: 100px;">Note</th>
                                <th class="text-center" style="width: 200px;">Observation</th>
                                <th class="text-center" style="width: 100px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (count($noteEtus) === 0)
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">Aucun étudiant trouvé</div>
                                    </td>
                                </tr>
                            @else
                                @foreach ($noteEtus as $index => $noteEtu)
                                    <tr>
                                        <td>
                                            <div class="form-check d-inline-block">
                                                <input class="form-check-input" type="checkbox" 
                                                      wire:click="toggleSelectStudent({{$index}})"
                                                      @if(in_array($index, $selectedStudents)) checked @endif>
                                            </div>
                                        </td>
                                        <td class="fw-semibold">
                                            @if ($isAdded)
                                                {{ $noteEtu['user']['nom'] }} {{ $noteEtu['user']['prenom'] }}
                                            @else
                                                {{ $noteEtu['nom'] }} {{ $noteEtu['prenom'] }}
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <div class="input-group input-group-sm">
                                                <input type="number" 
                                                       @if ($isAdded && !$isEditable) readonly @endif
                                                       @if ($isAdded)
                                                       wire:model.lazy="noteEtus.{{ $index }}.valeur"
                                                       @else
                                                       wire:model.lazy="noteEtus.{{ $index }}.note"
                                                       @endif
                                                       class="form-control text-center @error('noteEtus.' . $index . '.note') is-invalid @enderror"
                                                       min="0" max="20" step="0.25" placeholder="0-20">
                                            </div>
                                            @error('noteEtus.' . $index . '.note')
                                                <span class="text-danger small">{{ $message }}</span>
                                            @enderror
                                        </td>
                                        <td class="text-center">
                                            <div class="input-group input-group-sm">
                                                <input type="text" 
                                                       @if ($isAdded && !$isEditable) readonly @endif
                                                       @if ($isAdded)
                                                       wire:model.lazy="noteEtus.{{ $index }}.observation"
                                                       @else
                                                       wire:model.lazy="noteEtus.{{ $index }}.observation"
                                                       @endif
                                                       class="form-control" 
                                                       placeholder="Observation...">
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            @if ($isAdded && $isEditable)
                                                <button type="button" class="btn btn-sm btn-primary" 
                                                        wire:click="updateNote({{$index}})" 
                                                        wire:loading.attr="disabled" 
                                                        title="Enregistrer cette note">
                                                    <i class="fa fa-save"></i>
                                                </button>
                                            @elseif (!$isAdded)
                                                <button type="button" class="btn btn-sm btn-primary" 
                                                        wire:click="saveNote({{$index}})" 
                                                        wire:loading.attr="disabled"
                                                        title="Enregistrer cette note">
                                                    <i class="fa fa-save"></i>
                                                </button>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                </div>

                <!-- Pagination (à implémenter si nécessaire) -->
                <div class="row mt-3">
                    <div class="col-sm-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <select class="form-select form-select-sm" wire:model="perPage">
                                    <option value="10">10 par page</option>
                                    <option value="25">25 par page</option>
                                    <option value="50">50 par page</option>
                                    <option value="100">100 par page</option>
                                </select>
                            </div>
                            <div>
                                <!-- Pagination controls would go here if implemented -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END Dynamic Table Full -->
    </div>
    <!-- END Page Content -->
</div>

<script>
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    });
</script>