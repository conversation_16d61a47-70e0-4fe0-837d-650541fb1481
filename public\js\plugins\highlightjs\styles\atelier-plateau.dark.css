/* Base16 Atelier Plateau Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/plateau) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON>on/base16) */

/* Atelier-Plateau Comment */
.hljs-comment {
  color: #7e7777;
}

/* Atelier-Plateau Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.hljs-name,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #ca4949;
}

/* Atelier-Plateau Orange */
.hljs-number,
.hljs-preprocessor,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #b45a3c;
}

/* Atelier-Plateau Yellow */
.ruby .hljs-class .hljs-title,
.css .hljs-rule .hljs-attribute {
  color: #a06e3b;
}

/* Atelier-Plateau Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #4b8b8b;
}

/* Atelier-Plateau Aqua */
.hljs-title,
.css .hljs-hexcolor {
  color: #5485b6;
}

/* Atelier-Plateau Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #7272ca;
}

/* Atelier-Plateau Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #8464c4;
}

.diff .hljs-deletion,
.diff .hljs-addition {
  color: #1b1818;
  display: inline-block;
  width: 100%;
}

.diff .hljs-deletion {
  background-color: #ca4949;
}

.diff .hljs-addition {
  background-color: #4b8b8b;
}

.diff .hljs-change {
  color: #7272ca;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #1b1818;
  color: #8a8585;
  padding: 0.5em;
  -webkit-text-size-adjust: none;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
