 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Utilisateurs
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Utilisateurs
             </h3>
             <div class="block-options">
                 <a class="btn btn-sm btn-primary me-1" wire:click="goToAddUser()">
                     <i class="fa fa-fw fa-user-plus me-1"></i> Nouvel Utilisateur
                 </a>
             </div>
         </div>
         <div class="block-content block-content-full">

             <div class="row">
                 <div class="col-sm-12 col-md-6">
                         <label>
                             <select name="DataTables_Table_4_length" class="form-select form-select-sm">
                                 <option value="5">5</option>
                                 <option value="10">10</option>
                                 <option value="15">15</option>
                                 <option value="20">20</option>
                             </select>
                         </label>
                     
                 </div>
                 <div class="col-sm-12 col-md-6 text-end">
                     
                         <label>
                             <input type="search" wire:model="query" class="form-control form-control-sm" placeholder="Search..">
                         </label>
                     
                 </div>
             </div>

             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-borderless table-striped table-vcenter">
                 <thead>
                     <tr>
                         <th class="text-center" style="width: 50px;">#</th>
                         <th>Nom et Prénom</th>
                         <th class="d-none d-sm-table-cell" style="width: 30%;">Email</th>
                         <th class="d-none d-sm-table-cell" style="width: 10%;">Rôle</th>
                         <th class="d-none d-sm-table-cell" style="width: 10%;">Ajouté</th>
                         <th class="text-center" style="width: 100px;">Actions</th>
                     </tr>
                 </thead>
                 <tbody>

                     @foreach ($users as $user)
                         <tr>
                             <td class="text-center">{{ $user->id }}</td>
                             <td class="fw-semibold">
                                 {{ $user->nom }} {{ $user->prenom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $user->email }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $user->allRoleNames }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $user->created_at->diffForHumans() }}
                             </td>
                             <td class="text-center">
                                 <div class="btn-group">
                                     <button type="button" class="btn btn-sm btn-alt-secondary" title="Edit"
                                         wire:click="goToEditUser({{ $user->id }})">
                                         <i class="fa fa-fw fa-pencil-alt"></i>
                                     </button>
                                     <button type="button" class="btn btn-sm btn-alt-secondary" title="Delete" onclick="confirm('Êtes-vous sûrs?') || event.stopImmediatePropagation()"
                                         wire:click="deleteUser({{ $user->id }})">
                                         <i class="fa fa-fw fa-times"></i>
                                     </button>
                                 </div>
                             </td>
                         </tr>
                     @endforeach



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     {{ $users->links() }}
                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
