 @section('js')
     <!-- jQuery (required for DataTables plugin) -->
     <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
 @endsection

 <div wire:ignore.self>
     <!-- Hero -->
     <div class="bg-body-light">
         <div class="content content-full">

             <h1 class="h3 fw-bold mb-2">
                 Modification de note
             </h1>

             <div class="flex-grow-1">
                 <h1 class="h3 fw-bold mb-2">
                     <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListNote()">
                         <i class="si si-arrow-left fa-2x"></i>
                     </button>
                 </h1>
             </div>

         </div>
     </div>
     <!-- END Hero -->


     <!-- Page Content -->
     <div class="content">
         <form method="POST" role="form" wire:submit.prevent="updateNote()" enctype="multipart/form-data">

             <!-- Dynamic Table Full -->
             <div class="block block-rounded">

                 <div class="block-content block-content-full">
                     <div class="row g-4">
                         <div class="col-6">
                             <label class="form-label" for="example-text-input">Etudiant : </label>
                             {{ $current_user->nom }} {{ $current_user->prenom }}
                         </div>
                         <div class="col-6">
                             <label class="form-label" for="example-text-input">Valeur<span
                                     class="text-danger">*</span></label>
                             <input type="text" wire:model="editNote.valeur"
                                 class="form-control @error('editNote.valeur') is-invalid @enderror"
                                 id="example-text-input" placeholder="Text Input">

                             @error('editNote.valeur')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>
                         <div class="col-6">
                             <label class="form-label" for="example-text-input">Id History<span
                                     class="text-danger">*</span></label>
                             <input type="text" wire:model="editNote.history_note_id"
                                 class="form-control @error('editNote.history_note_id') is-invalid @enderror"
                                 id="example-text-input" placeholder="Text Input">

                             @error('editNote.history_note_id')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>


                         <div class="col-6">
                             <label class="form-label" for="example-select">Parcours <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('parcour') is-invalid @enderror" wire:model="parcour"
                                 id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($parcours as $parc)
                                     <option value="{{ $parc->id }}">{{ $parc->sigle }}
                                     </option>
                                 @endforeach
                             </select>

                             @error('parcour')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>

                         <div class="col-6">
                             <label class="form-label" for="example-select">Matiere <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('matiere') is-invalid @enderror" wire:model="matiere"
                                 id="example-select" name="example-select">
                                 @if ($parcours->isEmpty())
                                     <option value="">-- choose parcours first --</option>
                                 @endif
                                 @foreach ($matieres as $product)
                                     <option value="{{ $product->id }}">{{ $product->nom }}</option>
                                 @endforeach
                             </select>

                             @error('matiere')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>


                         <div>
                             <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                         </div>
                     </div>
                 </div>
                 <!-- END Dynamic Table Full -->


             </div>
             <!-- END Page Content -->
         </form>
     </div>
