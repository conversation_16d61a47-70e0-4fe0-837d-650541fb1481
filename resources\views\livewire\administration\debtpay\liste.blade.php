 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Paiments Incomplets
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">
     <form method="POST" role="form" wire:submit.prevent="goToEtat()" enctype="multipart/form-data">
         <!-- Dynamic Table Full -->
         <div class="block block-rounded">

             <div class="block-content">
                 <div class="row justify-content-center py-sm-3 py-md-5">
                     <div class="col-sm-10 col-md-8">
                         {{-- <div class="mb-4">
                             <label class="form-label" for="example-select">Parcours <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('newEtus.parcour_id') is-invalid @enderror"
                                 wire:model="newEtus.parcour_id" id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 <option value="100">Tous les parcours</option>
                                 @foreach ($parcours as $parcour)
                                     <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                 @endforeach
                             </select>

                             @error('newEtus.parcour_id')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div> --}}


                         <div class="mb-4">
                             <label class="form-label" for="example-select">Niveau <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('newEtus.niveau_id') is-invalid @enderror"
                                 wire:model="newEtus.niveau_id" id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($niveaux as $niveau)
                                     <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                 @endforeach
                             </select>

                             @error('newEtus.niveau_id')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>
                         
                         {{-- <div class="mb-4 d-flex">
                             <label class="form-label" for="example-select">Fusioner paiment (facultatif)</label>
                             <div class="me-3">

                                 <select class="form-select @error('type1') is-invalid @enderror"
                                     wire:model="type1" id="example-select" name="example-select">
                                     <option selected value="0">Open this select menu</option>
                                     @foreach ($types as $type)
                                         <option value="{{ $type->id }}">{{ $type->nom }}</option>
                                     @endforeach
                                 </select>

                                 @error('type1')
                                     <span class="text-danger">{{ $message }}</span>
                                 @enderror
                             </div>
                             <div class="">

                                 <select class="form-select @error('type2') is-invalid @enderror"
                                     wire:model="type2" id="example-select" name="example-select">
                                     <option selected value="0">Open this select menu</option>
                                     @foreach ($types as $type)
                                         <option value="{{ $type->id }}">{{ $type->nom }}</option>
                                     @endforeach
                                 </select>

                                 @error('type2')
                                     <span class="text-danger">{{ $message }}</span>
                                 @enderror
                             </div>
                         </div> --}}

                         <div class="mb-4">
                             <label class="form-label" for="example-select">Année Universitaire <span
                                     class="text-danger">*</span></label>
                             <select
                                 class="form-select @error('newEtus.annee_universitaire_id') is-invalid @enderror"
                                 wire:model="newEtus.annee_universitaire_id" id="example-select"
                                 name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($annees as $annee)
                                     <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                 @endforeach
                             </select>

                             @error('newEtus.annee_universitaire_id')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>
                         <div class="mb-4">
                             <button type="submit" class="btn btn-primary mb-3">Générer</button>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
         <!-- END Dynamic Table Full -->
     </form>

 </div>
 <!-- END Page Content -->
