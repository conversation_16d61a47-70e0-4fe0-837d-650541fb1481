<div class="block block-rounded">

                <div class="block-content block-content-full">
                    <div class="table-responsive">
                        <table class="mt-5 table table-bordered table-striped table-vcenter">
                            <thead>
                                <tr>
                                    <th class="text-center">Type</th>
                                    <th class="text-center">Moyen</th>
                                    <th class="text-center">Code</th>
                                    <th class="text-center">Montant</th>
                                    <th class="text-center">Observation</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($etatPayment as $payment)
                                    <tr>
                                        @if ($payment->historyPay->where('user_id', $current_user->id)->where('annee_universitaire_id', $current_annee->id)->isNotEmpty())
                                            {{-- @dump($payment->niveau) --}}
                                            <td class="fw-semibold">
                                                <label class="form-label"
                                                    for="example-text-input">{{ $payment->nom }}</label>
                                            </td>
                                            @php
                                                $total = 0;
                                                
                                            @endphp

                                            @foreach ($payment->historyPay->where('user_id', $current_user->id)->where('annee_universitaire_id', $current_annee->id)->where('type_payment_id', $payment->id) as $histo)
                                                @php
                                                    $ini = $payment->niveau->first()->pivot->prix;
                                                    $total += $histo->montant;
                                                    $i = $loop->parent->iteration . $loop->parent->iteration;
                                                @endphp
                                                <td class="text-center">
                                                    {{ $histo->moyen->nom }}
                                                    @if ($ini != $total)
                                                        <select
                                                            class="form-select @error('newPay.' . $i . '.moyen') is-invalid @enderror"
                                                            wire:model="newPay.{{ $i }}.moyen"
                                                            id="example-select1" name="example-select1">
                                                            <option selected value="null">Moyen de paiment</option>
                                                            @foreach ($moyens as $moyen)
                                                                <option value="{{ $moyen->id }}">{{ $moyen->nom }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                    @endif
                                                </td>
                                                <td class="text-center">
                                                    {{ $histo->code }}
                                                    @if ($ini != $total)
                                                        <input type="text"
                                                            wire:model="newPay.{{ $i }}.code"
                                                            class="form-control @error('newPay.' . $i . '.code') is-invalid @enderror"
                                                            id="example-text-input" name="example-text-input"
                                                            placeholder="Entrez le code du reçu">
                                                    @endif
                                                </td>
                                                <td class="text-center">
                                                    {{ $histo->montant }}
                                                    @if ($ini != $total)
                                                        <input type="number"
                                                            wire:model="newPay.{{ $i }}.montant"
                                                            class="form-control @error('newPay.' . $i . '.montant') is-invalid @enderror"
                                                            id="example-text-input" name="address"
                                                            placeholder="Entrez le montant">
                                                    @endif
                                                </td>
                                                <td class="text-center">
                                                    {{ $histo->created_at }}
                                                    @if ($ini != $total)
                                                        <button type="button"
                                                            wire:click="valider({{ $payment->id }}, {{ $i }})"
                                                            class="btn btn-sm btn-alt-primary"
                                                            @disabled(empty($newPay[$i]))>Valider
                                                        </button>
                                                    @endif
                                                </td>
                                                @dd($total, $ini)
                                            @endforeach
                                        @else
                                            <td class="fw-semibold">
                                                <label class="form-label"
                                                    for="example-text-input">{{ $payment->nom }}<span
                                                        class="text-danger">*</span></label>
                                            </td>
                                            <td class="text-center">
                                                <select
                                                    class="form-select @error('newPay.' . $loop->index . '.moyen') is-invalid @enderror"
                                                    wire:model="newPay.{{ $loop->index }}.moyen" id="example-select1"
                                                    name="example-select1">
                                                    <option selected value="null">Moyen de paiment</option>
                                                    @foreach ($moyens as $moyen)
                                                        <option value="{{ $moyen->id }}">{{ $moyen->nom }}
                                                        </option>
                                                    @endforeach
                                                </select>


                                            </td>
                                            <td class="text-center">
                                                <input type="text" wire:model="newPay.{{ $loop->index }}.code"
                                                    class="form-control @error('newPay.' . $loop->index . '.code') is-invalid @enderror"
                                                    id="example-text-input" name="example-text-input"
                                                    placeholder="Entrez le code du reçu">


                                            </td>
                                            <td class="text-center">
                                                <input type="number" wire:model="newPay.{{ $loop->index }}.montant"
                                                    class="form-control @error('newPay.' . $loop->index . '.montant') is-invalid @enderror"
                                                    id="example-text-input" name="address"
                                                    placeholder="Entrez le montant">


                                            </td>
                                            <td class="text-center">
                                                <button type="button"
                                                    wire:click="valider({{ $payment->id }}, {{ $loop->index }})"
                                                    class="btn btn-sm btn-alt-primary"
                                                    @disabled(empty($newPay[$loop->index]))>Valider
                                                </button>
                                            </td>
                                        @endif
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>


                    {{-- <div class="row g-4">
                @foreach ($etatPayment as $payment)
                    @dump($payment->historyPay)
                @if ($payment->historyPay->where('user_id', $current_user->id)->where('annee_universitaire_id',         $current_annee->id)->isNotEmpty())
                <div class="col-md-2">
                    <label class="form-label" for="example-text-input">{{ $payment->nom }}</label>
                </div>
                @foreach ($payment->historyPay->where('user_id', $current_user->id)->where('annee_universitaire_id', $current_annee->id)->where('type_payment_id', $payment->id) as $histo)
                    <div class="col-md-2">
                        {{ $histo->moyen->nom }}
                    </div>
                    <div class="col-md-3">
                        {{ $histo->code }}
                    </div>
                    <div class="col-md-3">
                        {{ $histo->montant }}
                    </div>
                    <div class="col-md-2">
                        Payé
                    </div>
                @endforeach
                @else
                <div class="col-md-2">
                    <label class="form-label" for="example-text-input">{{ $payment->nom }}<span
                            class="text-danger">*</span></label>
                </div>
                <div class="col-md-2">
                    <select class="form-select @error('newUser.moyen') is-invalid @enderror"
                        wire:model="newPay.{{ $loop->index }}.moyen" id="example-select1" name="example-select1">
                        <option selected value="null">Moyen de paiment</option>
                        @foreach ($moyens as $moyen)
                            <option value="{{ $moyen->id }}">{{ $moyen->nom }}</option>
                        @endforeach
                    </select>

                    @error('newPay.moyen')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <div class="col-md-3">
                    <input type="text" wire:model="newPay.{{ $loop->index }}.code"
                        class="form-control @error('newPay.code') is-invalid @enderror" id="example-text-input"
                        name="example-text-input" placeholder="Entrez le code du reçu">

                    @error('newPay.code')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <div class="col-md-3">
                    <input type="number" wire:model="newPay.{{ $loop->index }}.montant"
                        class="form-control @error('newPay.montant') is-invalid @enderror" id="example-text-input"
                        name="address" placeholder="Entrez le montant">

                    @error('newPay.montant')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-primary mb-3"
                        wire:click="valider({{ $loop->index }})">Valider</button>
                </div>
                @endif
                @endforeach --}}

                    {{-- <div class="col-6">
                        <label class="form-label" for="example-text-input">{{ $payment->nom }} <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="montant"
                            class="form-control @error('montant') is-invalid @enderror" id="example-text-input"
                            name="firstname" placeholder="Text Input">

                        @error('montant')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div> --}}



                    {{-- </div> --}}
                </div>
            </div>

            <div class="row justify-content-center py-sm-3 py-md-5">
                                <div class="col-sm-10 col-md-8">
                                    <div class="mb-4">
                                        <label class="form-label" for="block-form1-username">Username</label>
                                        <input type="text" class="form-control form-control-alt"
                                            id="block-form1-username" name="block-form1-username"
                                            placeholder="Enter your username..">
                                    </div>
                                    <div class="mb-4">
                                        <label class="form-label" for="block-form1-password">Password</label>
                                        <input type="password" class="form-control form-control-alt"
                                            id="block-form1-password" name="block-form1-password"
                                            placeholder="Enter your password..">
                                    </div>
                                    <div class="mb-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value=""
                                                id="block-form1-remember-me" name="block-form1-remember-me">
                                            <label class="form-check-label" for="block-form1-remember-me">Remember
                                                Me?</label>
                                        </div>
                                    </div>
                                </div>
                            </div>