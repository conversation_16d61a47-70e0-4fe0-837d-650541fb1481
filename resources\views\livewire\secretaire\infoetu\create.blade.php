<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListUser()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListUser()">Liste des étudiants</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Remplissage d'informations
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">

    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">Validation des payments</h3>
            {{-- <div class="block-options">
                    <button type="button" class="btn-block-option">
                        <i class="si si-settings"></i>
                    </button>
                </div> --}}
        </div>
        <div class="block-content">
            <div class="table-responsive mb-3">
                <table class="table table-bordered table-striped table-vcenter">
                    <thead>
                        <tr>
                            <th>Motif</th>
                            <th>Montant</th>
                            <th class="text-center" style="width: 100px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach ($payments as $payment)
                            <tr>
                                <td class="fw-semibold">
                                    {{ $payment->payment->nom }}
                                </td>
                                <td class="fw-semibold">
                                    {{ $payment->prixForHumans }}
                                </td>

                                <td class="text-center">
                                    @if ($payment->is_valid_sec)
                                        <i class="fa fa-fw fa-circle-check text-success"></i>
                                    @else
                                        <button type="button" wire:click="valider({{ $payment->id }})"
                                            class="btn btn-sm btn-alt-secondary">Valider
                                        </button>
                                    @endif
                                </td>
                            </tr>
                        @endforeach



                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="updateUser()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire de remplissage</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editUser.nom"
                            class="form-control @error('editUser.nom') is-invalid @enderror" id="example-text-input"
                            name="firstname" placeholder="Text Input">

                        @error('editUser.nom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Prénom <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editUser.prenom"
                            class="form-control @error('editUser.prenom') is-invalid @enderror" id="example-text-input"
                            name="lastname" placeholder="Text Input">

                        @error('editUser.prenom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-select1">Niveau<span class="text-danger">*</span></label>
                        <select class="form-select @error('editUser.niveau_id') is-invalid @enderror"
                            wire:model="editUser.niveau_id" id="example-select1" name="example-select1">
                            <option selected value="null">Ouvrez le menu déroulant</option>
                            @foreach ($niveaux as $niveau)
                                <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                            @endforeach
                        </select>

                        @error('editUser.niveau_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-select1">Parcours<span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('editUser.parcour_id') is-invalid @enderror"
                            wire:model="editUser.parcour_id" id="example-select1" name="example-select1">
                            <option selected value="null">Ouvrez le menu déroulant</option>
                            @foreach ($parcours as $parcour)
                                <option value="{{ $parcour->id }}">{{ $parcour->nom }}</option>
                            @endforeach
                        </select>

                        @error('editUser.parcour_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-select">Sexe <span class="text-danger">*</span></label>
                        <select class="form-select @error('editUser.sexe') is-invalid @enderror"
                            wire:model="editUser.sexe" id="example-select" name="example-select">
                            <option selected value="">Open this select menu</option>
                            <option value="H">Homme</option>
                            <option value="F">Femme</option>
                        </select>

                        @error('editUser.sexe')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Matricule</label>
                        <input type="text" wire:model="editUser.matricule"
                            class="form-control @error('editUser.matricule') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.matricule')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Date de naissance <span
                                class="text-danger">*</span></label>
                        <input type="text" onchange='Livewire.emit("selectDate", this.value)' wire:model="editUser.date_naissance"
                            class="js-datepicker form-control @error('editUser.date_naissance') is-invalid @enderror"
                            id="example-text-input" name="birthday" data-week-start="1" data-autoclose="true"
                            data-today-highlight="true" data-date-format="dd/mm/yy" placeholder="dd/mm/yy">

                        @error('editUser.date_naissance')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Lieu de naissance <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editUser.lieu_naissance"
                            class="form-control @error('editUser.lieu_naissance') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.lieu_naissance')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-email-input">Email</label>
                        <input type="email" wire:model="editUser.email"
                            class="form-control @error('editUser.email') is-invalid @enderror"
                            id="example-email-input" name="example-email-input" placeholder="Email Input">

                        @error('editUser.email')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone</label>
                        <input type="text" wire:model="editUser.telephone1"
                            class="form-control @error('editUser.telephone1') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.telephone1')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Autre Telephone</label>
                        <input type="text" wire:model="editUser.telephone2"
                            class="form-control @error('editUser.telephone2') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.telephone2')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Adresse <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editUser.adresse"
                            class="form-control @error('editUser.adresse') is-invalid @enderror"
                            id="example-text-input" name="address" placeholder="Text Input">

                        @error('editUser.adresse')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">N° CNI</label>
                        <input type="text" wire:model="editUser.cin"
                            class="form-control @error('editUser.cin') is-invalid @enderror" id="example-text-input"
                            name="example-text-input" placeholder="Text Input">

                        @error('editUser.cin')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Date de délivrance</label>
                        <input type="text" wire:model="editUser.date_delivrance"
                            class="form-control @error('editUser.date_delivrance') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.date_delivrance')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Lieu de délivrance</label>
                        <input type="text" wire:model="editUser.lieu_delivrance"
                            class="form-control @error('editUser.lieu_delivrance') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.lieu_delivrance')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Duplicata</label>
                        <input type="text" wire:model="editUser.duplicata"
                            class="form-control @error('editUser.duplicata') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.duplicata')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom du père</label>
                        <input type="text" wire:model="editUser.nom_pere"
                            class="form-control @error('editUser.nom_pere') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.nom_pere')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom de la mère</label>
                        <input type="text" wire:model="editUser.nom_mere"
                            class="form-control @error('editUser.nom_mere') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.nom_mere')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone du père</label>
                        <input type="text" wire:model="editUser.tel_pere"
                            class="form-control @error('editUser.tel_pere') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.tel_pere')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone de la mère</label>
                        <input type="text" wire:model="editUser.tel_mere"
                            class="form-control @error('editUser.tel_mere') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.tel_mere')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom Tuteur</label>
                        <input type="text" wire:model="editUser.nom_tuteur"
                            class="form-control @error('editUser.nom_tuteur') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.nom_tuteur')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone du Tuteur</label>
                        <input type="text" wire:model="editUser.tel_tuteur"
                            class="form-control @error('editUser.tel_tuteur') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('editUser.tel_tuteur')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    



                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
