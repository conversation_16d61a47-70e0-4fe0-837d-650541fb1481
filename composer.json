{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "barryvdh/laravel-dompdf": "^2.0", "doctrine/dbal": "^3.6", "dyrynda/laravel-cascade-soft-deletes": "^4.2", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^9.19", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "laravel/ui": "^4.2", "livewire/livewire": "^2.11", "maatwebsite/excel": "^3.1", "phpoffice/phpspreadsheet": "^1.29", "staudenmeir/belongs-to-through": "^2.5", "staudenmeir/eloquent-has-many-deep": "^1.7"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "beyondcode/laravel-query-detector": "^1.7", "fakerphp/faker": "^1.9.1", "laravel-lang/attributes": "^2.2", "laravel-lang/lang": "^12.17", "laravel-lang/publisher": "^14.6", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}