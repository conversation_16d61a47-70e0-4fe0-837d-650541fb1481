@section('js')
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
@endsection

<div>
    @if ($viewMode === 'courses')
        <!-- Section Liste des Cours -->
        <div class="bg-body-light">
            <div class="content content-full">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 fw-bold mb-0">Gestion des Notes</h1>
                    <button wire:click="toggleFilters" class="btn btn-sm btn-alt-primary">
                        <i class="fa fa-filter me-1"></i> {{ $showFilters ? 'Masquer' : 'Afficher' }} les filtres
                    </button>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">Liste des Cours</h3>
                </div>
                
                <div class="block-content block-content-full">
                    @if ($showFilters)
                        <div class="row mb-4 bg-body-light p-3 rounded">
                            <div class="col-md-8">
                                <div class="row g-2">
                                    <div class="col-6 col-md-3">
                                        <label class="form-label small">Parcours</label>
                                        <select wire:model="filtreParcours" class="form-select form-select-sm">
                                            <option value="">Tous</option>
                                            @foreach ($parcours as $parcour)
                                                <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <label class="form-label small">Niveau</label>
                                        <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                            <option value="">Tous</option>
                                            @foreach ($niveaux as $niveau)
                                                <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <label class="form-label small">Année</label>
                                        <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                            <option value="">Toutes</option>
                                            @foreach ($annees as $annee)
                                                <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <label class="form-label small">Enseignant</label>
                                        <select wire:model="enseignantId" class="form-select form-select-sm">
                                            <option value="">Tous</option>
                                            @foreach ($enseignants as $enseignant)
                                                <option value="{{ $enseignant->id }}">{{ $enseignant->nom }} {{ $enseignant->prenom }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 d-flex align-items-end justify-content-end">
                                <div class="input-group input-group-sm">
                                    <input type="search" wire:model="query" class="form-control form-control-sm" 
                                        placeholder="Rechercher...">
                                    <button type="button" wire:click="resetFilters" class="btn btn-secondary">
                                        <i class="fa fa-redo"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="input-group mb-3">
                                    <input type="search" wire:model="query" class="form-control" 
                                        placeholder="Rechercher un cours...">
                                    <button class="btn btn-outline-secondary" type="button">
                                        <i class="fa fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-hover table-vcenter">
                            <thead>
                                <tr>
                                    <th>Nom du cours</th>
                                    <th>Enseignant</th>
                                    <th>UE</th>
                                    <th>Parcours & Niveau</th>
                                    <th class="text-center" style="width: 100px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($cours as $cour)
                                    <tr class="cursor-pointer" style="cursor: pointer" wire:click="viewNotes({{ $cour->id }})">
                                        <td class="fw-semibold">
                                            {{ $cour->nom }}
                                        </td>
                                        <td>
                                            @if ($cour->user == null)
                                                <span class="text-muted">Non assigné</span>
                                            @else
                                                {{ $cour->user->nom }} {{ $cour->user->prenom }}
                                            @endif
                                        </td>
                                        <td>
                                            {{ $cour->ue->nom }}
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $cour->ue->parcours->sigle }}</span>
                                            <span class="badge bg-info">{{ $cour->ue->niveau->nom }}</span>
                                        </td>
                                        <td class="text-center">
                                            <button type="button" wire:click.stop="viewNotes({{ $cour->id }})" 
                                                class="btn btn-sm btn-alt-primary">
                                                <i class="fa fa-edit"></i> Gérer les notes
                                            </button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fa fa-search fa-2x mb-2"></i>
                                                <p>Aucun cours ne correspond à vos critères.</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-3">
                        {{ $cours->links() }}
                    </div>
                </div>
            </div>
        </div>
    @else
        @include('livewire.gestion-notes.editor')
    @endif
</div>

<script>
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    });
    
    window.addEventListener("showErrorMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'danger',
            icon: 'fa fa-times me-1',
            message: event.detail.message || 'Une erreur est survenue!'
        });
    });
    
    // Timer pour effacer les indicateurs de sauvegarde
    window.addEventListener("startSaveStatusTimer", event => {
        const userId = event.detail.userId;
        const timeout = event.detail.timeout || 3000;
        
        setTimeout(() => {
            @this.resetSaveStatus(userId);
        }, timeout);
    });
</script>