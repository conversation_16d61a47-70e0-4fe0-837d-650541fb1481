/*

FAR Style (c) MajestiC <<EMAIL>>

*/

.hljs {
  display: block; padding: 0.5em;
  background: #000080;
}

.hljs,
.hljs-subst {
  color: #0FF;
}

.hljs-string,
.ruby .hljs-string,
.haskell .hljs-type,
.hljs-tag .hljs-value,
.css .hljs-rules .hljs-value,
.css .hljs-rules .hljs-value .hljs-number,
.hljs-preprocessor,
.hljs-pragma,
.ruby .hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.hljs-built_in,
.sql .hljs-aggregate,
.django .hljs-template_tag,
.django .hljs-variable,
.smalltalk .hljs-class,
.hljs-addition,
.apache .hljs-tag,
.apache .hljs-cbracket,
.tex .hljs-command,
.clojure .hljs-title,
.coffeescript .hljs-attribute {
  color: #FF0;
}

.hljs-keyword,
.css .hljs-id,
.hljs-title,
.haskell .hljs-type,
.vbscript .hljs-built_in,
.sql .hljs-aggregate,
.rsl .hljs-built_in,
.smalltalk .hljs-class,
.xml .hljs-tag .hljs-title,
.hljs-winutils,
.hljs-flow,
.hljs-change,
.hljs-envvar,
.bash .hljs-variable,
.tex .hljs-special,
.clojure .hljs-built_in {
  color: #FFF;
}

.hljs-comment,
.hljs-phpdoc,
.hljs-javadoc,
.java .hljs-annotation,
.hljs-template_comment,
.hljs-deletion,
.apache .hljs-sqbracket,
.tex .hljs-formula {
  color: #888;
}

.hljs-number,
.hljs-date,
.hljs-regexp,
.hljs-literal,
.smalltalk .hljs-symbol,
.smalltalk .hljs-char,
.clojure .hljs-attribute {
  color: #0F0;
}

.python .hljs-decorator,
.django .hljs-filter .hljs-argument,
.smalltalk .hljs-localvars,
.smalltalk .hljs-array,
.hljs-attr_selector,
.hljs-pseudo,
.xml .hljs-pi,
.diff .hljs-header,
.hljs-chunk,
.hljs-shebang,
.nginx .hljs-built_in,
.hljs-prompt {
  color: #008080;
}

.hljs-keyword,
.css .hljs-id,
.hljs-title,
.haskell .hljs-type,
.vbscript .hljs-built_in,
.sql .hljs-aggregate,
.rsl .hljs-built_in,
.smalltalk .hljs-class,
.hljs-winutils,
.hljs-flow,
.apache .hljs-tag,
.nginx .hljs-built_in,
.tex .hljs-command,
.tex .hljs-special,
.hljs-request,
.hljs-status {
  font-weight: bold;
}
