<!-- Add Student Modal -->
<div class="modal fade" id="modal-add-student" tabindex="-1" role="dialog" aria-labelledby="modal-add-student-label" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-add-student-label">Ajouter un Nouvel Étudiant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" wire:click="closeAddStudentModal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form wire:submit.prevent="addUser">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="newUser.nom" class="form-label">Nom <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('newUser.nom') is-invalid @enderror" id="newUser.nom" wire:model.defer="newUser.nom" required>
                            @error('newUser.nom') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="col-md-6">
                            <label for="newUser.prenom" class="form-label">Prénom(s)</label>
                            <input type="text" class="form-control @error('newUser.prenom') is-invalid @enderror" id="newUser.prenom" wire:model.defer="newUser.prenom">
                             @error('newUser.prenom') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="newUser.telephone1" class="form-label">Téléphone <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control @error('newUser.telephone1') is-invalid @enderror" id="newUser.telephone1" wire:model.defer="newUser.telephone1" required>
                            @error('newUser.telephone1') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                         <div class="col-md-6">
                            <label for="newUser.email" class="form-label">Email</label>
                            <input type="email" class="form-control @error('newUser.email') is-invalid @enderror" id="newUser.email" wire:model.defer="newUser.email">
                            @error('newUser.email') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                     <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="newUser.sexe" class="form-label">Sexe <span class="text-danger">*</span></label>
                            <select class="form-select @error('newUser.sexe') is-invalid @enderror" id="newUser.sexe" wire:model.defer="newUser.sexe" required>
                                <option value="M">Masculin</option>
                                <option value="F">Féminin</option>
                            </select>
                             @error('newUser.sexe') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="col-md-4">
                            <label for="newUser.date_naissance" class="form-label">Date de Naissance</label>
                            <input type="text" class="form-control js-flatpickr @error('newUser.date_naissance') is-invalid @enderror" id="newUser.date_naissance" wire:model.defer="newUser.date_naissance" placeholder="YYYY-MM-DD">
                             @error('newUser.date_naissance') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                         <div class="col-md-4">
                            <label for="newUser.lieu_naissance" class="form-label">Lieu de Naissance</label>
                            <input type="text" class="form-control @error('newUser.lieu_naissance') is-invalid @enderror" id="newUser.lieu_naissance" wire:model.defer="newUser.lieu_naissance">
                             @error('newUser.lieu_naissance') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="newUser.adresse" class="form-label">Adresse</label>
                        <input type="text" class="form-control @error('newUser.adresse') is-invalid @enderror" id="newUser.adresse" wire:model.defer="newUser.adresse">
                        @error('newUser.adresse') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>

                    <hr>
                    <h6 class="mb-3">Informations Académiques</h6>

                    <div class="row mb-3">
                        <div class="col-md-6">
                             <label for="newUser.parcour_id" class="form-label">Parcours <span class="text-danger">*</span></label>
                             <select class="form-select @error('newUser.parcour_id') is-invalid @enderror" id="newUser.parcour_id" wire:model.defer="newUser.parcour_id" required>
                                 <option value="">Sélectionner...</option>
                                 @foreach($parcours as $parcour)
                                     <option value="{{ $parcour->id }}">{{ $parcour->nom }} ({{ $parcour->sigle }})</option>
                                 @endforeach
                             </select>
                             @error('newUser.parcour_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="col-md-6">
                             <label for="newUser.niveau_id" class="form-label">Niveau <span class="text-danger">*</span></label>
                             <select class="form-select @error('newUser.niveau_id') is-invalid @enderror" id="newUser.niveau_id" wire:model.defer="newUser.niveau_id" required>
                                 <option value="">Sélectionner...</option>
                                  @foreach($niveaux as $niveau)
                                     <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                 @endforeach
                             </select>
                             @error('newUser.niveau_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                    <!-- Add other fields as needed -->

                    <div class="modal-footer px-0 pb-0">
                        <button type="button" class="btn btn-alt-secondary" data-bs-dismiss="modal" wire:click="closeAddStudentModal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            <span wire:loading wire:target="addUser" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
