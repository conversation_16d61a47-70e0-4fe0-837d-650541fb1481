<div>
    <!-- Hero Section with Enhanced Design -->
    <div class="bg-body-light border-bottom">
        <div class="content content-full">
            <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
                <div class="flex-grow-1">
                    <h1 class="h2 fw-bold mb-2 text-primary">
                        <i class="fa fa-chart-bar me-2 opacity-75"></i>
                        Gestion des résultats
                    </h1>
                    <p class="fs-sm text-muted mb-0">
                        Générez et consultez les résultats académiques par parcours, niveau et semestre
                    </p>
                </div>
            </div>

            <!-- Enhanced Filter Form -->
            <div class="block block-rounded shadow-sm mt-4">
                <div class="block-header block-header-default bg-primary-light">
                    <h3 class="block-title text-primary">
                        <i class="fa fa-filter me-1"></i>
                        Critères de sélection
                    </h3>
                </div>
                <div class="block-content">
                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <!-- Parcours (Multi-select) -->
                            <div class="mb-3">
                                <label class="form-label fw-semibold" for="parcour-select">
                                    <i class="fa fa-graduation-cap me-1 text-primary"></i>
                                    Parcours <span class="text-danger">*</span>
                                </label>
                                <div wire:ignore>
                                    <select class="form-select @error('newResults.parcour_id') is-invalid @enderror"
                                        id="parcour-select" name="parcour-select" multiple>
                                        @foreach ($parcours as $parcour)
                                            <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('newResults.parcour_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-6">
                            <!-- Niveau -->
                            <div class="mb-3">
                                <label class="form-label fw-semibold" for="niveau-select">
                                    <i class="fa fa-layer-group me-1 text-primary"></i>
                                    Niveau <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('newResults.niveau_id') is-invalid @enderror"
                                    wire:model="newResults.niveau_id" id="niveau-select" name="niveau-select">
                                    <option value="0">Sélectionner un niveau</option>
                                    @foreach ($niveaux as $niveau)
                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                    @endforeach
                                </select>
                                @error('newResults.niveau_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <!-- Semestres (Multi-select) -->
                            <div class="mb-3">
                                <label class="form-label fw-semibold" for="semestre-select">
                                    <i class="fa fa-calendar-alt me-1 text-primary"></i>
                                    Semestre <span class="text-danger">*</span>
                                </label>
                                <div wire:ignore>
                                    <select class="form-select @error('newResults.semestre_id') is-invalid @enderror"
                                        id="semestre-select" name="semestre-select" multiple>
                                        @foreach ($semestres as $semestre)
                                            <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('newResults.semestre_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-6">
                            <!-- Année Universitaire -->
                            <div class="mb-3">
                                <label class="form-label fw-semibold" for="annee-select">
                                    <i class="fa fa-clock me-1 text-primary"></i>
                                    Année <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('newResults.annee_universitaire_id') is-invalid @enderror"
                                    wire:model="newResults.annee_universitaire_id" id="annee-select" name="annee-select">
                                    <option value="0">Sélectionner une année</option>
                                    @foreach ($annees as $annee)
                                        <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                    @endforeach
                                </select>
                                @error('newResults.annee_universitaire_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-6 d-flex align-items-end">
                            <!-- Action Buttons -->
                            <div class="mb-3 w-100">
                                <div class="d-flex gap-2">
                                    <button type="button" wire:click="generateResults()"
                                        class="btn btn-primary w-100 position-relative"
                                        wire:loading.attr="disabled"
                                        wire:loading.class="btn-loading">
                                        <span wire:loading wire:target="generateResults"
                                            class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                        <span wire:loading.remove wire:target="generateResults">
                                            <i class="fa fa-chart-bar me-1 opacity-75"></i>
                                        </span>
                                        <span wire:loading wire:target="generateResults">Génération...</span>
                                        <span wire:loading.remove wire:target="generateResults">Générer</span>
                                    </button>
                                    <button type="button" class="btn btn-alt-secondary" wire:click="resetResults"
                                        wire:loading.attr="disabled"
                                        wire:loading.class="btn-loading"
                                        title="Réinitialiser les critères">
                                        <span wire:loading wire:target="resetResults"
                                            class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                        <i class="fa fa-sync" wire:loading.remove wire:target="resetResults"></i>
                                        <span class="d-none d-lg-inline" wire:loading.remove wire:target="resetResults">Reset</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Results Section -->
        <div class="block block-rounded shadow-sm">
            <div class="block-header block-header-default bg-light">
                <h3 class="block-title">
                    @if (!empty($notes))
                        <i class="fa fa-list-alt me-2 text-success"></i>
                        <span class="text-success">Liste des résultats</span>
                        <div class="mt-1">
                            <small class="text-muted">
                                @if ($current_parcours && count($current_parcours) > 0)
                                    <span class="badge bg-primary me-1">
                                        @foreach ($current_parcours as $index => $parcour)
                                            {{ $index > 0 ? ' / ' : '' }}{{ $parcour->sigle }}
                                        @endforeach
                                    </span>
                                @endif

                                @if ($current_niveau)
                                    <span class="badge bg-info me-1">{{ $current_niveau->nom }}</span>
                                @endif

                                @if ($current_semestres && count($current_semestres) > 0)
                                    <span class="badge bg-warning me-1">
                                        @foreach ($current_semestres as $index => $semestre)
                                            {{ $index > 0 ? ' / ' : '' }}{{ $semestre->nom }}
                                        @endforeach
                                    </span>
                                @endif

                                @if ($current_annee)
                                    <span class="badge bg-secondary">{{ $current_annee->nom }}</span>
                                @endif
                            </small>
                        </div>
                    @else
                        <i class="fa fa-info-circle me-2 text-muted"></i>
                        <span class="text-muted">Aucun résultat disponible</span>
                    @endif
                </h3>

                @if (!empty($notes))
                    <div class="block-options">
                        <div class="d-flex gap-2">
                            <span class="badge bg-success fs-sm me-2">
                                {{ count($notes) }} étudiant(s)
                            </span>
                            <button class="btn btn-sm btn-primary position-relative"
                                wire:click="pdfGenerate()"
                                wire:loading.attr="disabled"
                                wire:loading.class="btn-loading">
                                <span wire:loading wire:target="pdfGenerate"
                                    class="spinner-border spinner-border-sm me-1"
                                    role="status" aria-hidden="true"></span>
                                <i class="fa fa-file-pdf me-1" wire:loading.remove wire:target="pdfGenerate"></i>
                                <span wire:loading wire:target="pdfGenerate">Génération PDF...</span>
                                <span wire:loading.remove wire:target="pdfGenerate">Imprimer PDF</span>
                                <div class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success d-none d-md-block">
                                    PDF
                                </div>
                            </button>
                        </div>
                    </div>
                @endif
            </div>

            <div class="block-content block-content-full p-0" wire:loading.class="table-loading" wire:target="generateResults">
                <!-- Loading state for results generation -->
                <div wire:loading wire:target="generateResults" class="p-5 text-center">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <h5 class="text-primary">Génération des résultats en cours...</h5>
                    <p class="text-muted mb-0">Veuillez patienter pendant le calcul des moyennes et classements.</p>
                </div>

                @if (!empty($notes))
                    <div class="table-responsive" wire:loading.remove wire:target="generateResults">
                        <table class="table table-bordered table-striped table-vcenter mb-0 results-table">
                            <thead class="table-dark">
                                <tr>
                                    <th class="text-center fw-bold" style="width: 80px;">
                                        <i class="fa fa-trophy me-1"></i>Rang
                                    </th>
                                    <th class="fw-bold">
                                        <i class="fa fa-user me-1"></i>Nom et Prénoms
                                    </th>
                                    <th class="text-center fw-bold" style="width: 120px;">
                                        <i class="fa fa-chart-line me-1"></i>Moyenne
                                    </th>
                                    <th class="text-center fw-bold" style="width: 140px;">
                                        <i class="fa fa-medal me-1"></i>Mention
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($notes as $index => $result)
                                    <tr class="result-row">
                                        <td class="text-center fw-bold fs-5 text-primary">
                                            @if(isset($result['rang']))
                                                @if($result['rang'] <= 3)
                                                    @php
                                                        $rankIcon = match($result['rang']) {
                                                            1 => 'fa-trophy text-warning',
                                                            2 => 'fa-medal text-secondary',
                                                            3 => 'fa-award text-warning',
                                                            default => ''
                                                        };
                                                    @endphp
                                                    <i class="fa {{ $rankIcon }} me-1"></i>
                                                @endif
                                                {{ $result['rang'] }}
                                                @if($index > 0 && isset($notes[$index-1]['rang']) && $result['rang'] == $notes[$index-1]['rang'])
                                                    <br><small class="text-muted fst-italic">(ex æquo)</small>
                                                @endif
                                            @else
                                                {{ $index + 1 }}
                                            @endif
                                        </td>
                                        <td class="fw-semibold">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0 me-2">
                                                    <div class="bg-primary-light rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                        <i class="fa fa-user text-primary"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    {{ $result['nom'] }} {{ $result['prenom'] }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-primary fs-6 px-3 py-2">
                                                {{ $result['moy'] }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            @php
                                                $badgeClass = match ($result['mention']) {
                                                    'Très bien' => 'success',
                                                    'Bien' => 'info',
                                                    'Assez-bien' => 'warning',
                                                    default => 'secondary',
                                                };
                                                $badgeIcon = match ($result['mention']) {
                                                    'Très bien' => 'fa-star',
                                                    'Bien' => 'fa-thumbs-up',
                                                    'Assez-bien' => 'fa-check',
                                                    default => 'fa-minus',
                                                };
                                            @endphp
                                            <span class="badge bg-{{ $badgeClass }} fs-sm px-3 py-2">
                                                <i class="fa {{ $badgeIcon }} me-1"></i>
                                                {{ $result['mention'] }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="p-4" wire:loading.remove wire:target="generateResults">
                        <div class="alert alert-info d-flex align-items-center" role="alert">
                            <div class="flex-shrink-0 me-3">
                                <i class="fa fa-info-circle fa-2x"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="alert-heading mb-1">Aucun résultat disponible</h5>
                                <p class="mb-0">
                                    Veuillez sélectionner les critères ci-dessus et cliquer sur "Générer" pour afficher les résultats.
                                </p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- END Results Section -->
    </div>
    <!-- END Page Content -->
</div>

<!-- Enhanced Styling and Print Optimization -->
<style>
    /* Enhanced professional styling */
    .results-table {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .result-row:hover {
        background-color: rgba(59, 113, 202, 0.05);
        transition: background-color 0.2s ease;
    }

    /* Enhanced loading states */
    .btn-loading {
        opacity: 0.7;
        cursor: not-allowed;
        position: relative;
    }

    .btn-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
    }

    /* Smooth transitions for better UX */
    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    }

    /* Enhanced form styling */
    .form-select:focus,
    .form-control:focus {
        border-color: #3B71CA;
        box-shadow: 0 0 0 0.2rem rgba(59, 113, 202, 0.25);
    }

    /* Loading overlay for table */
    .table-loading {
        position: relative;
    }

    .table-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    /* Print-specific styles for optimal layout */
    @media print {
        /* Hide non-essential elements */
        .bg-body-light,
        .block-header,
        .btn,
        .badge-count,
        .d-print-none {
            display: none !important;
        }

        /* Ensure content fits on minimum pages */
        .content {
            padding: 0 !important;
            margin: 0 !important;
        }

        /* Optimize table for print */
        .results-table {
            width: 100% !important;
            font-size: 11pt !important;
            border-collapse: collapse !important;
        }

        .results-table th {
            background-color: #f0f0f0 !important;
            color: #000 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .results-table td {
            padding: 4pt 6pt !important;
        }

        /* Ensure rows don't break across pages */
        .result-row {
            page-break-inside: avoid !important;
        }

        /* Simplify user display for print */
        .result-row .d-flex {
            display: block !important;
        }

        .result-row .bg-primary-light {
            display: none !important;
        }

        /* Ensure badges print properly */
        .badge {
            border: 1px solid #ddd !important;
            background-color: transparent !important;
            color: #000 !important;
            padding: 1pt 4pt !important;
            font-weight: normal !important;
        }

        /* Ensure table header repeats on each page */
        thead {
            display: table-header-group !important;
        }

        /* Add page title for print */
        .block-rounded::before {
            content: "Résultats Académiques - IMSAA";
            display: block;
            text-align: center;
            font-size: 14pt;
            font-weight: bold;
            margin-bottom: 10pt;
        }
    }
</style>
