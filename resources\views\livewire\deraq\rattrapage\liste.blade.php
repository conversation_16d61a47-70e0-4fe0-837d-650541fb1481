 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Rattrapage
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">
     <form method="POST" role="form" wire:submit.prevent="goToAddRattrapage()" enctype="multipart/form-data">
         <!-- Dynamic Table Full -->
         <div class="block block-rounded">

             <div class="block-content">
                 <div class="row justify-content-center py-sm-3 py-md-5">
                     <div class="col-sm-10 col-md-8">
                         <div class="mb-4">
                             <label class="form-label" for="example-select">Parcours <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('newRattrapages.parcour_id') is-invalid @enderror"
                                 wire:model="newRattrapages.parcour_id" id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($parcours as $parcour)
                                     <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                 @endforeach
                             </select>

                             @error('newRattrapages.parcour_id')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>

                         <div class="mb-4">
                             <label class="form-label" for="example-select">Niveau <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('newRattrapages.niveau_id') is-invalid @enderror"
                                 wire:model="newRattrapages.niveau_id" id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($niveaux as $niveau)
                                     <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                 @endforeach
                             </select>

                             @error('newRattrapages.niveau_id')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>
                         <div class="mb-4">
                             <label class="form-label" for="example-select">Semestre <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('newRattrapages.semestre_id') is-invalid @enderror"
                                 wire:model="newRattrapages.semestre_id" id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($semestres as $semestre)
                                     <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                                 @endforeach
                             </select>

                             @error('newRattrapages.semestre_id')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>
                         <div class="mb-4">
                             <label class="form-label" for="example-select">Année Universitaire <span
                                     class="text-danger">*</span></label>
                             <select
                                 class="form-select @error('newRattrapages.annee_universitaire_id') is-invalid @enderror"
                                 wire:model="newRattrapages.annee_universitaire_id" id="example-select"
                                 name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($annees as $annee)
                                     <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                 @endforeach
                             </select>

                             @error('newRattrapages.annee_universitaire_id')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>
                         <div class="mb-4">
                             <button type="submit" class="btn btn-primary mb-3">Générer</button>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
         <!-- END Dynamic Table Full -->
     </form>

 </div>
 <!-- END Page Content -->
