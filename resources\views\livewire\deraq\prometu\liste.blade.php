 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-4">
             Gestion des admissions
         </h1>
         <div class="row justify-content-center p-md-2">
             <div class="col-md-3">
                 <div class="mb-4">
                     <label class="form-label" for="example-select">Parcours <span class="text-danger">*</span></label>
                     <select class="form-select @error('listEtus.parcour_id') is-invalid @enderror"
                         wire:model="listEtus.parcour_id" id="example-select" name="example-select">
                         <option selected value="0">Open this select menu</option>
                         @foreach ($parcours as $parcour)
                             <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                         @endforeach
                     </select>

                     @error('listEtus.parcour_id')
                         <span class="text-danger">{{ $message }}</span>
                     @enderror
                 </div>
             </div>
             <div class="col-md-3">
                 <div class="mb-4">
                     <label class="form-label" for="example-select">Niveau <span class="text-danger">*</span></label>
                     <select class="form-select @error('listEtus.niveau_id') is-invalid @enderror"
                         wire:model="listEtus.niveau_id" id="example-select" name="example-select">
                         <option selected value="0">Open this select menu</option>
                         @foreach ($niveaux as $niveau)
                             <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                         @endforeach
                     </select>

                     @error('listEtus.niveau_id')
                         <span class="text-danger">{{ $message }}</span>
                     @enderror
                 </div>
             </div>
             <div class="col-md-3">
                 <div class="mb-4">
                     <label class="form-label" for="example-select">Année Universitaire <span
                             class="text-danger">*</span></label>
                     <select class="form-select @error('listEtus.annee_universitaire_id') is-invalid @enderror"
                         wire:model="listEtus.annee_universitaire_id" id="example-select" name="example-select">
                         <option selected value="0">Open this select menu</option>
                         @foreach ($annees as $annee)
                             <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                         @endforeach
                     </select>

                     @error('listEtus.annee_universitaire_id')
                         <span class="text-danger">{{ $message }}</span>
                     @enderror
                 </div>
             </div>
             <div class="col-md-3 d-flex align-items-center">
                 <button type="button" wire:click="goToPromEtu()" class="btn btn-primary">Générer</button>
             </div>
         </div>
     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">
     <div class="row">
         <div class="col-md-8">
             <!-- Dynamic Table Full -->
             <div class="block block-rounded">
                 <div class="block-header block-header-default">
                     <h3 class="block-title">
                         Liste des Etudiants
                     </h3>
                 </div>
                 <div class="block-content block-content-full">

                     <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                     <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                         <thead>
                             <tr>
                                 <th class="text-center" style="width: 50px;">#</th>
                                 <th>Nom et Prénom</th>
                                 {{-- <th class="d-none d-sm-table-cell">Parcours</th>
                         <th class="d-none d-sm-table-cell">Niveau</th>
                         <th class="d-none d-sm-table-cell" style="width: 10%;">Année</th>
                         <th class="text-center" style="width: 110px;">Actions</th> --}}
                             </tr>
                         </thead>
                         <tbody>

                             @forelse ($etus as $etu)
                                 <tr>
                                     <td class="text-center">{{ $etu->id }}</td>
                                     <td class="fw-semibold">
                                         @if ($etu->user == null)
                                             Pas de nom
                                         @else
                                             {{ $etu->user->nom }} {{ $etu->user->prenom }}
                                         @endif

                                     </td>
                                     {{--
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->niveau->nom }}
                                 </td>
                                 <td class="text-muted d-none d-sm-table-cell">
                                     {{ $etu->annee->nom }}
                                 </td> --}}

                                 </tr>
                             @empty
                                 <tr>
                                     <td colspan="4">
                                         <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                             role="alert">
                                             <div class="flex-grow-1 me-3">
                                                 <p class="mb-0">
                                                     Choisissez les informations et cliquez sur générer !!
                                                 </p>
                                             </div>
                                             <div class="flex-shrink-0">
                                                 <i class="fa fa-fw fa-exclamation-circle"></i>
                                             </div>

                                         </div>
                                     </td>
                                 </tr>
                             @endforelse



                         </tbody>
                     </table>


                 </div>
             </div>
             <!-- END Dynamic Table Full -->
         </div>

         <div class="col-md-4">
             <div class="block block-rounded">
                 <div class="block-header block-header-default">
                     <h3 class="block-title">Choix </h3>
                     {{-- <div class="block-options">
                    <button type="button" class="btn-block-option">
                        <i class="si si-settings"></i>
                    </button>
                </div> --}}
                 </div>
                 <div class="block-content">
                     <div class="row justify-content-center">


                         <div class="mb-4 col-md-10">
                             <label class="form-label" for="example-select">Parcours <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('promParcours') is-invalid @enderror"
                                 wire:model="promParcours" id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($parcours as $parcour)
                                     <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                 @endforeach
                             </select>

                             @error('promParcours')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>

                         <div class="mb-4 col-md-10">
                             <label class="form-label" for="example-select">Niveau <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('promNiveau') is-invalid @enderror"
                                 wire:model="promNiveau" id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($niveaux as $niveau)
                                     <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                 @endforeach
                             </select>

                             @error('promNiveau')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>

                         <div class="mb-4 col-md-10">
                             <label class="form-label" for="example-select">Année Universitaire <span
                                     class="text-danger">*</span></label>
                             <select class="form-select @error('promAnnee') is-invalid @enderror" wire:model="promAnnee"
                                 id="example-select" name="example-select">
                                 <option selected value="0">Open this select menu</option>
                                 @foreach ($annees as $annee)
                                     <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                 @endforeach
                             </select>

                             @error('promAnnee')
                                 <span class="text-danger">{{ $message }}</span>
                             @enderror
                         </div>

                         <div class="mb-4 text-center col-md-10">
                             <button type="button" wire:click="validateEtu()"
                                 class="btn btn-primary me-5">Valider</button>
                         </div>

                     </div>
                 </div>
             </div>
         </div>
     </div>
     <!-- END Dynamic Table Full -->
 </div>

 <!-- END Page Content -->
