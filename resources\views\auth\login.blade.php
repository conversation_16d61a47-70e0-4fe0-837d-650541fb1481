@extends('layouts.simple')

@section('content')
    <div id="page-container">

        <!-- Main Container -->
        <main id="main-container">
            <!-- Page Content -->
            <div class="bg-image" style="background-image: url('{{ asset('/media/photos/sld1.jpeg') }}');">
                <div class="row g-0 bg-primary-dark-op">
                    <!-- Meta Info Section -->
                    <div class="hero-static col-lg-4 d-none d-lg-flex flex-column justify-content-center">
                        <div class="p-4 p-xl-5 flex-grow-1 d-flex align-items-center">
                            <div class="w-100">
                                <a class="link-fx fw-semibold fs-2 text-white" href="#">
                                    Institut de Management et de Sciences Appliquées d'Antsinanana
                                </a>
                                <p class="text-white-75 me-xl-8 mt-2">
                                    Bienvenue sur votre espace adminitrations de votre Institut.
                                </p>
                            </div>
                        </div>
                        <div class="p-4 p-xl-5 d-xl-flex justify-content-between align-items-center fs-sm">
                            <p class="fw-medium text-white-50 mb-0">
                                <strong>IMSAA</strong> &copy; <span data-toggle="year-copy"></span>
                            </p>
                            <ul class="list list-inline mb-0 py-2">
                                <li class="list-inline-item">
                                    <a class="text-white-75 fw-medium" href="javascript:void(0)">Legal</a>
                                </li>
                                <li class="list-inline-item">
                                    <a class="text-white-75 fw-medium" href="javascript:void(0)">Contact</a>
                                </li>
                                <li class="list-inline-item">
                                    <a class="text-white-75 fw-medium" href="javascript:void(0)">Terms</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- END Meta Info Section -->

                    <!-- Main Section -->
                    <div class="hero-static col-lg-8 d-flex flex-column align-items-center bg-body-extra-light">
                        <div class="p-4 w-100 flex-grow-1 d-flex align-items-center">
                            <div class="w-100">
                                <!-- Header -->
                                <div class="text-center mb-3">
                                    <p class="mb-4">
                                        <img class="w-25" src="{{ asset('/media/photos/logo.png') }}" alt="Logo_Imsaa"
                                            sizes="50" srcset="">
                                    </p>
                                    
                                </div>
                                <!-- END Header -->

                                <!-- Sign In Form -->
                                <!-- jQuery Validation (.js-validation-signin class is initialized in js/pages/op_auth_signin.min.js which was auto compiled from _js/pages/op_auth_signin.js) -->
                                <!-- For more info and examples you can check out https://github.com/jzaefferer/jquery-validation -->
                                <div class="row g-0 justify-content-center">
                                    <div class="col-sm-8 col-xl-4">
                                        <form class="js-validation-signin" action="{{ route('login') }}" method="POST">
                                            @csrf
                                            <div class="mb-4">
                                                <input type="text"
                                                    class="form-control form-control-lg form-control-alt py-3 @error('email') is-invalid @enderror"
                                                    name="email" value="{{ old('email') }}" required autocomplete="email"
                                                    autofocus id="login-username" placeholder="Email">
                                            </div>
                                            <div class="mb-4">
                                                <input type="password"
                                                    class="form-control form-control-lg form-control-alt py-3 @error('password') is-invalid @enderror"
                                                    name="password" required autocomplete="current-password"
                                                    id="login-password" placeholder="Mot de passe">
                                            </div>
                                            <div
                                                class="d-flex flex-column-reverse justify-content-between align-items-center mb-4">
                                                <div>

                                                    @if (Route::has('password.request'))
                                                        <a class="text-muted fs-sm fw-medium d-block d-lg-inline-block mb-1"
                                                            href="{{ route('password.request') }}">
                                                            Mot de passe oublié?
                                                        </a>
                                                    @endif
                                                </div>
                                                <div>
                                                    <button type="submit" class="btn btn-lg btn-alt-primary mb-2">
                                                        <i class="fa fa-fw fa-sign-in-alt me-1 opacity-50"></i> Se connecter
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <!-- END Sign In Form -->
                            </div>
                        </div>
                        <div
                            class="px-4 py-3 w-100 d-lg-none d-flex flex-column flex-sm-row justify-content-between fs-sm text-center text-sm-start">
                            <p class="fw-medium text-black-50 py-2 mb-0">
                                <strong>IMSAA</strong> &copy; <span data-toggle="year-copy"></span>
                            </p>
                            <ul class="list list-inline py-2 mb-0">
                                <li class="list-inline-item">
                                    <a class="text-muted fw-medium" href="javascript:void(0)">Legal</a>
                                </li>
                                <li class="list-inline-item">
                                    <a class="text-muted fw-medium" href="javascript:void(0)">Contact</a>
                                </li>
                                <li class="list-inline-item">
                                    <a class="text-muted fw-medium" href="javascript:void(0)">Terms</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- END Main Section -->
                </div>
            </div>
            <!-- END Page Content -->
        </main>
        <!-- END Main Container -->
    </div>
@endsection
