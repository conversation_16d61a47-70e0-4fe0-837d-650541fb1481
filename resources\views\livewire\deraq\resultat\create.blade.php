<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListResult()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListResult()">Générer Résultat</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Liste des résultats
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<!-- Page Content -->
<div class="content">

    <!-- Dynamic Table Full -->
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                @if (!empty($notes))
                    @if ($parcour1 != 0 && $parcour2 == 0)
                        Liste des résultats du {{ $current_parcours->nom }}/{{ $current_parcours1->nom }}
                        {{ $current_niveau->nom }} {{ $current_semestre->nom }} {{ $current_annee->nom }}
                    @elseif ($parcour1 != 0 && $parcour2 != 0)
                        Liste des résultats du {{ $current_parcours->nom }}/{{ $current_parcours1->nom }}/{{ $current_parcours2->nom }}
                        {{ $current_niveau->nom }} {{ $current_semestre->nom }} {{ $current_annee->nom }}
                    @else
                        Liste des résultats du {{ $current_parcours->nom }} {{ $current_niveau->nom }}
                        {{ $current_semestre->nom }} {{ $current_annee->nom }}
                    @endif
                @else
                    Pas de résultat
                @endif

            </h3>
            @if (!empty($notes))
                <div class="block-options">
                    <a class="btn btn-sm btn-primary me-1" wire:click="pdfGenerate()">
                        <i class="fa fa-plus me-1"></i> Imprimer en PDF
                    </a>
                </div>
            @endif

        </div>
        <div class="block-content block-content-full">
            <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
            <table class="table table-bordered table-striped table-vcenter">
                <thead>
                    <tr>
                        <th class="text-center" style="width: 50px;">Rang</th>
                        <th>Nom</th>
                        <th class="text-center" style="width: 100px;">Moyenne</th>
                        <th class="text-center" style="width: 100px;">Mention</th>
                    </tr>
                </thead>
                <tbody>

                    @forelse ($notes as $result)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>
                                {{ $result["nom"] }} {{ $result["prenom"] }}
                            </td>

                            <td>
                                {{ $result["moy"] }}
                            </td>
                            <td>
                               {{ $result["mention"] }}
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4">
                                <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                    role="alert">
                                    <div class="flex-grow-1 me-3">
                                        <p class="mb-0">
                                            Impossible de générer. Pas encore de note ajouté !!
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <i class="fa fa-fw fa-exclamation-circle"></i>
                                    </div>

                                </div>
                            </td>
                        </tr>
                    @endforelse



                </tbody>
            </table>
        </div>
    </div>
    <!-- END Dynamic Table Full -->


</div>
<!-- END Page Content -->
