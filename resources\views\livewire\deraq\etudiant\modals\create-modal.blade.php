<!-- Create Student Modal -->
<div wire:ignore.self class="modal fade" id="createModal" tabindex="-1" role="dialog" aria-labelledby="createModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createModalLabel">Ajouter un nouvel étudiant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="closeAllModals"></button>
            </div>
            <div class="modal-body">
                <form wire:submit.prevent="addUser">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nom" class="form-label">Nom <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('newUser.nom') is-invalid @enderror" id="nom" 
                                wire:model.defer="newUser.nom" placeholder="Nom de l'étudiant">
                            @error('newUser.nom')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="prenom" class="form-label">Prénom <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('newUser.prenom') is-invalid @enderror" id="prenom" 
                                wire:model.defer="newUser.prenom" placeholder="Prénom de l'étudiant">
                            @error('newUser.prenom')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">Téléphone <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('newUser.telephone1') is-invalid @enderror" id="telephone" 
                                wire:model.defer="newUser.telephone1" placeholder="Numéro de téléphone">
                            @error('newUser.telephone1')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sexe" class="form-label">Sexe <span class="text-danger">*</span></label>
                            <select class="form-select @error('newUser.sexe') is-invalid @enderror" id="sexe" 
                                wire:model.defer="newUser.sexe">
                                <option value="">-- Sélectionner --</option>
                                <option value="M">Masculin</option>
                                <option value="F">Féminin</option>
                            </select>
                            @error('newUser.sexe')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_naissance" class="form-label">Date de naissance</label>
                            <input type="date" class="form-control js-datepicker" id="date_naissance" 
                                wire:model.defer="newUser.date_naissance">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" 
                                wire:model.defer="newUser.email" placeholder="Email de l'étudiant">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="parcours" class="form-label">Parcours <span class="text-danger">*</span></label>
                            <select class="form-select @error('newUser.parcour_id') is-invalid @enderror" id="parcours" 
                                wire:model.defer="newUser.parcour_id">
                                <option value="">-- Sélectionner --</option>
                                @foreach ($parcours as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->nom }} ({{ $parcour->sigle }})</option>
                                @endforeach
                            </select>
                            @error('newUser.parcour_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="niveau" class="form-label">Niveau <span class="text-danger">*</span></label>
                            <select class="form-select @error('newUser.niveau_id') is-invalid @enderror" id="niveau" 
                                wire:model.defer="newUser.niveau_id">
                                <option value="">-- Sélectionner --</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>
                            @error('newUser.niveau_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" wire:click="closeAllModals">Annuler</button>
                <button type="button" class="btn btn-primary" wire:click="addUser">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    window.addEventListener('livewire:load', function () {
        Livewire.on('openCreateModal', () => {
            new bootstrap.Modal(document.getElementById('createModal')).show();
        });
    });
    
    window.addEventListener('helperDatePicker', function() {
        One.helpers(['js-datepicker']);
        jQuery('.js-datepicker').on('change', function (e) {
            const selectedDate = e.target.value;
            if (selectedDate) {
                @this.set('newUser.date_naissance', selectedDate);
            }
        });
    });
</script>
@endpush