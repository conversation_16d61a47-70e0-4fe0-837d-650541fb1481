
<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="h3 fw-bold mb-2">
                Gestion des Etudiants
            </h1>
            <!-- Added search counter to show results -->
            @if(!empty($query))
                <span class="badge bg-primary">{{ $etus->total() }} résultats pour "{{ $query }}"</span>
            @endif
        </div>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">

    <!-- Filter Card -->
    <div class="block block-rounded mb-4">
        <div class="block-content block-content-full bg-body-light">
            <div class="row">
                <div class="col-sm-12 col-lg-8">
                    <div class="d-flex flex-wrap gap-2">
                        <div class="flex-grow-1">
                            <label class="form-label" for="filter-parcours">Parcours</label>
                            <select wire:model="filtreParcours" id="filter-parcours" class="form-select">
                                <option value="">Tous les parcours</option>
                                @foreach ($parcours as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="flex-grow-1">
                            <label class="form-label" for="filter-niveau">Niveau</label>
                            <select wire:model="filtreNiveau" id="filter-niveau" class="form-select">
                                <option value="">Tous les niveaux</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="flex-grow-1">
                            <label class="form-label" for="filter-annee">Année</label>
                            <select wire:model="filtreAnnee" id="filter-annee" class="form-select">
                                <option value="">Toutes les années</option>
                                @foreach ($annees as $annee)
                                    <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-lg-4">
                    <label class="form-label" for="search-input">Recherche</label>
                    <div class="input-group">
                        <input type="search" wire:model.debounce.300ms="query" id="search-input" class="form-control" placeholder="Nom, prénom...">
                        <button type="button" class="btn btn-alt-primary" wire:click="$refresh">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Students Table -->
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                Liste des Etudiants
                @if($etus->total() > 0)
                    <span class="badge bg-primary ms-1">{{ $etus->total() }}</span>
                @endif
            </h3>
            <div class="block-options">
                <button type="button" class="btn btn-sm btn-alt-secondary" wire:click="$refresh">
                    <i class="si si-refresh"></i> Actualiser
                </button>
            </div>
        </div>
        <div class="block-content block-content-full">
            @if($etus->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-vcenter">
                        <thead>
                            <tr>
                                <th>Nom et Prénom</th>
                                <th class="d-none d-sm-table-cell">Parcours</th>
                                <th class="d-none d-sm-table-cell">Niveau</th>
                                <th class="d-none d-sm-table-cell">Année Universitaire</th>
                                <th class="text-center" style="width: 120px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($etus as $etu)
                                <tr>
                                    <td class="fw-semibold">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0 me-2">
                                                <div class="avatar rounded-circle bg-primary-lighter text-primary">
                                                    <span class="avatar-content">{{ substr($etu->user->nom, 0, 1) }}{{ substr($etu->user->prenom, 0, 1) }}</span>
                                                </div>
                                            </div>
                                            <div>
                                                {{ $etu->user->nom }} {{ $etu->user->prenom }}
                                                @if($etu->user->email)
                                                    <div class="fs-sm text-muted">{{ $etu->user->email }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-sm-table-cell">
                                        @if ($etu->parcours)
                                            <span class="badge bg-primary">{{ $etu->parcours->sigle }}</span>
                                        @else
                                            <span class="badge bg-warning">Non défini</span>
                                        @endif
                                    </td>
                                    <td class="d-none d-sm-table-cell">
                                        {{ $etu->niveau->nom }}
                                    </td>
                                    <td class="d-none d-sm-table-cell">
                                        {{ $etu->annee->nom }}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <a href="{{ route('caf.caisse.payment.etudiant', ['userId' => $etu->user->id, 'niveauId' => $etu->niveau->id, 'anneeId' => $etu->annee->id]) }}"
                                                class="btn btn-sm btn-alt-primary" target="_blank">
                                                <i class="si si-wallet me-1"></i> Paiement
                                            </a>
                                            <!-- Added dropdown for additional options -->
                                            <button type="button" class="btn btn-sm btn-alt-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span class="visually-hidden">Toggle Dropdown</span>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-end">
                                                <a class="dropdown-item" href="#">
                                                    <i class="far fa-user me-1"></i> Profil
                                                </a>
                                                <a class="dropdown-item" href="#">
                                                    <i class="far fa-file-alt me-1"></i> Historique
                                                </a>
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="#" target="_blank">
                                                    <i class="fa fa-print me-1"></i> Imprimer reçu
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="fs-sm text-muted">
                        Affichage de {{ $etus->firstItem() ?? 0 }} à {{ $etus->lastItem() ?? 0 }} sur {{ $etus->total() }} étudiants
                    </div>
                    <div>
                        {{ $etus->links() }}
                    </div>
                </div>
            @else
                <div class="py-4 text-center">
                    <div class="display-4 text-muted">
                        <i class="si si-magnifier"></i>
                    </div>
                    <p class="mt-3 mb-0 fw-semibold">Aucun étudiant trouvé</p>
                    @if(!empty($query) || !empty($filtreParcours) || !empty($filtreNiveau) || !empty($filtreAnnee))
                        <p class="fs-sm text-muted mb-3">Essayez de modifier vos critères de recherche</p>
                        <button class="btn btn-sm btn-alt-primary" wire:click="resetFilters">
                            <i class="fa fa-redo me-1"></i> Réinitialiser les filtres
                        </button>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
<!-- END Page Content -->

<script>
    document.addEventListener('livewire:load', function () {
        // Add loading indicators
        Livewire.hook('message.sent', () => {
            Dashmix.block('state_loading', '#page-container');
        });
        
        Livewire.hook('message.processed', () => {
            Dashmix.block('state_normal', '#page-container');
        });
    });
</script>
