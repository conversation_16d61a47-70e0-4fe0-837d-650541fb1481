<!DOCTYPE html>
<html>

<head>
    <title>État de Paiement des Étudiants</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css" media="screen">
        html {
            font-family: sans-serif;
            line-height: 1.15;
            margin: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            text-align: left;
            background-color: #fff;
            font-size: 12px; /* Slightly smaller base font for potentially more content */
            margin: 30pt; /* Adjust margin slightly */
        }

        h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        strong {
            font-weight: bolder;
        }

        img {
            vertical-align: middle;
            border-style: none;
        }

        table {
            border-collapse: collapse;
        }

        th {
            text-align: inherit;
        }

        h4, .h4 {
            margin-bottom: 0.5rem;
            font-weight: 500;
            line-height: 1.2;
        }

        h4, .h4 {
            font-size: 1.5rem;
        }

        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
        }

        .table th,
        .table td {
            padding: 0.5rem; /* Adjust padding */
            vertical-align: top;
        }

        /* .table.table-items td { */ /* Removed as .table-items class is not used */
            /* border-top: 1px solid #dee2e6; */
        /* } */

        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
            background-color: #3B71CA;
            color: white;
        }

        .mt-5 {
            margin-top: 3rem !important;
        }

        .pr-0, .px-0 {
            padding-right: 0 !important;
        }

        .pl-0, .px-0 {
            padding-left: 0 !important;
        }

        .text-right {
            text-align: right !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-uppercase {
            text-transform: uppercase !important;
        }

        * {
            font-family: "DejaVu Sans";
        }

        body, h1, h2, h3, h4, h5, h6, table, th, tr, td, p, div {
            line-height: 1.1;
        }

        .party-header {
            font-size: 1.5rem;
            font-weight: 400;
        }

        .total-amount {
            font-size: 12px;
            font-weight: 700;
        }

        .border-0 {
            border: none !important;
        }

        .cool-gray {
            color: #3B71CA;
        }

        .red {
            color: #DC4C64;
        }

        .tg {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .tg td {
            border-color: black;
            border-style: solid;
            border-width: 1px;
            font-family: "DejaVu Sans", Arial, sans-serif; /* Ensure DejaVu Sans */
            font-size: 11px; /* Smaller font for table data */
            overflow: hidden;
            padding: 6px 5px; /* Adjust padding */
            word-break: normal;
        }

        .tg th {
            border-color: black;
            border-style: solid;
            border-width: 1px;
            font-family: "DejaVu Sans", Arial, sans-serif; /* Ensure DejaVu Sans */
            font-size: 11px; /* Smaller font for table header */
            font-weight: bold; /* Make header bold */
            overflow: hidden;
            padding: 6px 5px; /* Adjust padding */
            word-break: normal;
            background-color: #3B71CA;
            color: white;
        }

        .tg .tg-cly1 {
            text-align: left;
            vertical-align: middle
        }

        .tg .tg-zr06 {
            background-color: #FFF;
            text-align: left;
            vertical-align: middle
        }

        tr:nth-child(even) {
            background-color: #f7f7f7;
        }
    </style>
</head>

<body>
    <!-- En-tête -->
    <table class="table">
        <tbody>
            <tr>
                <td class="border-0 pl-0" width="30%">
                    <img src="https://www.institut-imsaa.com/image/logo/logo2-removebg-preview.png" alt="logo IMSAA" height="100">
                </td>
                <td class="border-0 pl-0 text-center">
                    <h4 class="text-uppercase cool-gray">
                        <strong> <span class="red">I</span>NSTITUT DE <span class="red">M</span>ANAGEMENT ET
                            DES
                            <span class="red">S</span>CIENCES <span class="red">A</span>PPLIQUEES <br>D'<span
                                class="red">A</span>NTSIRANANA
                            </span>
                    </h4>
                    <p style="margin-bottom: 0.2rem;">DIRECTION GENERALE </p>
                    <p style="margin-bottom: 0.2rem;">DIRECTION DES ETUDES DE LA RECHERCHE ET DE L'ASSURANCE QUALITE</p>
                </td>
            </tr>
        </tbody>
    </table>

    <!-- Titre et informations -->
    <div class="text-center" style="margin-bottom: 1.5rem; font-size: 13px;">
        @if (isset($current_niveau))
            <p style="margin-bottom: 0.5rem;"><strong>État de paiement des étudiants - {{ $current_niveau->nom }}</strong></p>

            @if (isset($current_parcours))
                <p>
                    Parcours : {{ $current_parcours->nom }}
                </p>
            @else
                <p>
                    Parcours : Tous les parcours
                </p>
            @endif

            <p style="margin-bottom: 0.3rem;">
                Types de paiement :
                @forelse($current_type_payments as $index => $type)
                    {{ $type->nom }}@if($index < count($current_type_payments) - 1), @endif
                @empty
                    Non spécifié
                @endforelse
            </p>

            <p style="margin-bottom: 0.3rem;">
                État : {{ $etat == 1 ? 'Non payé' : 'Payé' }}
            </p>

            @if (isset($current_annee))
                <p>
                    Année universitaire : {{ $current_annee->nom }}
                </p>
            @endif
        @else
            <strong>État de paiement des étudiants</strong>
        @endif
    </div>

    <!-- Tableau des résultats -->
    <table class="tg" style="margin-top: 10px;">
        <thead>
            <tr>
                <th class="tg-cly1">N°</th>
                <th class="tg-cly1">NOM</th>
                <th class="tg-cly1">PRÉNOM</th>
                <th class="tg-cly1">PARCOURS</th>
                <th class="tg-cly1">TÉLÉPHONE</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($etudiants as $etu)
                <tr> {{-- Removed inline style height --}}
                    <td class="tg-zr06">{{ $loop->iteration }}</td>
                    <td class="tg-zr06">{{ $etu->nom }}</td>
                    <td class="tg-zr06">{{ $etu->prenom }}</td>
                    <td class="tg-zr06">
                        @if (empty($etu->info) || empty($etu->info[0]->parcours))
                            Non défini
                        @else
                            {{ $etu->info[0]->parcours->sigle }}
                        @endif
                    </td>
                    <td class="tg-zr06">
                        {{ $etu->telephone1 }}
                        @if ($etu->telephone2)
                            / {{ $etu->telephone2 }}
                            @endif
                        </span>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="5">
                        <p class="text-center">
                            Aucun étudiant trouvé avec ces critères.
                        </p>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <!-- Pied de page -->
    <p class="mt-5">
        Arrêté la présente liste au nombre de {{ count($etudiants) }} étudiants.
    </p>
    <p>
        Fait à Antsiranana le, {{ date('d/m/Y') }}
    </p>
</body>

</html>
