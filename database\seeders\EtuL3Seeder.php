<?php

namespace Database\Seeders;

use App\Models\InscriptionStudent;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EtuL3Seeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $items = [
            
                
                  [
                    "nom" => "ZAKIRALY",
                    "prenom" => "Ansira",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "TONDIE",
                    "prenom" => "Aïlla",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "BEZAFY",
                    "prenom" => "<PERSON>risca",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "MANJARARISOA",
                    "prenom" => "Jenilla",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "MOHAMAD",
                    "prenom" => "Aïcha",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "NANTENAINA",
                    "prenom" => "Monica Dal<PERSON>",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "JENNY",
                    "prenom" => "Firoda Valda",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "TSIVALIANA",
                    "prenom" => "Marie Anita",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "BEARIVELO",
                    "prenom" => "Andrianirina Jean Aimé",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "LALANIRINA",
                    "prenom" => "Julie",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "RIVO",
                    "prenom" => "Christian",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "FAINA",
                    "prenom" => "Léoncia Marie Geneviève",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "RAZAFIMAMONJY",
                    "prenom" => "Wendy Karen",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "HERINIAINA",
                    "prenom" => "Larissa",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "SAMSIA",
                    "prenom" => "Soraya",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "LORISY",
                    "prenom" => "Jean Stephano",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "RAMAHAZOMILA",
                    "prenom" => "Keren Presculla",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "RAHELISOA",
                    "prenom" => "Lucianah Golia",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "ALIRENCIA",
                    "prenom" => "Soa Abdallah",
                    "parcour_id" => 10,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "ZAFY",
                    "prenom" => "Dahy Luciano",
                    "parcour_id" => 15,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "DRISY",
                    "prenom" => "Steeven",
                    "parcour_id" => 15,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "RASOLOFOMANANA",
                    "prenom" => "Robert Macnamara",
                    "parcour_id" => 15,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "IBRAHIM",
                    "prenom" => "Micaël Tombo",
                    "parcour_id" => 15,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "KARIM",
                    "prenom" => "SOIFOUANI",
                    "parcour_id" => 15,
                    "niveau_id" => 3
                  ],
                  [
                    "nom" => "HARONA",
                    "prenom" => "Amad",
                    "parcour_id" => 15,
                    "niveau_id" => 3
                  ]
              
        ];

        foreach ($items as $item) {
            $user = User::create($item);
            $user->roles()->attach(5);

            InscriptionStudent::create([
                "user_id" => $user->id,
                "annee_universitaire_id" => 4,
                "niveau_id" => $user->niveau_id,
                "parcour_id" => $user->parcour_id,
            ]);
        }
    }
}
