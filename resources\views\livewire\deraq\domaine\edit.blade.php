<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListDomaine()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListDomaine()">Liste des Domaine</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Edition de Domaine
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="updateDomaine()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire d'édition de Domaine</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    
                    <div class="col-12">
                        <label class="form-label" for="example-text-input">Nom du Domaine<span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="editDomaine.nom"
                            class="form-control @error('editDomaine.nom') is-invalid @enderror" id="example-text-input"
                            name="lastname" placeholder="Text Input">

                        @error('editDomaine.nom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    

                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
