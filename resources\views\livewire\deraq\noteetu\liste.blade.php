 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             {{ $current_user->nom }} {{ $current_user->prenom }} : {{ $current_user->parcours->sigle }} {{ $current_user->niveau->nom }}
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste de notes
             </h3>
         </div>
         <div class="block-content block-content-full">
             <div class="row mb-3">
                 <div class="col-sm-12 col-md-6">
                     <div class="d-flex">
                         <div>
                             <label>
                                 <select wire:model="filtreTypes" class="form-select form-select-sm">
                                     <option selected value="">Filtre Type</option>
                                     @foreach ($types as $type)
                                         <option value="{{ $type->id }}">{{ $type->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         
                         <div>
                             <label>
                                 <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                     <option selected value="">Filtre Année</option>
                                     @foreach ($annees as $annee)
                                         <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreSemestre" class="form-select form-select-sm">
                                     <option selected value="">Filtre Semestre</option>
                                     @foreach ($semestres as $semestre)
                                         <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         
                     </div>

                 </div>
                 <div class="col-sm-12 col-md-6 text-end">

                     <label>
                         <input type="search" wire:model="query" class="form-control form-control-sm"
                             placeholder="Search..">
                     </label>

                 </div>
             </div>
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                 <thead>
                     <tr>
                         <th class="text-center">Matière</th>
                         <th class="text-center">Type</th>
                         <th class="text-center d-none d-sm-table-cell">Année</th>
                         <th class="text-center">Note</th>
                     </tr>
                 </thead>
                 <tbody>

                     @foreach ($notes as $note)
                         <tr>
                             <td class="text-center">{{ $note->matiere->nom }}</td>
                             <td class="text-center fw-semibold">
                                 {{ $note->typeNote->nom }}
                             </td>
                             <td class="text-center d-none d-sm-table-cell">
                                 {{ $note->ue->annee->nom }}
                             </td>
                             <td class="text-center">
                                 {{ $note->valeur }}
                             </td>
                             
                         </tr>
                     @endforeach



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     {{ $notes->links() }}
                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
