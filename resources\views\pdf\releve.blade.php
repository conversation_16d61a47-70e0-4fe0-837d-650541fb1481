<!DOCTYPE html>
<html>

<head>
    <title>Student Grading Result</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css" media="screen">
        html {
            font-family: sans-serif;
            line-height: 1.15;
            margin: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            text-align: left;
            background-color: #fff;
            font-size: 13px;
            margin: 36pt;
        }

        h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
        }

        p {
            margin-top: 0;
            margin-bottom: 0.5rem;
        }

        strong {
            font-weight: bolder;
        }

        img {
            vertical-align: middle;
            border-style: none;
        }

        table {
            border-collapse: collapse;
        }

        th {
            text-align: inherit;
        }

        h4,
        .h4 {
            margin-bottom: 0.5rem;
            font-weight: 500;
            line-height: 1.2;
        }

        h4,
        .h4 {
            font-size: 1.5rem;
        }

        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            vertical-align: top;
        }

        .table.table-items td {
            border-top: 1px solid #dee2e6;
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
            background-color: #3B71CA;
        }

        table tr:nth-child(even) {
            background-color: #f7f7f7;
        }

        .mt-5 {
            margin-top: 2rem !important;
        }
        .mt-4 {
            margin-top: 1rem !important;
        }

        .pr-0,
        .px-0 {
            padding-right: 0 !important;
        }

        .pl-0,
        .px-0 {
            padding-left: 0 !important;
        }

        .text-right {
            text-align: right !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-uppercase {
            text-transform: uppercase !important;
        }

        * {
            font-family: "DejaVu Sans";
        }

        body,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        table,
        th,
        tr,
        td,
        p,
        div {
            line-height: 1.1;
        }

        .party-header {
            font-size: 1.5rem;
            font-weight: 400;
        }

        .total-amount {
            font-size: 12px;
            font-weight: 700;
        }

        .border-0 {
            border: none !important;
        }

        .cool-gray {
            color: #3B71CA;
        }

        .red {
            color: #DC4C64;
        }

        .tg {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .tg td {
            border-color: black;
            border-style: solid;
            border-width: 1px;
            font-family: Arial, sans-serif;
            font-size: 13px;
            overflow: hidden;
            padding: 5px 5px;
            word-break: normal;
        }

        .tg th {
            border-color: black;
            border-style: solid;
            border-width: 1px;
            font-family: Arial, sans-serif;
            font-size: 13px;
            font-weight: normal;
            overflow: hidden;
            padding: 5px 5px;
            word-break: normal;
        }

        .tg .tg-sh4c {
            text-align: center;
            vertical-align: middle
        }

        .tg .tg-baqh {
            text-align: center;
            vertical-align: top
        }

        .tg .tg-2gg7 {
            font-weight: bold;
            text-align: right;
            vertical-align: middle
        }

        .tg .tg-c3ow {
            border-color: inherit;
            text-align: center;
            vertical-align: top
        }

        .tg .tg-wp8o {
            border-color: #000000;
            text-align: center;
            vertical-align: middle
        }

        .tg .tg-xwyw {
            border-color: #000000;
            text-align: center;
            vertical-align: middle
        }

        .tg .tg-0a7q {
            border-color: #000000;
            text-align: left;
            vertical-align: middle;
            text-align: center
        }

        .tg .tg-w4w7 {
            background-color: #FFF;
            border-color: #000000;
            text-align: center;
            vertical-align: middle
        }

        .tg .tg-3kbp {
            background-color: #FFF;
            text-align: center;
            vertical-align: middle
        }

        .page-break {
            page-break-after: always;
        }
    </style>
</head>

<body>
    @foreach ($semestres as $semestre)
        <table class="table">
            <tbody>
                <tr>
                    <td class="border-0 pl-0" width="30%">
                        <img src="https://www.institut-imsaa.com/image/logo/logo2-removebg-preview.png" alt="logo IMSAA"
                            height="100">
                    </td>
                    <td class="border-0 pl-0 text-center">
                        <h4 class="text-uppercase cool-gray">
                            <strong> <span class="red">I</span>NSTITUT DE <span class="red">M</span>ANAGEMENT ET
                                DES
                                <span class="red">S</span>CIENCES <span class="red">A</span>PPLIQUEES <br>D'<span
                                    class="red">A</span>NTSIRANANA
                                </span>
                        </h4>
                        {{-- <p>DIRECTION GENERALE </p> --}}
                        {{-- <p>DIRECTION DES ETUDES DE LA RECHERCHE ET DE L’ASSURANCE QUALITE</p> --}}
                        <p>Domaine : {{ $current_parcours->mention->domaine->nom }}</p>
                        <p>
                            Mention : {{ $current_parcours->mention->nom }}
                        </p>
                        <p>
                            Année Universitaire : {{ $current_annee->nom }}
                        </p>

                    </td>
                </tr>
            </tbody>
        </table>

        <p class="text-center" style="font-size: 15px;">
            <strong>RELEVE DE NOTES</strong>
        </p>

        <p>
            Nom et Prénom : {{ $current_user->nom }} {{ $current_user->prenom }}
        </p>
        <p>
            Niveau : {{ $current_niveau->nom }}
        </p>
        <p>
            Parcours : {{ $current_parcours->nom }}
        </p>
        <p>
            Semestre : S{{ $semestre->id }}
        </p>
        <table class="tg">
            <thead>
                {{-- <tr>
                <th scope="col" class="text-center border-0 pl-0">Rang</th>

                <th scope="col" class="border-0">Nom et Prénom</th>

                <th scope="col" class=" border-0 pr-0">Mention</th>
            </tr> --}}
                <tr>
                    <th class="tg-baqh">Code UE</th>
                    <th class="tg-baqh">Unité d’Enseignement</th>
                    <th class="tg-baqh">Code EC</th>
                    <th class="tg-baqh">Elément Constitutif </th>
                    <th class="tg-baqh">Notes</th>
                    <th class="tg-baqh">Crédit</th>
                    <th class="tg-baqh">Moyenne UE</th>
                    <th class="tg-baqh">Décisions</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $count = 0;
                    $moygen = 0;
                @endphp


                {{-- <tr>
                    <td colspan="8" class="tg-2gg7">
                        {{ $semestre->nom }}
                    </td>
                </tr> --}}
                @foreach ($semestre->ue as $ec)
                    @php
                        
                        $total = 0;
                        $moy = 0;
                    @endphp
                    @if ($ec->matiere->count() != 0)
                        @for ($i = 0; $i < $ec->matiere->count(); $i++)
                            @php
                                $total += $ec->matiere[$i]->moyenne;
                            @endphp
                        @endfor
                        @php
                            $moy = $total / $ec->matiere->count();
                            $moygen += $moy;
                            $count = $count + 1;
                            $m = number_format($moy, 2, ',', '.');
                            $u = $ec->matiere[0]->moyenne;
                            $s = number_format($u, 2, ',', '.');
                        @endphp
                        <tr>
                            <td rowspan="{{ $ec->matiere->count() }}" class="tg-0a7q">
                                {{ $ec->code }}
                            </td>
                            <td rowspan="{{ $ec->matiere->count() }}" class="tg-0a7q">
                                {{ $ec->nom }}
                            </td>
                            {{-- <td class="tg-wp8o"> {{ $ec->matiere[0]->code }} </td> --}}
                            <td class="tg-w4w7"> {{ $ec->matiere[0]->code }} </td>
                            <td class="tg-w4w7"> {{ $ec->matiere[0]->nom }} </td>
                            <td class="tg-wp8o"> {{ $s }}</td>
                            <td rowspan="{{ $ec->matiere->count() }}" class="tg-xwyw"> {{ $ec->credit }}
                            </td>
                            <td rowspan="{{ $ec->matiere->count() }}" class="tg-xwyw">

                                {{ $m }}
                            </td>
                            <td rowspan="{{ $ec->matiere->count() }}" class="tg-xwyw">
                                @if ($moy >= 10)
                                    Validée
                                @else
                                    Non validée
                                @endif
                            </td>
                        </tr>
                        @for ($i = 1; $i < $ec->matiere->count(); $i++)
                            @php
                                $t = $ec->matiere[$i]->moyenne;
                                $x = number_format($t, 2, ',', '.');
                            @endphp
                            <tr>
                                {{-- <td class="tg-sh4c"> {{ $ec->matiere[$i]->code }} </td> --}}
                                <td class="tg-3kbp"> {{ $ec->matiere[$i]->code }} </td>
                                <td class="tg-3kbp"> {{ $ec->matiere[$i]->nom }} </td>
                                <td class="tg-sh4c"> {{ $x }}</td>
                            </tr>
                        @endfor
                    @endif
                @endforeach
                <tr>
                    <td colspan="8" class="tg-2gg7">
                        @if ($count != 0)
                            @php
                                $di = $moygen / $count;
                                $moys = number_format($di, 2, ',', '.');
                            @endphp
                            <strong>Moyenne générale : {{ $moys }}</strong>
                        @endif
                    </td>
                    {{-- <td class="tg-xwyw">
                        @if ($di >= 10)
                            <strong>Validé</strong>
                        @else
                            <strong>Non validé</strong>
                        @endif
                    </td> --}}
                </tr>
            </tbody>
        </table>


        <p class="mt-4 text-center">
            @if ($di >= 11)
                <strong>Observation : Semestre validé</strong>
            @elseif ($di < 11 && $di >= 10)
                <strong>Observation : Semestre validé avec compensation</strong>
            @elseif ($di < 10 && $di >= 9)
                <strong>Observation : Semestre validé sous reserve de repêchage</strong>
            @else
                <strong>Observation : Semestre non validé</strong>
            @endif
        </p>
        <p class="mt-5 text-center" style="margin-left: 200px;">
            Antsiranana, le
        </p>
        {{-- <p class="text-right">
            La direction générale &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        </p> --}}

        <div style="position: absolute; bottom: 30px;">
            <table class="table">
                <tbody>
                    <tr>
                        <td class="border-0 pl-0" width="40%" style="text-align: justify;">
                            En application des instructions, il est interdit de délivrer un deuxième relevé de notes.
                            L’intéressé ne devrait en aucun cas se dessaisir du présent relevé.
                        </td>
                        <td class="border-0 pl-0 text-center">
                            &nbsp;
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="page-break"></div>

        {{-- <p>
        Listes arretées au nombre de {{ count($results) }} étudiants.
    </p> --}}
    @endforeach
</body>

</html>
