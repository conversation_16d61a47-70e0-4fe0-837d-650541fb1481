 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Ajout de notes
         </h1>

     </div>
 </div>
 <!-- END Hero -->


 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Cours
             </h3>

         </div>
         <div class="block-content block-content-full">
             <div class="row mb-3">
                 <div class="col-sm-12 col-md-6">

                     <div class="d-flex">
                         <div>
                             <label>
                                 <select wire:model="filtreParcours" class="form-select form-select-sm">
                                     <option selected value="">Filtre Parcours</option>
                                     @foreach ($parcours as $parcour)
                                         <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                     <option selected value="">Filtre Niveau</option>
                                     @foreach ($niveaux as $niveau)
                                         <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                     <option selected value="">Filtre Année</option>
                                     @foreach ($annees as $annee)
                                         <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                     </div>
                 </div>
                 <div class="col-sm-12 col-md-6 text-end">

                     <label>
                         <input type="search" wire:model="query" class="form-control form-control-sm"
                             placeholder="Search..">
                     </label>

                 </div>
             </div>
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter">
                 <thead>
                     <tr>
                         <th>Nom</th>
                         <th class="text-center d-none d-sm-table-cell">Enseignant</th>
                         <th class="text-center d-none d-sm-table-cell">UE</th>
                         <th class="text-center d-none d-sm-table-cell">Parcours & Niveau</th>
                         <th class="text-center">Année</th>
                         <th class="text-center" style="width: 110px;">Actions</th>
                     </tr>
                 </thead>
                 <tbody>

                     @foreach ($cours as $cour)
                         <tr>
                             <td class="fw-semibold">
                                 {{ $cour->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 @if ($cour->user == null)
                                     Pas d'enseignant
                                 @else
                                     {{ $cour->user->nom }} {{ $cour->user->prenom }}
                                 @endif
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $cour->ue->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $cour->ue->parcours->sigle }} {{ $cour->ue->niveau->nom }}
                             </td>
                             <td class="text-muted">
                                 {{ $cour->ue->annee->nom }}
                             </td>
                             <td class="text-center">
                                 @if ($cour->is_filled)
                                     <button class="btn btn-sm btn-alt-success">
                                         <i class="fa fa-check"></i>
                                         Notes
                                     </button>
                                 @else
                                     <a href="{{ route('deraq.pedagogiques.cours.notes', ['coursId' => $cour->id]) }}"
                                         class="btn btn-sm btn-alt-secondary">
                                         <i class="fa fa-plus"></i>
                                         Notes
                                     </a>
                                 @endif

                             </td>
                         </tr>
                     @endforeach



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     {{ $cours->links() }}
                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
