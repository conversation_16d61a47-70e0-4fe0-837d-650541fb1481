 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Enseignants
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Enseignants
             </h3>
             <div class="block-options">
                 <a class="btn btn-sm btn-primary me-1" wire:click="goToAddUser()">
                     <i class="fa fa-fw fa-user-plus me-1"></i> Nouvel Enseignant
                 </a>
             </div>
         </div>
         <div class="block-content block-content-full">
             <div class="row mb-3">
                 <div class="col-sm-12 col-md-6">
                     <label>
                         <select wire:model="filtreParcours" class="form-select form-select-sm">
                             <option selected value="">Filtrer par Parcours</option>
                             @foreach ($parcours as $parcour)
                                 <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                             @endforeach
                         </select>
                     </label>

                 </div>
                 <div class="col-sm-12 col-md-6 text-end">

                     <label>
                         <input type="search" wire:model="query" class="form-control form-control-sm"
                             placeholder="Search..">
                     </label>

                 </div>
             </div>
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                 <thead>
                     <tr>
                         <th class="text-center" style="width: 50px;">#</th>
                         <th>Nom et Prénom</th>
                         <th class="d-none d-sm-table-cell">Email</th>
                         <th class="d-none d-sm-table-cell">Matière</th>
                         <th class="d-none d-sm-table-cell" style="width: 10%;">Ajouté</th>
                         <th class="text-center" style="width: 100px;">Actions</th>
                     </tr>
                 </thead>
                 <tbody>

                     @foreach ($enseignants as $enseignant)
                         <tr>
                             <td class="text-center">{{ $enseignant->id }}</td>
                             <td class="fw-semibold">
                                 {{ $enseignant->nom }} {{ $enseignant->prenom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $enseignant->email }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $enseignant->allMatiereNames }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $enseignant->created_at->diffForHumans() }}
                             </td>
                             <td class="text-center">
                                 <div class="btn-group">
                                     <button type="button" class="btn btn-sm btn-alt-secondary"
                                          wire:click="goToEditUser({{ $enseignant->id }})" title="Edit">
                                         <i class="fa fa-fw fa-pencil-alt"></i>
                                     </button>
                                     <button type="button" class="btn btn-sm btn-alt-secondary"
                                          wire:click="deleteUser({{ $enseignant->id }})" title="Delete" onclick="confirm('Êtes-vous sûrs?') || event.stopImmediatePropagation()">
                                         <i class="fa fa-fw fa-times"></i>
                                     </button>
                                 </div>
                             </td>
                         </tr>
                     @endforeach



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     {{ $enseignants->links() }}
                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
