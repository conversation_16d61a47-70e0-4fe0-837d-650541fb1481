<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\User;
use Livewire\Component;

class PromtionEtu extends Component
{
    public $currentPage = PAGELIST;

    public $listEtus = [];

    public $etus = [];

    public $promParcours;
    public $promNiveau;
    public $promAnnee;

    public Parcour $current_parcours;
    public Parcour $current_parcours1;
    public Parcour $current_parcours2;
    public Niveau $current_niveau;
    public AnneeUniversitaire $current_annee;

    public function render()
    {
        return view('livewire.deraq.prometu.index', [
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "annees" => AnneeUniversitaire::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editSemestre.nom' => 'required',
            ];
        }

        return [
            'listEtus.niveau_id' => 'required',
            'listEtus.parcour_id' => 'required',
            'listEtus.annee_universitaire_id' => 'required',

        ];
    }

    public function goToListEtus()
    {
        $this->currentPage = PAGELIST;
        $this->listEtus = [];
        $this->etus = [];
        $this->promParcours = "";
        $this->promNiveau = "";
        $this->promAnnee = "";
    }

    public function goToPromEtu()
    {

        $validationAttributes = $this->validate();

        $this->generate($validationAttributes["listEtus"]["parcour_id"], $validationAttributes["listEtus"]["niveau_id"], $validationAttributes["listEtus"]["annee_universitaire_id"]);

    }

    public function generate($parcour_id, $niveau_id, $annee_universitaire_id)
    {


        $this->current_parcours = Parcour::find($parcour_id);

        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);



        $this->etus = InscriptionStudent::whereParcourId($parcour_id)
            ->whereNiveauId($niveau_id)
            ->whereAnneeUniversitaireId($annee_universitaire_id)
            ->get();
        // dd($this->etus);

    }

    public function validateEtu()
    {
        $validateArr = [
            'promParcours' => 'required',
            'promNiveau' => 'required',
            'promAnnee' => 'required',
        ];
        $this->validate($validateArr);

        foreach ($this->etus as $etu) {

            User::find($etu["user_id"])->update([
                "niveau_id" => $this->promNiveau,
                "parcour_id" => $this->promParcours,
            ]);

            InscriptionStudent::create([
                "annee_universitaire_id" => $this->promAnnee,
                "niveau_id" => $this->promNiveau,
                "parcour_id" => $this->promParcours,
                "user_id" => $etu["user_id"],
            ]);
            
        }
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Admission réussi!"]);
        $this->goToListEtus();
    }
}
