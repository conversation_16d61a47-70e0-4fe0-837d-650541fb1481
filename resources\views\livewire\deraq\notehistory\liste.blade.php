 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Historiques de notes
         </h1>

     </div>
 </div>
 <!-- END Hero -->


 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Historiques
             </h3>
             <div class="block-options">
                 <a class="btn btn-sm btn-primary me-1" wire:click="goToSuppNote()">
                     <i class="far fa-eye me-1"></i> Voir Note supprimé
                 </a>
             </div>
         </div>
         {{-- @dump($histories) --}}
         <div class="block-content block-content-full">
             <div class="row mb-3">
                 <div class="col-sm-12 col-md-6">
                     <div class="d-flex">
                         <div>
                             <label>
                                 <select wire:model="filtreParcours" class="form-select form-select-sm">
                                     <option selected value="">Filtre Parcours</option>
                                     @foreach ($parcours as $parcour)
                                         <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                     <option selected value="">Filtre Niveau</option>
                                     @foreach ($niveaux as $niveau)
                                         <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                     <option selected value="">Filtre Année</option>
                                     @foreach ($annees as $annee)
                                         <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreType" class="form-select form-select-sm">
                                     <option selected value="">Filtre Type</option>
                                     @foreach ($typenotes as $type)
                                         <option value="{{ $type->id }}">{{ $type->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>

                     </div>

                 </div>
                 <div class="col-sm-12 col-md-6 text-end">

                     <label>
                         <input type="search" wire:model="query" class="form-control form-control-sm"
                             placeholder="Search..">
                     </label>

                 </div>
             </div>
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter">
                 <thead>
                     <tr>
                         <th>#</th>
                         <th>Matière</th>
                         <th class="text-center d-none d-sm-table-cell">Correcteur</th>
                         <th class="text-center d-none d-sm-table-cell">Parcours</th>
                         <th class="text-center d-none d-sm-table-cell">Type</th>
                         <th class="text-center d-none d-sm-table-cell">Date de remise</th>
                         <th class="text-center" style="width: 150px;">Actions</th>
                     </tr>
                 </thead>
                 <tbody>

                     @foreach ($histories as $history)
                         <tr>
                             <td class="fw-semibold">
                                 {{ $history->id }}
                             </td>
                             <td class="fw-semibold">
                                 {{ $history->matieres->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $history->user->nom }} {{ $history->user->prenom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $history->matieres->ue->parcours->sigle }}
                                 {{ $history->matieres->ue->niveau->sigle }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $history->types->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $history->created_at }}
                             </td>
                             <td class="text-center">
                                 @if ($history->is_valid)
                                     Résultat sorti!!
                                     <div class="btn-group mb-2">
                                             <button type="button"
                                                 wire:click="goToEditNote({{ $history->matiere_id }} , {{ $history->type_note_id }}, {{ $history->id }})"
                                                 class="btn btn-sm btn-alt-secondary">Modifier
                                             </button>
                                         </div>
                                     @can('superadmin')
                                         
                                         <button type="button" class="btn btn-sm btn-alt-secondary"
                                             wire:click="deleteHisto({{ $history->id }})" title="Delete">
                                             <i class="fa fa-fw fa-times"></i> Supprimer
                                         </button>
                                     @endcan
                                 @else
                                     <div class="btn-group mb-2">
                                         <button type="button"
                                             wire:click="goToEditNote({{ $history->matiere_id }} , {{ $history->type_note_id }}, {{ $history->id }})"
                                             class="btn btn-sm btn-alt-secondary">Modifier
                                         </button>
                                     </div>
                                     <button type="button" class="btn btn-sm btn-alt-secondary"
                                         wire:click="deleteHisto({{ $history->id }})" title="Delete">
                                         <i class="fa fa-fw fa-times"></i> Supprimer
                                     </button>
                                 @endif
                             </td>
                         </tr>
                     @endforeach



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     {{ $histories->links() }}
                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
