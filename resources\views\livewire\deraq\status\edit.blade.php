 @section('js')
     <!-- jQuery (required for DataTables plugin) -->
     <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
 @endsection

 <div wire:ignore.self>
     <!-- Hero -->
     <div class="bg-body-light">
         <div class="content content-full">

             <h1 class="h3 fw-bold mb-2">
                 Ajout de notes
             </h1>

             <div class="flex-grow-1">
                 <h1 class="h3 fw-bold mb-2">
                     <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListNote()">
                         <i class="si si-arrow-left fa-2x"></i>
                     </button>
                 </h1>
             </div>

         </div>
     </div>
     <!-- END Hero -->


     <!-- Page Content -->
     <div class="content">

         <!-- Dynamic Table Full -->
         <div class="block block-rounded">

             <div class="block-content block-content-full">
             <div class="row">
                     <label class="col-sm-4 col-form-label text-center" for="example-hf-email">Motif :</label>
                     <div class="col-sm-8">
                         <select class="form-select @error('editNote.0.type_note_id') is-invalid @enderror" wire:model="editNote.0.type_note_id"
                             id="example-select1" name="example-select1">
                             {{-- <option selected value="null">Open this select menu</option> --}}
                             @foreach ($typeNotes as $typeNote)
                                 <option value="{{ $typeNote->id }}">{{ $typeNote->nom }}</option>
                             @endforeach
                         </select>

                         @error('editNote.0.type_note_id')
                             <span class="text-danger">{{ $message }}</span>
                         @enderror
                     </div>
                 </div>

                 {{-- @dump($history->id) --}}
                 {{-- 
                  @if ($errors->any())
                                        <div class="alert alert-danger">

                                            <h5><i class="icon fas fa-ban"></i> Erreurs!</h5>
                                            <ul>
                                            @foreach ($errors->all() as $error)
                                                <li>{{$error}}</li>
                                            @endforeach
                                            </ul>
                                        </div>
                                @endif --}}
                 <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                 <table class="mt-5 table table-bordered table-striped table-vcenter">
                     <thead>
                         <tr>
                             <th>Nom et Prenom</th>
                             <th class="text-center" style="width: 200px;">Note</th>
                         </tr>
                     </thead>
                     <tbody>

                         @foreach ($editNote as $noteEtu)
                             <tr>
                                 <td class="fw-semibold">
                                     {{ $noteEtu['user']['nom'] }} {{ $noteEtu['user']['prenom'] }}
                                 </td>
                                 <td class="text-muted">
                                     <input type="number" wire:model="editNote.{{ $loop->index }}.valeur"
                                         class="form-control text-center @error('editNote.' . $loop->index . '.valeur') is-invalid @enderror"
                                         id="example-text-input" name="firstname" placeholder="Note">

                                     @error('editNote.' . $loop->index . '.valeur')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </td>

                             </tr>
                         @endforeach



                     </tbody>
                 </table>


                 <button class="btn btn-primary mt-3" wire:click="updateNote()"><i class="fas fa-check"></i>
                     Valider
                 </button>
                 <button class="btn btn-primary mt-3" wire:click.prevent="goToListNote()">
                     Annuler
                 </button>

             </div>
         </div>
         <!-- END Dynamic Table Full -->


     </div>
     <!-- END Page Content -->
 </div>
