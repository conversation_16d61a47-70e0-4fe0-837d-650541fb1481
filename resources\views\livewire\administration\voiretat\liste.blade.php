<div>
    <!-- Hero -->
    <div class="bg-body-light">
        <div class="content content-full">
            <h1 class="h3 fw-bold mb-2">
                État de paiement des étudiants
            </h1>
            <div class="row justify-content-center p-md-2">
                <div class="col-md-2">
                    <div class="mb-4">
                        <label class="form-label" for="etat-select">État <span class="text-danger">*</span></label>
                        <select class="form-select @error('newEtus.etat') is-invalid @enderror" wire:model="newEtus.etat"
                            id="etat-select">
                            <option value="0">Sélectionner</option>
                            <option value="1">Non payé</option>
                            <option value="2">Payé</option>
                        </select>
                        @error('newEtus.etat')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-4">
                        <label class="form-label" for="parcours-select">Parcours <span class="text-danger">*</span></label>
                        <select class="form-select @error('newEtus.parcour_id') is-invalid @enderror"
                            wire:model="newEtus.parcour_id" id="parcours-select">
                            <option value="0">Sélectionner</option>
                            <option value="100">Tous les parcours</option>
                            @foreach ($parcours as $parcour)
                                <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                            @endforeach
                        </select>
                        @error('newEtus.parcour_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-4">
                        <label class="form-label" for="niveau-select">Niveau <span class="text-danger">*</span></label>
                        <select class="form-select @error('newEtus.niveau_id') is-invalid @enderror"
                            wire:model="newEtus.niveau_id" id="niveau-select">
                            <option value="0">Sélectionner</option>
                            @foreach ($niveaux as $niveau)
                                <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                            @endforeach
                        </select>
                        @error('newEtus.niveau_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-4">
                        <label class="form-label" for="annee-select">Année <span class="text-danger">*</span></label>
                        <select class="form-select @error('newEtus.annee_universitaire_id') is-invalid @enderror"
                            wire:model="newEtus.annee_universitaire_id" id="annee-select">
                            <option value="0">Sélectionner</option>
                            @foreach ($annees as $annee)
                                <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                            @endforeach
                        </select>
                        @error('newEtus.annee_universitaire_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-4">
                        <label class="form-label" for="type-payment-select">Types de paiement <span class="text-danger">*</span></label>
                        <select class="form-select @error('newEtus.type_payment_id') is-invalid @enderror"
                            wire:model="newEtus.type_payment_id" id="type-payment-select" multiple>
                            @foreach ($types as $type)
                                <option value="{{ $type->id }}">{{ $type->nom }}</option>
                            @endforeach
                        </select>
                        @error('newEtus.type_payment_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-8 d-flex justify-content-between">
                    <div>
                        <button type="button" wire:click="goToEtat()" class="btn btn-primary" wire:loading.attr="disabled">
                            <span wire:loading wire:target="goToEtat" class="spinner-border spinner-border-sm text-light me-2" role="status"></span>
                            Appliquer
                        </button>
                        <button type="button" wire:click="goToListResult()" class="btn btn-secondary">
                            Réinitialiser
                        </button>
                    </div>
                    @if (count($etus) > 0)
                    <div>
                        <button type="button" wire:click="exportToPDF()" class="btn btn-success" wire:loading.attr="disabled">
                            <span wire:loading wire:target="exportToPDF" class="spinner-border spinner-border-sm text-light me-1" role="status"></span>
                            <span wire:loading.remove wire:target="exportToPDF"><i class="fa fa-file-pdf me-1"></i></span>
                            Exporter
                        </button>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Dynamic Table Full -->
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    @if (count($etus) > 0 && isset($current_niveau) && !empty($current_type_payments))
                        <span>
                            Niveau: {{ $current_niveau->nom }} - 
                            Types: 
                            @foreach($current_type_payments as $index => $type)
                                {{ $type->nom }}@if($index < count($current_type_payments) - 1), @endif
                            @endforeach
                            - 
                            État: {{ $newEtus['etat'] == 1 ? 'Non payé' : 'Payé' }}
                        </span>
                    @endif
                </h3>
                <div class="block-options">
                    @if (count($etus) > 0)
                    <div class="input-group">
                        <input type="text" class="form-control form-control-sm" wire:model.debounce.300ms="search" placeholder="Rechercher...">
                        <select class="form-select form-select-sm" wire:model="perPage">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    @endif
                </div>
            </div>
            <div class="block-content block-content-full">
                <!-- DataTable -->
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-vcenter fs-sm">
                        <thead>
                            <tr>
                                <th class="text-center" style="width: 50px;">#</th>
                                <th wire:click="sortBy('nom')" style="cursor: pointer;">
                                    Nom
                                    @if ($sortField === 'nom')
                                        <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                    @endif
                                </th>
                                <th wire:click="sortBy('prenom')" style="cursor: pointer;">
                                    Prénom
                                    @if ($sortField === 'prenom')
                                        <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                    @endif
                                </th>
                                <th>Parcours</th>
                                <th>Téléphone</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($etus as $etu)
                                <tr>
                                    <td class="text-center">{{ $loop->iteration }}</td>
                                    <td class="fw-semibold">{{ $etu->nom }}</td>
                                    <td class="fw-semibold">{{ $etu->prenom }}</td>
                                    <td class="fw-semibold">
                                        @if (empty($etu->info) || empty($etu->info[0]->parcours))
                                            <span class="badge bg-warning">Non défini</span>
                                        @else
                                            {{ $etu->info[0]->parcours->sigle }}
                                        @endif
                                    </td>
                                    <td class="fw-semibold">
                                        {{ $etu->telephone1 }}
                                        @if ($etu->telephone2)
                                            <br>{{ $etu->telephone2 }}
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        @if (!empty($newEtus['etat']) && $newEtus['etat'] != 0)
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="fa fa-search fa-2x text-muted mb-2"></i>
                                                <p>Aucun étudiant trouvé avec ces critères.</p>
                                            </div>
                                        @else
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="fa fa-filter fa-2x text-muted mb-2"></i>
                                                <p>Veuillez sélectionner les critères de recherche.</p>
                                            </div>
                                        @endif
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!-- END Dynamic Table Full -->
    </div>
    <!-- END Page Content -->
</div>
