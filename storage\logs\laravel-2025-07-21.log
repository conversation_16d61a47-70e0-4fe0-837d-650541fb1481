[2025-07-21 12:31:45] local.INFO: Detected N+1 Query  
[2025-07-21 12:31:45] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Note
Num-Called: 3
Call-Stack:
#27 \app\Http\Livewire\Etudiant.php:731
#28 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#29 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#30 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#31 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#32 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#33 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#34 \vendor\livewire\livewire\src\LifecycleManager.php:89
#35 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#36 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#37 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#38 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#39 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#40 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#42 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#49 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
  
[2025-07-21 12:31:58] local.INFO: Detected N+1 Query  
[2025-07-21 12:31:58] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Note
Num-Called: 3
Call-Stack:
#27 \app\Http\Livewire\Etudiant.php:731
#28 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#29 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#30 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#31 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#32 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#33 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#34 \vendor\livewire\livewire\src\LifecycleManager.php:89
#35 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#36 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#37 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#38 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#39 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#40 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#42 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#49 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
  
[2025-07-21 12:33:17] local.INFO: Detected N+1 Query  
[2025-07-21 12:33:17] local.INFO: Model: App\Models\Semestre
Relation: App\Models\Ue
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Etudiant.php:731
#18 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#19 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#20 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#22 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#23 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#24 \vendor\livewire\livewire\src\LifecycleManager.php:89
#25 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#26 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#27 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#28 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#29 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#30 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#32 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#34 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#36 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#38 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#39 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#41 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#43 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#46 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:776
#48 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:740
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:729
  
[2025-07-21 12:33:17] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 2
Call-Stack:
#22 \app\Http\Livewire\Etudiant.php:731
#23 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#24 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#25 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#26 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#27 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#28 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#29 \vendor\livewire\livewire\src\LifecycleManager.php:89
#30 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#31 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#32 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#33 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#34 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#35 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#37 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#41 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#43 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
  
[2025-07-21 12:33:17] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Note
Num-Called: 6
Call-Stack:
#27 \app\Http\Livewire\Etudiant.php:731
#28 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#29 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#30 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#31 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#32 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#33 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#34 \vendor\livewire\livewire\src\LifecycleManager.php:89
#35 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#36 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#37 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#38 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#39 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#40 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#42 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#49 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
  
