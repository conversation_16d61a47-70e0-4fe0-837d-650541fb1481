<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class HistoriquePayment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'type_payment_id',
        'moyen_payment_id',
        'type_encaissement_id',
        'annee_universitaire_id',
        'montant',
        'libelle',
        'is_valid',
        'is_valid_sec',
        'code',
        'created_at',
        
    ];

    public function user(){
        return $this->belongsTo(User::class, "user_id", "id")->select(['id', 'nom', 'prenom']);
    }

    public function payment(){
        return $this->belongsTo(TypePayment::class, "type_payment_id", "id");
    }

    public function moyen(){
        return $this->belongsTo(MoyenPayment::class, "moyen_payment_id", "id");
    }

    public function encaissement(){
        return $this->belongsTo(TypeEncaissement::class, "type_encaissement_id", "id");
    }

    public function getPrixForHumansAttribute(){
        return number_format($this->montant, 0, ",", " ") . " " . env("CURRENCY", "Ar");
    }
}
