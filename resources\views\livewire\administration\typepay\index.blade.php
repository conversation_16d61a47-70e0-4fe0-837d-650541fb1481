@section('css')
    <link rel="stylesheet" href="{{ asset('js/plugins/sweetalert2/sweetalert2.min.css') }}">
@endsection

@section('js')
    <!-- jQuery (required for DataTables and notifications) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    <!-- SweetAlert2 for better confirmation dialogs -->
    <script src="{{ asset('js/plugins/sweetalert2/sweetalert2.min.js') }}"></script>
    <!-- Bootstrap JS for modals -->
    <script src="{{ asset('js/plugins/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
@endsection

<div wire:ignore.self>
    @if ($currentPage == 'create')
        @include('livewire.administration.typepay.create')
    @endif

    @if ($currentPage == 'edit')
        @include('livewire.administration.typepay.edit')
    @endif

    @if ($currentPage == 'list')
        @include('livewire.administration.typepay.liste')
    @endif
</div>



<!-- Scripts for UI enhancements -->
<script>
    // Success notification
    window.addEventListener("showSuccessMessage", event => {
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    });
    
    // Error notification
    window.addEventListener("showErrorMessage", event => {
        One.helpers('jq-notify', {
            type: 'danger',
            icon: 'fa fa-times me-1',
            message: event.detail.message || 'Une erreur est survenue!'
        });
    });
    
    // SweetAlert2 confirmation dialog
    window.addEventListener('swal:confirm', event => {
        Swal.fire({
            title: event.detail.title,
            text: event.detail.text,
            icon: event.detail.icon,
            showCancelButton: event.detail.showCancelButton,
            confirmButtonText: event.detail.confirmButtonText,
            cancelButtonText: event.detail.cancelButtonText,
            reverseButtons: true,
            focusCancel: true
        }).then((result) => {
            if (result.isConfirmed) {
                Livewire.emit('delete', event.detail.id);
            }
        });
    });
    
    // Show the replicate modal
    window.addEventListener('openReplicateModal', () => {
        var modal = new bootstrap.Modal(document.getElementById('modal-replicate'));
        modal.show();
    });
    
    // Livewire hook to setup tooltips after updates
    document.addEventListener('livewire:load', function () {
        Livewire.hook('message.processed', () => {
            // Initialize Bootstrap tooltips
            [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function (el) {
                return new bootstrap.Tooltip(el);
            });
        });
    });
</script>

{{-- <script>
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    })
</script> --}}

