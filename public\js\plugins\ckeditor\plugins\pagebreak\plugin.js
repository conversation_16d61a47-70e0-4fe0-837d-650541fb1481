/*
 Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
 For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
(function(){function e(a){return{"aria-label":a,"class":"cke_pagebreak",contenteditable:"false","data-cke-display-name":"pagebreak","data-cke-pagebreak":1,style:"page-break-after: always",title:a}}CKEDITOR.plugins.add("pagebreak",{requires:"fakeobjects",lang:"af,ar,az,bg,bn,bs,ca,cs,cy,da,de,de-ch,el,en,en-au,en-ca,en-gb,eo,es,es-mx,et,eu,fa,fi,fo,fr,fr-ca,gl,gu,he,hi,hr,hu,id,is,it,ja,ka,km,ko,ku,lt,lv,mk,mn,ms,nb,nl,no,oc,pl,pt,pt-br,ro,ru,si,sk,sl,sq,sr,sr-latn,sv,th,tr,tt,ug,uk,vi,zh,zh-cn",icons:"pagebreak,pagebreak-rtl",
hidpi:!0,onLoad:function(){var a=("background:url("+CKEDITOR.getUrl(this.path+"images/pagebreak.gif")+") no-repeat center center;clear:both;width:100%;border-top:#999 1px dotted;border-bottom:#999 1px dotted;padding:0;height:7px;cursor:default;").replace(/;/g," !important;");CKEDITOR.addCss("div.cke_pagebreak{"+a+"}")},init:function(a){a.blockless||(a.addCommand("pagebreak",CKEDITOR.plugins.pagebreakCmd),a.ui.addButton&&a.ui.addButton("PageBreak",{label:a.lang.pagebreak.toolbar,command:"pagebreak",
toolbar:"insert,70"}),CKEDITOR.env.webkit&&a.on("contentDom",function(){a.document.on("click",function(c){c=c.data.getTarget();c.is("div")&&c.hasClass("cke_pagebreak")&&a.getSelection().selectElement(c)})}))},afterInit:function(a){function c(f){CKEDITOR.tools.extend(f.attributes,e(a.lang.pagebreak.alt),!0);f.children.length=0}var b=a.dataProcessor,g=b&&b.dataFilter,b=b&&b.htmlFilter,h=/page-break-after\s*:\s*always/i,k=/display\s*:\s*none/i;b&&b.addRules({attributes:{"class":function(a,c){var b=a.replace("cke_pagebreak",
"");if(b!=a){var d=CKEDITOR.htmlParser.fragment.fromHtml('\x3cspan style\x3d"display: none;"\x3e\x26nbsp;\x3c/span\x3e').children[0];c.children.length=0;c.add(d);d=c.attributes;delete d["aria-label"];delete d.contenteditable;delete d.title}return b}}},{applyToAll:!0,priority:5});g&&g.addRules({elements:{div:function(a){if(a.attributes["data-cke-pagebreak"])c(a);else if(h.test(a.attributes.style)){var b=a.children[0];b&&"span"==b.name&&k.test(b.attributes.style)&&c(a)}}}})}});CKEDITOR.plugins.pagebreakCmd=
{exec:function(a){a.insertElement(CKEDITOR.plugins.pagebreak.createElement(a))},context:"div",allowedContent:{div:{styles:"!page-break-after"},span:{match:function(a){return(a=a.parent)&&"div"==a.name&&a.styles&&a.styles["page-break-after"]},styles:"display"}},requiredContent:"div{page-break-after}"};CKEDITOR.plugins.pagebreak={createElement:function(a){return a.document.createElement("div",{attributes:e(a.lang.pagebreak.alt)})}}})();