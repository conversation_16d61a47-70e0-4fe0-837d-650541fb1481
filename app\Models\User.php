<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Dyrynda\Database\Support\CascadeSoftDeletes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, CascadeSoftDeletes;
    use \Staudenmeir\EloquentHasManyDeep\HasRelationships;
    use \Znck\Eloquent\Traits\BelongsToThrough;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [];

    protected $cascadeDeletes = ['notes', 'info', 'historique'];
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function roles(){
        return $this->belongsToMany(Role::class);
    }

    public function permissions(){
        return $this->belongsToMany(Permission::class, "user_permission", "user_id", "permission_id");
    }

    public function hasRole($role){
        return $this->roles()->where("nom", $role)->first() !== null;
    }

    public function hasAnyRoles($roles){
        return $this->roles()->whereIn("nom", $roles)->first() !== null;
    }

    public function getAllRoleNamesAttribute(){
        return $this->roles->implode("nom", ' | ');
    }


    public function parcours(){
        return $this->belongsTo(Parcour::class, "parcour_id", "id");
    }

    public function matieres(){
        return $this->hasMany(Matiere::class);
    }
    // public function ue(){
    //     return $this->belongsToThrough(Ue::class, Niveau::class);
    // }

    public function info(){
        return $this->hasMany(InscriptionStudent::class);
    }

    public function notes(){
        return $this->hasMany(Note::class);
    }

    public function historique(){
        return $this->hasMany(HistoriquePayment::class);
    }

    public function notesP1(){
        return $this->hasMany(Note::class)->whereTypeNoteId(1);
    }

    public function notesP2(){
        return $this->hasMany(Note::class)->whereTypeNoteId(2);
    }

    public function notesExam(){
        return $this->hasMany(Note::class)->whereTypeNoteId(3);
    }

    public function getMoyenneAttribute()
    {
        $sumP1 = 0;
        $sumP2 = 0;
        $sumEx = 0;
        $count = 0;
        
        foreach ($this->notesP1 as $note) {
            $sumP1 += $note->valeur;
            $count++;
        }

        foreach ($this->notesP2 as $note) {
            $sumP2 += $note->valeur;
            $count++;
        }

        foreach ($this->notesExam as $note) {
            $sumEx += $note->valeur;
            $count = $count +2;
        }

        return $count > 0 ? ($sumP1+$sumP2+($sumEx*2))/$count : 0;
    }

    public function getAllMatiereNamesAttribute(){
        return $this->matieres->implode("nom", ', ');
    }

    public function niveau(){
        return $this->belongsTo(Niveau::class);
    }
}
