{"__meta": {"id": "Xd0e4d8a99847bf00811a1e154e3502b6", "datetime": "2025-07-21 12:27:22", "utime": 1753090042.925809, "method": "POST", "uri": "/livewire/message/certificat-scolarite", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753090041.423854, "end": 1753090042.925844, "duration": 1.5019898414611816, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1753090041.423854, "relative_start": 0, "end": 1753090042.356746, "relative_end": 1753090042.356746, "duration": 0.932891845703125, "duration_str": "933ms", "params": [], "collector": null}, {"label": "Application", "start": 1753090042.357582, "relative_start": 0.9337279796600342, "end": 1753090042.925848, "relative_end": 4.0531158447265625e-06, "duration": 0.5682659149169922, "duration_str": "568ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26190464, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.deraq.certificat.index (\\resources\\views\\livewire\\deraq\\certificat\\index.blade.php)", "param_count": 12, "params": ["livewireLayout", "errors", "_instance", "current_user", "current_parcours", "current_niveau", "current_annee", "inscription", "showDataValidationModal", "editableUserData", "missingFields", "validationErrors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/certificat/index.blade.php&line=0"}, {"name": "livewire.deraq.certificat.modals.data-validation-modal (\\resources\\views\\livewire\\deraq\\certificat\\modals\\data-validation-modal.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "_instance", "livewireLayout", "current_user", "current_parcours", "current_niveau", "current_annee", "inscription", "showDataValidationModal", "editableUserData", "missingFields", "validationErrors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/certificat/modals/data-validation-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.10178000000000001, "accumulated_duration_str": "102ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.02171, "duration_str": "21.71ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 21.33}, {"sql": "select * from `users` where `users`.`id` = 505 limit 1", "type": "query", "params": [], "bindings": ["505"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 21.33, "width_percent": 1.022}, {"sql": "select * from `parcours` where `parcours`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.04555, "duration_str": "45.55ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 22.352, "width_percent": 44.753}, {"sql": "select * from `mentions` where `mentions`.`id` in (9) and `mentions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00342, "duration_str": "3.42ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 67.106, "width_percent": 3.36}, {"sql": "select * from `domaines` where `domaines`.`id` in (1) and `domaines`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 26, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 27, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 28, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00479, "duration_str": "4.79ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 70.466, "width_percent": 4.706}, {"sql": "select * from `niveaux` where `niveaux`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 75.172, "width_percent": 1.169}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00387, "duration_str": "3.87ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 76.341, "width_percent": 3.802}, {"sql": "select * from `inscription_students` where `inscription_students`.`id` = 869 limit 1", "type": "query", "params": [], "bindings": ["869"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.020210000000000002, "duration_str": "20.21ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 80.143, "width_percent": 19.857}]}, "models": {"data": {"App\\Models\\InscriptionStudent": 1, "App\\Models\\AnneeUniversitaire": 1, "App\\Models\\Niveau": 1, "App\\Models\\Domaine": 1, "App\\Models\\Mention": 1, "App\\Models\\Parcour": 1, "App\\Models\\User": 2}, "count": 8}, "livewire": {"data": {"certificat-scolarite #tmMJZ8Zj2hxP7RFqrgwL": "array:5 [\n  \"data\" => array:9 [\n    \"current_user\" => App\\Models\\User {#1692\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:33 [\n        \"id\" => 505\n        \"nom\" => \"qsdfqzeza\"\n        \"prenom\" => \"zaetgdfs\"\n        \"sexe\" => \"H\"\n        \"date_naissance\" => \"02/03/01\"\n        \"lieu_naissance\" => \"SDQF\"\n        \"nationalite\" => null\n        \"ville\" => null\n        \"pays\" => null\n        \"adresse\" => null\n        \"telephone1\" => \"3114\"\n        \"telephone2\" => null\n        \"nom_pere\" => null\n        \"nom_mere\" => null\n        \"cin\" => null\n        \"date_delivrance\" => null\n        \"lieu_delivrance\" => null\n        \"duplicata\" => null\n        \"matricule\" => \"IMSAA/229/25\"\n        \"email\" => null\n        \"password\" => null\n        \"photo\" => \"media/avatars/avatar0.jpg\"\n        \"inscription_date\" => \"15-05-2025\"\n        \"parcour_id\" => 22\n        \"niveau_id\" => 4\n        \"created_at\" => \"2025-05-15 16:55:00\"\n        \"updated_at\" => \"2025-05-15 16:55:29\"\n        \"deleted_at\" => null\n        \"is_filled\" => 1\n        \"tel_pere\" => null\n        \"tel_mere\" => null\n        \"nom_tuteur\" => null\n        \"tel_tuteur\" => null\n      ]\n      #original: array:33 [\n        \"id\" => 505\n        \"nom\" => \"qsdfqzeza\"\n        \"prenom\" => \"zaetgdfs\"\n        \"sexe\" => \"H\"\n        \"date_naissance\" => \"02/03/01\"\n        \"lieu_naissance\" => \"SDQF\"\n        \"nationalite\" => null\n        \"ville\" => null\n        \"pays\" => null\n        \"adresse\" => null\n        \"telephone1\" => \"3114\"\n        \"telephone2\" => null\n        \"nom_pere\" => null\n        \"nom_mere\" => null\n        \"cin\" => null\n        \"date_delivrance\" => null\n        \"lieu_delivrance\" => null\n        \"duplicata\" => null\n        \"matricule\" => \"IMSAA/229/25\"\n        \"email\" => null\n        \"password\" => null\n        \"photo\" => \"media/avatars/avatar0.jpg\"\n        \"inscription_date\" => \"15-05-2025\"\n        \"parcour_id\" => 22\n        \"niveau_id\" => 4\n        \"created_at\" => \"2025-05-15 16:55:00\"\n        \"updated_at\" => \"2025-05-15 16:55:29\"\n        \"deleted_at\" => null\n        \"is_filled\" => 1\n        \"tel_pere\" => null\n        \"tel_mere\" => null\n        \"nom_tuteur\" => null\n        \"tel_tuteur\" => null\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: []\n      #guarded: []\n      #rememberTokenName: \"remember_token\"\n      #cascadeDeletes: array:3 [\n        0 => \"notes\"\n        1 => \"info\"\n        2 => \"historique\"\n      ]\n      #accessToken: null\n      #forceDeleting: false\n    }\n    \"current_parcours\" => App\\Models\\Parcour {#1684\n      #connection: \"mysql\"\n      #table: \"parcours\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 22\n        \"sigle\" => \"ADA\"\n        \"nom\" => \"Administration des affaires\"\n        \"mention_id\" => 9\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 22\n        \"sigle\" => \"ADA\"\n        \"nom\" => \"Administration des affaires\"\n        \"mention_id\" => 9\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"mention\" => App\\Models\\Mention {#1751\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 9\n            \"nom\" => \"Management des affaires Internationale \"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 9\n            \"nom\" => \"Management des affaires Internationale \"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"domaine\" => App\\Models\\Domaine {#1800\n              #connection: \"mysql\"\n              #table: \"domaines\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:3 [\n                \"id\" => 1\n                \"nom\" => \"Science de la société\"\n                \"deleted_at\" => null\n              ]\n              #original: array:3 [\n                \"id\" => 1\n                \"nom\" => \"Science de la société\"\n                \"deleted_at\" => null\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n          ]\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:3 [\n        0 => \"nom\"\n        1 => \"sigle\"\n        2 => \"mention_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1818\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 4\n        \"nom\" => \"4ème année\"\n        \"sigle\" => \"M1\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 4\n        \"nom\" => \"4ème année\"\n        \"sigle\" => \"M1\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1827\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 6\n        \"nom\" => \"2024/2025\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 6\n        \"nom\" => \"2024/2025\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"inscription\" => App\\Models\\InscriptionStudent {#1836\n      #connection: \"mysql\"\n      #table: \"inscription_students\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:8 [\n        \"id\" => 869\n        \"annee_universitaire_id\" => 6\n        \"user_id\" => 505\n        \"parcour_id\" => 22\n        \"niveau_id\" => 4\n        \"created_at\" => \"2025-05-15 16:55:00\"\n        \"updated_at\" => \"2025-05-15 16:55:29\"\n        \"deleted_at\" => null\n      ]\n      #original: array:8 [\n        \"id\" => 869\n        \"annee_universitaire_id\" => 6\n        \"user_id\" => 505\n        \"parcour_id\" => 22\n        \"niveau_id\" => 4\n        \"created_at\" => \"2025-05-15 16:55:00\"\n        \"updated_at\" => \"2025-05-15 16:55:29\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"user_id\"\n        1 => \"annee_universitaire_id\"\n        2 => \"parcour_id\"\n        3 => \"niveau_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"showDataValidationModal\" => true\n    \"editableUserData\" => array:32 [\n      \"id\" => 505\n      \"nom\" => \"qsdfqzeza\"\n      \"prenom\" => \"zaetgdfs\"\n      \"sexe\" => \"H\"\n      \"date_naissance\" => \"02/03/01\"\n      \"lieu_naissance\" => \"SDQF\"\n      \"nationalite\" => null\n      \"ville\" => null\n      \"pays\" => null\n      \"adresse\" => null\n      \"telephone1\" => \"3114\"\n      \"telephone2\" => null\n      \"nom_pere\" => null\n      \"nom_mere\" => null\n      \"cin\" => null\n      \"date_delivrance\" => null\n      \"lieu_delivrance\" => null\n      \"duplicata\" => null\n      \"matricule\" => \"IMSAA/229/25\"\n      \"email\" => null\n      \"photo\" => \"media/avatars/avatar0.jpg\"\n      \"inscription_date\" => \"15-05-2025\"\n      \"parcour_id\" => 22\n      \"niveau_id\" => 4\n      \"created_at\" => \"2025-05-15T13:55:00.000000Z\"\n      \"updated_at\" => \"2025-05-15T13:55:29.000000Z\"\n      \"deleted_at\" => null\n      \"is_filled\" => 1\n      \"tel_pere\" => null\n      \"tel_mere\" => null\n      \"nom_tuteur\" => null\n      \"tel_tuteur\" => null\n    ]\n    \"missingFields\" => array:5 [\n      \"nom_pere\" => \"Nom du père\"\n      \"nom_mere\" => \"Nom de la mère\"\n      \"cin\" => \"Numéro CIN\"\n      \"date_delivrance\" => \"Date de délivrance CIN\"\n      \"lieu_delivrance\" => \"Lieu de délivrance CIN\"\n    ]\n    \"validationErrors\" => []\n  ]\n  \"name\" => \"certificat-scolarite\"\n  \"view\" => \"livewire.deraq.certificat.index\"\n  \"component\" => \"App\\Http\\Livewire\\CertificatScolarite\"\n  \"id\" => \"tmMJZ8Zj2hxP7RFqrgwL\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ArW6lJ6JQUbYgAMsgdUMOjDp5tJ3LmxauntXPbaD", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants/505/22/4/6/certificat\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753089943\n]"}, "request": {"path_info": "/livewire/message/certificat-scolarite", "status_code": "<pre class=sf-dump id=sf-dump-1201603705 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1201603705\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-784008113 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-784008113\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-353605430 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">tmMJZ8Zj2hxP7RFqrgwL</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">certificat-scolarite</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gestions/etudiants/505/22/4/6/certificat</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">ee156f32</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>current_user</span>\" => []\n      \"<span class=sf-dump-key>current_parcours</span>\" => []\n      \"<span class=sf-dump-key>current_niveau</span>\" => []\n      \"<span class=sf-dump-key>current_annee</span>\" => []\n      \"<span class=sf-dump-key>inscription</span>\" => []\n      \"<span class=sf-dump-key>showDataValidationModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>editableUserData</span>\" => []\n      \"<span class=sf-dump-key>missingFields</span>\" => []\n      \"<span class=sf-dump-key>validationErrors</span>\" => []\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>models</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>current_user</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>505</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_parcours</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Parcour</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>22</span>\n          \"<span class=sf-dump-key>relations</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">mention</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">mention.domaine</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Niveau</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\AnneeUniversitaire</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>inscription</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\InscriptionStudent</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>869</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">6c2e93dcb5b53f887f4d17be71db6ed52b41a7e412baae7d1620dd50a2ed7293</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mns2</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"23 characters\">openDataValidationModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353605430\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2097597348 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1260</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ArW6lJ6JQUbYgAMsgdUMOjDp5tJ3LmxauntXPbaD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/gestions/etudiants/505/22/4/6/certificat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik9pelY1NklCSUJ2VzhTdkFCYUZmZ2c9PSIsInZhbHVlIjoiZUFBaXZvMytnMks1YmdSMHpGaDV4R290UUkrQUV6L3d2SThlUFp3b1BVbFhWUUdFNzhVdnRmUEU4WG9FS0t2QzNhUWVvb29QbkF6eWEreldmUkVFL1Vrc09FTlA2cUQ0bUdKaEVHQW9qSUc2c0t5bUlJTnoxbEdlNU44SFZMNEwiLCJtYWMiOiJhNGUyMGFlNzViMTM3OWFjZDA0MWI1NTVhNmVlMjQwOWE3YmIwOWI1MTFiYzA3MjUxZDEzZTVhMjE4OTJlMWQwIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImE5RGRHS3dRVDBDSlBPQVVZbjNCL3c9PSIsInZhbHVlIjoiSHIzOEdvTHJzellhakFEbzluMFFlZUxlTnhJUXRna05jb3BDL3FlWFlNRllVVWd2T3ZFR0ZOb09CaW5mV2FZZ09JaFVMbUdQUGRhWmtBK1ZneTNjMmZ2ZVVQRjRwYkhrVDVmZ1BVdWx3dWpGaHE5VS9lQjlRNWxEa1paS1pFTXgiLCJtYWMiOiIwODFmOTkxOThhYTlmNDI5NWJmYWZlNTBiMzc1MTFkZjdjNDUxYjA4MGFlZGE4N2ZiMWE0N2RiYWI0ZDZjZDgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097597348\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-952630042 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53344</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/livewire/message/certificat-scolarite</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/livewire/message/certificat-scolarite</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"48 characters\">/index.php/livewire/message/certificat-scolarite</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1260</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1260</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/gestions/etudiants/505/22/4/6/certificat</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik9pelY1NklCSUJ2VzhTdkFCYUZmZ2c9PSIsInZhbHVlIjoiZUFBaXZvMytnMks1YmdSMHpGaDV4R290UUkrQUV6L3d2SThlUFp3b1BVbFhWUUdFNzhVdnRmUEU4WG9FS0t2QzNhUWVvb29QbkF6eWEreldmUkVFL1Vrc09FTlA2cUQ0bUdKaEVHQW9qSUc2c0t5bUlJTnoxbEdlNU44SFZMNEwiLCJtYWMiOiJhNGUyMGFlNzViMTM3OWFjZDA0MWI1NTVhNmVlMjQwOWE3YmIwOWI1MTFiYzA3MjUxZDEzZTVhMjE4OTJlMWQwIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImE5RGRHS3dRVDBDSlBPQVVZbjNCL3c9PSIsInZhbHVlIjoiSHIzOEdvTHJzellhakFEbzluMFFlZUxlTnhJUXRna05jb3BDL3FlWFlNRllVVWd2T3ZFR0ZOb09CaW5mV2FZZ09JaFVMbUdQUGRhWmtBK1ZneTNjMmZ2ZVVQRjRwYkhrVDVmZ1BVdWx3dWpGaHE5VS9lQjlRNWxEa1paS1pFTXgiLCJtYWMiOiIwODFmOTkxOThhYTlmNDI5NWJmYWZlNTBiMzc1MTFkZjdjNDUxYjA4MGFlZGE4N2ZiMWE0N2RiYWI0ZDZjZDgyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753090041.4239</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753090041</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-952630042\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-165341950 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ArW6lJ6JQUbYgAMsgdUMOjDp5tJ3LmxauntXPbaD</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9KVT4LDfjMyjJuBMto6pQDMCKQ9w02yaevv9f6dY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165341950\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-24096439 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 09:27:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImdxSWprSjFMS0N1ckh0cjF3dnR0Ync9PSIsInZhbHVlIjoiVHJBUy9hUUc0dlBsMmMyOGxWMjNPUFRrZmdyRnVBRTM0UHJKallhMEJJb1V4eGJaQnJSRGU5d1VzUGYyMzIyeE5BUm9EV0svUXNPTXhWQXZhQ0ZuOWRtemIvQ2FRTjJwTjBWQkhqZzhUcUlwc2NlRThESWVUMXVKaVMxK0NWSysiLCJtYWMiOiJhNzc2ZGZiZjljMGVhMjk0YTU1M2E0Mzk1MjM4YzU4NmU5OWQxY2VhNWY5Y2FlNDk4NDcwODgyYWU5ZWFhMzc2IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 11:27:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Inp2aFh4ODl2SEVSd3RuMlNOcitXUVE9PSIsInZhbHVlIjoiZGMzKzJJRGNOTTBOcGdpUjdBUlM5KzV3TW1jSE5BaEFFNndsTGR5VGdaZUc2VVhvV1RKT21lZ1RpOTVCZlBSMEhSek5JS1c0M3pYSS9KV0E2VnpiRDFjdzhNWmluTHU4SkF3U0x1QlZWUGdIcGVXWldqQlMwd3dyazdrUmZmcXYiLCJtYWMiOiI4NmE3MzI0OTdkNDIzOWMzYThmOTQyNzlmNTE3M2VjN2RhMTFlZTYyYzI0NDllNDBiOWRhM2Q5MjgwOTNlMGQ3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 11:27:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImdxSWprSjFMS0N1ckh0cjF3dnR0Ync9PSIsInZhbHVlIjoiVHJBUy9hUUc0dlBsMmMyOGxWMjNPUFRrZmdyRnVBRTM0UHJKallhMEJJb1V4eGJaQnJSRGU5d1VzUGYyMzIyeE5BUm9EV0svUXNPTXhWQXZhQ0ZuOWRtemIvQ2FRTjJwTjBWQkhqZzhUcUlwc2NlRThESWVUMXVKaVMxK0NWSysiLCJtYWMiOiJhNzc2ZGZiZjljMGVhMjk0YTU1M2E0Mzk1MjM4YzU4NmU5OWQxY2VhNWY5Y2FlNDk4NDcwODgyYWU5ZWFhMzc2IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 11:27:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Inp2aFh4ODl2SEVSd3RuMlNOcitXUVE9PSIsInZhbHVlIjoiZGMzKzJJRGNOTTBOcGdpUjdBUlM5KzV3TW1jSE5BaEFFNndsTGR5VGdaZUc2VVhvV1RKT21lZ1RpOTVCZlBSMEhSek5JS1c0M3pYSS9KV0E2VnpiRDFjdzhNWmluTHU4SkF3U0x1QlZWUGdIcGVXWldqQlMwd3dyazdrUmZmcXYiLCJtYWMiOiI4NmE3MzI0OTdkNDIzOWMzYThmOTQyNzlmNTE3M2VjN2RhMTFlZTYyYzI0NDllNDBiOWRhM2Q5MjgwOTNlMGQ3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 11:27:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24096439\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-888190470 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ArW6lJ6JQUbYgAMsgdUMOjDp5tJ3LmxauntXPbaD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/gestions/etudiants/505/22/4/6/certificat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753089943</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888190470\", {\"maxDepth\":0})</script>\n"}}