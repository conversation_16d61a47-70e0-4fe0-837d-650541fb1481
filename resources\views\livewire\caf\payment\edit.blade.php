
<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="h3 fw-bold mb-2">
                Paiment pour {{ $current_user->nom }} {{ $current_user->prenom }} | {{ $current_annee->nom }}
            </h1>
            <span class="badge bg-primary">ID: {{ $current_user->id }}</span>
        </div>
    </div>
</div>
<!-- END Hero -->

<div class="content">
    <!-- Payment Summary Card -->
    <div class="block block-rounded mb-4">
        <div class="block-header block-header-default">
            <h3 class="block-title">Résumé des Paiements</h3>
        </div>
        <div class="block-content">
            <div class="row">
                @php
                    $totalPaid = 0;
                    $totalRequired = 0;
                    
                    foreach ($newPay as $payment) {
                        $histoPaid = \App\Models\HistoriquePayment::where('user_id', $current_user->id)
                            ->where('annee_universitaire_id', $current_annee->id)
                            ->where('type_payment_id', $payment['type_id'])
                            ->sum('montant');
                            
                        $totalPaid += $histoPaid;
                        $totalRequired += $payment['max'];
                    }
                    
                    $paymentPercentage = $totalRequired > 0 ? ($totalPaid / $totalRequired) * 100 : 0;
                @endphp
                
                <div class="col-md-6">
                    <div class="block block-rounded bg-body-light">
                        <div class="block-content">
                            <div class="py-3">
                                <div class="fs-1 fw-bold text-primary text-center">{{ number_format($totalPaid, 0, ',', ' ') }} {{ env('CURRENCY', 'Ar') }}</div>
                                <div class="fs-sm fw-semibold text-uppercase text-muted text-center">Total Payé</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="block block-rounded bg-body-light">
                        <div class="block-content">
                            <div class="py-3">
                                <div class="fs-1 fw-bold text-danger text-center">{{ number_format($totalRequired - $totalPaid, 0, ',', ' ') }} {{ env('CURRENCY', 'Ar') }}</div>
                                <div class="fs-sm fw-semibold text-uppercase text-muted text-center">Reste à Payer</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mt-3">
                    <div class="progress push" style="height: 1.5rem;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ min(100, $paymentPercentage) }}%;" aria-valuenow="{{ $paymentPercentage }}" aria-valuemin="0" aria-valuemax="100">
                            <span class="fs-sm fw-semibold">{{ round($paymentPercentage) }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment Categories -->
    <div class="row">
        @foreach ($newPay as $index => $payment)
            @php
                $total = 0;
                $histos = \App\Models\HistoriquePayment::with('moyen')
                    ->where('user_id', $current_user->id)
                    ->where('annee_universitaire_id', $current_annee->id)
                    ->where('type_payment_id', $payment['type_id'])
                    ->get();
                
                foreach ($histos as $histo) {
                    $total += $histo->montant;
                }
                
                $paymentPercentage = $payment['max'] > 0 ? ($total / $payment['max']) * 100 : 0;
                $isPaid = $total >= $payment['max'] && $payment['max'] > 0;
                $isSpecialPayment = in_array($payment['type_nom'], ['Fiche de stage', 'Certificat de scolarité']);
            @endphp
            
            <div class="col-md-6">
                <div class="block block-rounded block-themed">
                    <div class="block-header {{ $isPaid ? 'bg-success' : 'bg-primary' }}">
                        <h3 class="block-title">{{ $payment['type_nom'] }}</h3>
                        <div class="block-options">
                            @if ($isPaid && !$isSpecialPayment)
                                <span class="badge bg-white text-success"><i class="fa fa-check me-1"></i> Payé</span>
                            @else
                                <span class="badge bg-white text-primary">
                                    {{ number_format($total, 0, ',', ' ') }} / {{ number_format($payment['max'], 0, ',', ' ') }} {{ env('CURRENCY', 'Ar') }}
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="block-content">
                        @if ($histos->isNotEmpty())
                            <!-- Progress Bar -->
                            @if ($payment['max'] > 0)
                                <div class="progress push" style="height: 0.5rem;">
                                    <div class="progress-bar {{ $isPaid ? 'bg-success' : 'bg-primary' }}" role="progressbar" style="width: {{ min(100, $paymentPercentage) }}%;" aria-valuenow="{{ $paymentPercentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            @endif
                            
                            <!-- Payment History -->
                            <div class="table-responsive">
                                <table class="table table-sm table-vcenter">
                                    <thead>
                                        <tr>
                                            <th>Moyen</th>
                                            <th>Code</th>
                                            <th>Montant</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($histos as $histo)
                                            <tr>
                                                <td>
                                                    <span class="badge bg-primary">{{ $histo->moyen->nom }}</span>
                                                </td>
                                                <td><code>{{ $histo->code }}</code></td>
                                                <td class="fw-semibold">{{ $histo->prixForHumans }}</td>
                                                <td class="fs-sm text-muted">{{ $histo->created_at->format('d/m/Y H:i') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif

                        @if ($isPaid == false || $isSpecialPayment)
                            <!-- New Payment Form -->
                            <div class="block block-rounded block-fx-shadow mt-3">
                                <div class="block-content block-content-full bg-body-light">
                                    <h4 class="fs-sm text-uppercase fw-semibold text-center mb-3">
                                        {{ $histos->isNotEmpty() ? 'Paiement du reste' : 'Nouveau Paiement' }}
                                    </h4>
                                </div>
                                <div class="block-content">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <div class="form-floating">
                                                <select class="form-select @error('newPay.' . $index . '.moyen') is-invalid @enderror"
                                                    wire:model="newPay.{{ $index }}.moyen" id="payment-method-{{ $index }}">
                                                    <option selected value="null">Sélectionner</option>
                                                    @foreach ($moyens as $moyen)
                                                        <option value="{{ $moyen->id }}">{{ $moyen->nom }}</option>
                                                    @endforeach
                                                </select>
                                                <label for="payment-method-{{ $index }}">Moyen de paiement</label>
                                                @error('newPay.' . $index . '.moyen')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating">
                                                <input type="text" 
                                                    wire:model="newPay.{{ $index }}.code"
                                                    class="form-control @error('newPay.' . $index . '.code') is-invalid @enderror"
                                                    id="payment-code-{{ $index }}" placeholder="Code">
                                                <label for="payment-code-{{ $index }}">Code du reçu</label>
                                                @error('newPay.' . $index . '.code')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating">
                                                <input type="number" 
                                                    wire:model="newPay.{{ $index }}.montant"
                                                    class="form-control @error('newPay.' . $index . '.montant') is-invalid @enderror"
                                                    id="payment-amount-{{ $index }}" placeholder="0">
                                                <label for="payment-amount-{{ $index }}">Montant</label>
                                                @error('newPay.' . $index . '.montant')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-12 text-center">
                                            <button type="button" 
                                                wire:click="valider({{ $index }})"
                                                wire:loading.attr="disabled"
                                                class="btn btn-alt-primary"
                                                @disabled(empty($newPay[$index]['moyen']) || empty($newPay[$index]['code']) || empty($newPay[$index]['montant']))>
                                                <div wire:loading wire:target="valider({{ $index }})" class="spinner-border spinner-border-sm text-light me-2" role="status"></div>
                                                Enregistrer le paiement
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
