{"__meta": {"id": "X925a280bc9e2f9fcc444824fbd995d1b", "datetime": "2025-07-21 12:54:28", "utime": 1753091668.81486, "method": "POST", "uri": "/livewire/message/certificat-scolarite", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753091668.259884, "end": 1753091668.814888, "duration": 0.5550038814544678, "duration_str": "555ms", "measures": [{"label": "Booting", "start": 1753091668.259884, "relative_start": 0, "end": 1753091668.614439, "relative_end": 1753091668.614439, "duration": 0.3545548915863037, "duration_str": "355ms", "params": [], "collector": null}, {"label": "Application", "start": 1753091668.615271, "relative_start": 0.35538697242736816, "end": 1753091668.814891, "relative_end": 3.0994415283203125e-06, "duration": 0.19962000846862793, "duration_str": "200ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26160936, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.deraq.certificat.index (\\resources\\views\\livewire\\deraq\\certificat\\index.blade.php)", "param_count": 12, "params": ["livewireLayout", "errors", "_instance", "current_user", "current_parcours", "current_niveau", "current_annee", "inscription", "showDataValidationModal", "editableUserData", "missingFields", "validationErrors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/certificat/index.blade.php&line=0"}, {"name": "livewire.deraq.certificat.modals.data-validation-modal (\\resources\\views\\livewire\\deraq\\certificat\\modals\\data-validation-modal.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "_instance", "livewireLayout", "current_user", "current_parcours", "current_niveau", "current_annee", "inscription", "showDataValidationModal", "editableUserData", "missingFields", "validationErrors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/certificat/modals/data-validation-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.010329999999999999, "accumulated_duration_str": "10.33ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.004900000000000001, "duration_str": "4.9ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 47.435}, {"sql": "select * from `users` where `users`.`id` = 103 limit 1", "type": "query", "params": [], "bindings": ["103"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 47.435, "width_percent": 11.423}, {"sql": "select * from `parcours` where `parcours`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 58.858, "width_percent": 7.067}, {"sql": "select * from `mentions` where `mentions`.`id` in (2) and `mentions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 65.924, "width_percent": 6.389}, {"sql": "select * from `domaines` where `domaines`.`id` in (1) and `domaines`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 26, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 27, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 28, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 72.314, "width_percent": 7.26}, {"sql": "select * from `niveaux` where `niveaux`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 79.574, "width_percent": 6.776}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 86.35, "width_percent": 6.68}, {"sql": "select * from `inscription_students` where `inscription_students`.`id` = 101 limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 93.03, "width_percent": 6.97}]}, "models": {"data": {"App\\Models\\InscriptionStudent": 1, "App\\Models\\AnneeUniversitaire": 1, "App\\Models\\Niveau": 1, "App\\Models\\Domaine": 1, "App\\Models\\Mention": 1, "App\\Models\\Parcour": 1, "App\\Models\\User": 2}, "count": 8}, "livewire": {"data": {"certificat-scolarite #E2o8uyCkTXHHh9QW5WV6": "array:5 [\n  \"data\" => array:9 [\n    \"current_user\" => App\\Models\\User {#1692\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:33 [\n        \"id\" => 103\n        \"nom\" => \"ANJARASOA\"\n        \"prenom\" => \"Eliane\"\n        \"sexe\" => null\n        \"date_naissance\" => \"23/07/2003\"\n        \"lieu_naissance\" => \"ANTSIRANANA \"\n        \"nationalite\" => null\n        \"ville\" => null\n        \"pays\" => null\n        \"adresse\" => \"MORAFENO\"\n        \"telephone1\" => \"321753373\"\n        \"telephone2\" => null\n        \"nom_pere\" => \"FORANO\"\n        \"nom_mere\" => \"LESO\"\n        \"cin\" => \"\"\n        \"date_delivrance\" => \"\"\n        \"lieu_delivrance\" => \"\"\n        \"duplicata\" => \"\"\n        \"matricule\" => null\n        \"email\" => null\n        \"password\" => null\n        \"photo\" => null\n        \"inscription_date\" => \"13/10/2022\"\n        \"parcour_id\" => 10\n        \"niveau_id\" => 3\n        \"created_at\" => \"2023-06-06 16:19:39\"\n        \"updated_at\" => \"2024-10-04 18:03:18\"\n        \"deleted_at\" => null\n        \"is_filled\" => 0\n        \"tel_pere\" => null\n        \"tel_mere\" => null\n        \"nom_tuteur\" => null\n        \"tel_tuteur\" => null\n      ]\n      #original: array:33 [\n        \"id\" => 103\n        \"nom\" => \"ANJARASOA\"\n        \"prenom\" => \"Eliane\"\n        \"sexe\" => null\n        \"date_naissance\" => \"23/07/2003\"\n        \"lieu_naissance\" => \"ANTSIRANANA \"\n        \"nationalite\" => null\n        \"ville\" => null\n        \"pays\" => null\n        \"adresse\" => \"MORAFENO\"\n        \"telephone1\" => \"321753373\"\n        \"telephone2\" => null\n        \"nom_pere\" => \"FORANO\"\n        \"nom_mere\" => \"LESO\"\n        \"cin\" => \"\"\n        \"date_delivrance\" => \"\"\n        \"lieu_delivrance\" => \"\"\n        \"duplicata\" => \"\"\n        \"matricule\" => null\n        \"email\" => null\n        \"password\" => null\n        \"photo\" => null\n        \"inscription_date\" => \"13/10/2022\"\n        \"parcour_id\" => 10\n        \"niveau_id\" => 3\n        \"created_at\" => \"2023-06-06 16:19:39\"\n        \"updated_at\" => \"2024-10-04 18:03:18\"\n        \"deleted_at\" => null\n        \"is_filled\" => 0\n        \"tel_pere\" => null\n        \"tel_mere\" => null\n        \"nom_tuteur\" => null\n        \"tel_tuteur\" => null\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: []\n      #guarded: []\n      #rememberTokenName: \"remember_token\"\n      #cascadeDeletes: array:3 [\n        0 => \"notes\"\n        1 => \"info\"\n        2 => \"historique\"\n      ]\n      #accessToken: null\n      #forceDeleting: false\n    }\n    \"current_parcours\" => App\\Models\\Parcour {#1684\n      #connection: \"mysql\"\n      #table: \"parcours\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"mention\" => App\\Models\\Mention {#1751\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 2\n            \"nom\" => \"Management des Opérations du Commerce International\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 2\n            \"nom\" => \"Management des Opérations du Commerce International\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"domaine\" => App\\Models\\Domaine {#1800\n              #connection: \"mysql\"\n              #table: \"domaines\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:3 [\n                \"id\" => 1\n                \"nom\" => \"Science de la société\"\n                \"deleted_at\" => null\n              ]\n              #original: array:3 [\n                \"id\" => 1\n                \"nom\" => \"Science de la société\"\n                \"deleted_at\" => null\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n          ]\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:3 [\n        0 => \"nom\"\n        1 => \"sigle\"\n        2 => \"mention_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1818\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1827\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"inscription\" => App\\Models\\InscriptionStudent {#1836\n      #connection: \"mysql\"\n      #table: \"inscription_students\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:8 [\n        \"id\" => 101\n        \"annee_universitaire_id\" => 4\n        \"user_id\" => 103\n        \"parcour_id\" => 5\n        \"niveau_id\" => 1\n        \"created_at\" => \"2023-06-06 16:19:39\"\n        \"updated_at\" => \"2023-06-06 16:19:39\"\n        \"deleted_at\" => null\n      ]\n      #original: array:8 [\n        \"id\" => 101\n        \"annee_universitaire_id\" => 4\n        \"user_id\" => 103\n        \"parcour_id\" => 5\n        \"niveau_id\" => 1\n        \"created_at\" => \"2023-06-06 16:19:39\"\n        \"updated_at\" => \"2023-06-06 16:19:39\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"user_id\"\n        1 => \"annee_universitaire_id\"\n        2 => \"parcour_id\"\n        3 => \"niveau_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"showDataValidationModal\" => true\n    \"editableUserData\" => array:32 [\n      \"id\" => 103\n      \"nom\" => \"ANJARASOA\"\n      \"prenom\" => \"Eliane\"\n      \"sexe\" => null\n      \"date_naissance\" => \"23/07/2003\"\n      \"lieu_naissance\" => \"ANTSIRANANA \"\n      \"nationalite\" => null\n      \"ville\" => null\n      \"pays\" => null\n      \"adresse\" => \"MORAFENO\"\n      \"telephone1\" => \"321753373\"\n      \"telephone2\" => null\n      \"nom_pere\" => \"FORANO\"\n      \"nom_mere\" => \"LESO\"\n      \"cin\" => \"\"\n      \"date_delivrance\" => \"\"\n      \"lieu_delivrance\" => \"\"\n      \"duplicata\" => \"\"\n      \"matricule\" => null\n      \"email\" => null\n      \"photo\" => null\n      \"inscription_date\" => \"13/10/2022\"\n      \"parcour_id\" => 10\n      \"niveau_id\" => 3\n      \"created_at\" => \"2023-06-06T13:19:39.000000Z\"\n      \"updated_at\" => \"2024-10-04T15:03:18.000000Z\"\n      \"deleted_at\" => null\n      \"is_filled\" => 0\n      \"tel_pere\" => null\n      \"tel_mere\" => null\n      \"nom_tuteur\" => null\n      \"tel_tuteur\" => null\n    ]\n    \"missingFields\" => array:3 [\n      \"cin\" => \"Numéro CIN\"\n      \"date_delivrance\" => \"Date de délivrance CIN\"\n      \"lieu_delivrance\" => \"Lieu de délivrance CIN\"\n    ]\n    \"validationErrors\" => []\n  ]\n  \"name\" => \"certificat-scolarite\"\n  \"view\" => \"livewire.deraq.certificat.index\"\n  \"component\" => \"App\\Http\\Livewire\\CertificatScolarite\"\n  \"id\" => \"E2o8uyCkTXHHh9QW5WV6\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ArW6lJ6JQUbYgAMsgdUMOjDp5tJ3LmxauntXPbaD", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants/103/5/1/4/certificat\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753089943\n]"}, "request": {"path_info": "/livewire/message/certificat-scolarite", "status_code": "<pre class=sf-dump id=sf-dump-1806357290 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1806357290\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-240563790 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-240563790\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-190640564 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">E2o8uyCkTXHHh9QW5WV6</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">certificat-scolarite</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"39 characters\">gestions/etudiants/103/5/1/4/certificat</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">451fdea4</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>current_user</span>\" => []\n      \"<span class=sf-dump-key>current_parcours</span>\" => []\n      \"<span class=sf-dump-key>current_niveau</span>\" => []\n      \"<span class=sf-dump-key>current_annee</span>\" => []\n      \"<span class=sf-dump-key>inscription</span>\" => []\n      \"<span class=sf-dump-key>showDataValidationModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>editableUserData</span>\" => <span class=sf-dump-note>array:32</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>103</span>\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ANJARASOA</span>\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Eliane</span>\"\n        \"<span class=sf-dump-key>sexe</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">23/07/2003</span>\"\n        \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"12 characters\">ANTSIRANANA </span>\"\n        \"<span class=sf-dump-key>nationalite</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>ville</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>pays</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"8 characters\">MORAFENO</span>\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"9 characters\">321753373</span>\"\n        \"<span class=sf-dump-key>telephone2</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>nom_pere</span>\" => \"<span class=sf-dump-str title=\"6 characters\">FORANO</span>\"\n        \"<span class=sf-dump-key>nom_mere</span>\" => \"<span class=sf-dump-str title=\"4 characters\">LESO</span>\"\n        \"<span class=sf-dump-key>cin</span>\" => \"\"\n        \"<span class=sf-dump-key>date_delivrance</span>\" => \"\"\n        \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"\"\n        \"<span class=sf-dump-key>duplicata</span>\" => \"\"\n        \"<span class=sf-dump-key>matricule</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>inscription_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">13/10/2022</span>\"\n        \"<span class=sf-dump-key>parcour_id</span>\" => <span class=sf-dump-num>10</span>\n        \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2023-06-06T13:19:39.000000Z</span>\"\n        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2024-10-04T15:03:18.000000Z</span>\"\n        \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_filled</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>tel_pere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tel_mere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>nom_tuteur</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tel_tuteur</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      \"<span class=sf-dump-key>missingFields</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Num&#233;ro CIN</span>\"\n        \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Date de d&#233;livrance CIN</span>\"\n        \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Lieu de d&#233;livrance CIN</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>validationErrors</span>\" => []\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>models</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>current_user</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>103</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_parcours</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Parcour</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n          \"<span class=sf-dump-key>relations</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">mention</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">mention.domaine</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Niveau</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\AnneeUniversitaire</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>inscription</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\InscriptionStudent</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>101</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">b43114549d522b4f211b513711bcac0f5247467c4e1848c9fec6e982dda79369</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">7cm4i</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"23 characters\">openDataValidationModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9s5u</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"23 characters\">openDataValidationModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">i52c</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"23 characters\">openDataValidationModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">xz7e</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"23 characters\">openDataValidationModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">igw4</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"23 characters\">openDataValidationModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190640564\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1445214913 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2358</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ArW6lJ6JQUbYgAMsgdUMOjDp5tJ3LmxauntXPbaD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/gestions/etudiants/103/5/1/4/certificat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImhNM3VGOUUzUW12YUR5bU5Nb2JMTkE9PSIsInZhbHVlIjoiV09kKzdkaTFNb01hRjZENUZDdkV6M0lmS2dDR0ZFd1lIbUdnOFE1NVNEaFltV2hkWjRycWE4YU5ISzhrb3ZqT2ZxT3FUTnhXbkVXZzYwaDRUZmw1WXdrRDl6Qk1LUTU5ME1WN1dsSFl2MGVZS3NTR3p5VjJxdVlSYXd4d1lBbFQiLCJtYWMiOiJhYWIwODA4ZDQ3MmY3NWE3YmM1NzI2YzU2ZWI3OGZjYmYxYWM0MDk1OGMyMzIxM2ZkOGFkZmQ1NzRhNTA5NjNiIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Im9IQVp6ZXBxOFVkOXkrNUJka2IvZmc9PSIsInZhbHVlIjoiejIrK2ppOStWa1ZGQUhadW5yQThCWEo4RERYbURoNXEzbUFXYjRMbGdWbTFtYk54ZlgrQllLbzFPTTI1bXZNSzhxSlNSSlpHVE5wTDVReE1hYlJRYWlyVis0Y010RHorb2hPUTBwYjdQRUNBa2szckZVbFlTTWVBVkdBL0xtZFgiLCJtYWMiOiJkZTc5MDRiZTEzMmY4YWFhYmViZDEyODZlZTRhZmExZDY0Mzg4MWRhOTE5ODY0NDAxZDVhZWI2ZTE3MzliOTA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445214913\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1182844685 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54806</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/livewire/message/certificat-scolarite</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/livewire/message/certificat-scolarite</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"48 characters\">/index.php/livewire/message/certificat-scolarite</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2358</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2358</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/gestions/etudiants/103/5/1/4/certificat</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImhNM3VGOUUzUW12YUR5bU5Nb2JMTkE9PSIsInZhbHVlIjoiV09kKzdkaTFNb01hRjZENUZDdkV6M0lmS2dDR0ZFd1lIbUdnOFE1NVNEaFltV2hkWjRycWE4YU5ISzhrb3ZqT2ZxT3FUTnhXbkVXZzYwaDRUZmw1WXdrRDl6Qk1LUTU5ME1WN1dsSFl2MGVZS3NTR3p5VjJxdVlSYXd4d1lBbFQiLCJtYWMiOiJhYWIwODA4ZDQ3MmY3NWE3YmM1NzI2YzU2ZWI3OGZjYmYxYWM0MDk1OGMyMzIxM2ZkOGFkZmQ1NzRhNTA5NjNiIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Im9IQVp6ZXBxOFVkOXkrNUJka2IvZmc9PSIsInZhbHVlIjoiejIrK2ppOStWa1ZGQUhadW5yQThCWEo4RERYbURoNXEzbUFXYjRMbGdWbTFtYk54ZlgrQllLbzFPTTI1bXZNSzhxSlNSSlpHVE5wTDVReE1hYlJRYWlyVis0Y010RHorb2hPUTBwYjdQRUNBa2szckZVbFlTTWVBVkdBL0xtZFgiLCJtYWMiOiJkZTc5MDRiZTEzMmY4YWFhYmViZDEyODZlZTRhZmExZDY0Mzg4MWRhOTE5ODY0NDAxZDVhZWI2ZTE3MzliOTA0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753091668.2599</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753091668</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182844685\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1003342246 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ArW6lJ6JQUbYgAMsgdUMOjDp5tJ3LmxauntXPbaD</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9KVT4LDfjMyjJuBMto6pQDMCKQ9w02yaevv9f6dY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003342246\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-443631810 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 09:54:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjFucnNjeVQ0VkQxb1VvMUdkd1d4TWc9PSIsInZhbHVlIjoicDZWd05RaThHYjZWUUNVTElEcndVRnRkNjc3dHpMMEMrOWVpNjhiU0xmdnN0a1llVVIrV2dKenowMzJ6cy82QW5oYmhzZTdQZ1JhMC84OEd4RXo5N2tJZHV2M1V3OCtXYVhYYXdLWk0wb3dDVzVuZUpKb3Z6ckdSbENwSS9tNG0iLCJtYWMiOiI5N2IxMWMzNzBkNGM4ZTEyNWEyZGM2ZGI2YjczNjlhYWRhYmExNzMyZWE0YTdmNDgwNjUzYmQ0MWMwZDA1Yzk0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 11:54:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6ImxRSExWQmJkb1l4L00wSkY5SmpLVEE9PSIsInZhbHVlIjoiQXIrS0kxSnhhRHlrbDR2NStyTXhIMVB5eWxFZGRTdHpYVjY4QzVHNWVaOVBEQmtRdEJnMk04SGZsU2VpdE40c1k3R2V2QXg3UHNzMVBSN05ZaTFYWnNGWTVPR01aYi9uNHhJaUFCTWt2a2lKZ2ZuSnB2SDl5YUhwdHN4cStyaHoiLCJtYWMiOiJhMDk5ZDE4ODUyNWI2ZjFjOWIyZDBmMjg2OWI2NTU3MTdmZjRmZTg3OTYyOGQwMzEwYTBiYmZjMTlmNDQ5NDJhIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 11:54:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjFucnNjeVQ0VkQxb1VvMUdkd1d4TWc9PSIsInZhbHVlIjoicDZWd05RaThHYjZWUUNVTElEcndVRnRkNjc3dHpMMEMrOWVpNjhiU0xmdnN0a1llVVIrV2dKenowMzJ6cy82QW5oYmhzZTdQZ1JhMC84OEd4RXo5N2tJZHV2M1V3OCtXYVhYYXdLWk0wb3dDVzVuZUpKb3Z6ckdSbENwSS9tNG0iLCJtYWMiOiI5N2IxMWMzNzBkNGM4ZTEyNWEyZGM2ZGI2YjczNjlhYWRhYmExNzMyZWE0YTdmNDgwNjUzYmQ0MWMwZDA1Yzk0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 11:54:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6ImxRSExWQmJkb1l4L00wSkY5SmpLVEE9PSIsInZhbHVlIjoiQXIrS0kxSnhhRHlrbDR2NStyTXhIMVB5eWxFZGRTdHpYVjY4QzVHNWVaOVBEQmtRdEJnMk04SGZsU2VpdE40c1k3R2V2QXg3UHNzMVBSN05ZaTFYWnNGWTVPR01aYi9uNHhJaUFCTWt2a2lKZ2ZuSnB2SDl5YUhwdHN4cStyaHoiLCJtYWMiOiJhMDk5ZDE4ODUyNWI2ZjFjOWIyZDBmMjg2OWI2NTU3MTdmZjRmZTg3OTYyOGQwMzEwYTBiYmZjMTlmNDQ5NDJhIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 11:54:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443631810\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-999749681 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ArW6lJ6JQUbYgAMsgdUMOjDp5tJ3LmxauntXPbaD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/gestions/etudiants/103/5/1/4/certificat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753089943</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999749681\", {\"maxDepth\":0})</script>\n"}}