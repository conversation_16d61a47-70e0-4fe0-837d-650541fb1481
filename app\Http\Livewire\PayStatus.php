<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoriquePayment;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\TypePayment;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class PayStatus extends Component
{
    use WithPagination; // Add pagination support
    
    protected $paginationTheme = 'bootstrap';
    
    public $currentPage = PAGELIST;
    public $listEtus = [];
    public $etus = [];
    public $searchTerm = '';
    public $isLoading = false;
    public $sortField = 'nom';
    public $sortDirection = 'asc';

    // For storing current selections
    public $current_parcours = null;
    public $current_niveau = null;
    public $current_annee = null;

    // For summary statistics
    public $totalStudents = 0;
    public $paymentStats = [];

    // Lifecycle hooks
    public function mount()
    {
        $this->listEtus = [
            'parcour_id' => '',
            'niveau_id' => 0,
            'annee_universitaire_id' => 0
        ];
    }

    public function render()
    {
        return view('livewire.administration.payStatus.index', [
            "parcours" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "annees" => AnneeUniversitaire::orderBy('nom', 'desc')->get()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {
            return [
                'editSemestre.nom' => 'required',
            ];
        }

        return [
            'listEtus.niveau_id' => 'required|not_in:0',
            'listEtus.parcour_id' => 'required',
            'listEtus.annee_universitaire_id' => 'required|not_in:0',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function goToListEtus()
    {
        $this->currentPage = PAGELIST;
        $this->etus = [];
        $this->reset(['searchTerm', 'paymentStats', 'totalStudents']);
    }

    public function showStatusEtu()
    {
        $this->validate();
        $this->isLoading = true;
        
        // Reset data
        $this->etus = [];
        $this->paymentStats = [];
        
        $this->generate(
            $this->listEtus["parcour_id"], 
            $this->listEtus["niveau_id"], 
            $this->listEtus["annee_universitaire_id"]
        );
        
        $this->isLoading = false;
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        
        // Re-sort the collection
        if (!empty($this->etus)) {
            $this->etus = collect($this->etus)->sortBy([
                [$this->sortField, $this->sortDirection]
            ])->values()->all();
        }
    }

    public function generate($parcour_id, $niveau_id, $annee_universitaire_id)
    {
        // Fix the logical error in the condition
        if ($parcour_id != 100) {
            $this->current_parcours = Parcour::find($parcour_id);
        } else {
            $this->current_parcours = null; // Reset when "All courses" is selected
        }
        
        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);

        // Fetch students based on the provided filters
        $query = User::whereHas('info', function ($q) use ($annee_universitaire_id, $niveau_id) {
            $q->whereAnneeUniversitaireId($annee_universitaire_id)
              ->whereNiveauId($niveau_id);
        });

        // FIXED: This condition was inverted in the original code
        if ($parcour_id != 100) {
            $query->whereHas('info', function ($q) use ($parcour_id) {
                $q->whereParcourId($parcour_id);
            });
        }

        $etusList = $query->orderBy('nom')->get();
        $this->totalStudents = $etusList->count();

        // Initialize payment type statistics
        $paymentTypes = TypePayment::whereHas('niveau', function ($q) use ($niveau_id) {
            $q->whereNiveauId($niveau_id);
        })->get();
        
        foreach ($paymentTypes as $type) {
            $this->paymentStats[$type->id] = [
                'nom' => $type->nom,
                'paid' => 0,
                'partial' => 0,
                'unpaid' => 0,
                'total' => 0
            ];
        }

        // Process each student
        foreach ($etusList as $current_user) {
            $typeIds = TypePayment::whereHas('niveau', function ($q) use ($niveau_id) {
                $q->whereNiveauId($niveau_id);
            })->pluck('id')->toArray();

            $pay = [];

            foreach ($paymentTypes as $type) {
                if (in_array($type->id, $typeIds)) {
                    $history = HistoriquePayment::with('moyen')
                        ->where('user_id', $current_user->id)
                        ->where('annee_universitaire_id', $annee_universitaire_id)
                        ->where('type_payment_id', $type->id)
                        ->get();

                    $total = $history->sum('montant');

                    $pivot = $type->niveau()
                              ->whereNiveauId($niveau_id)
                              ->wherePivot('annee_universitaire_id', $annee_universitaire_id)
                              ->first();
                              
                    if (!$pivot) {
                        continue; // Skip if no pricing found for this type/level/year
                    }
                    
                    $pivotPrice = $pivot->pivot->prix;
                    $status = 0; // Default: unpaid

                    if ($total > 0) {
                        $status = ($total >= $pivotPrice) ? 2 : 1; // 2 = Paid in full, 1 = Partially paid
                        
                        // Calculate percentage paid for partial payments
                        $percentPaid = ($total / $pivotPrice) * 100;
                    } else {
                        $percentPaid = 0;
                    }

                    // Update statistics
                    $this->paymentStats[$type->id]['total']++;
                    if ($status == 2) {
                        $this->paymentStats[$type->id]['paid']++;
                    } elseif ($status == 1) {
                        $this->paymentStats[$type->id]['partial']++;
                    } else {
                        $this->paymentStats[$type->id]['unpaid']++;
                    }

                    $pay[] = [
                        "type_nom" => $type->nom,
                        "status" => $status,
                        "MAX" => $pivotPrice,
                        "paid" => $total,
                        "percent" => $percentPaid
                    ];
                }
            }

            $this->etus[] = [
                "id" => $current_user->id,
                "nom" => $current_user->nom . " " . $current_user->prenom,
                "paiment" => $pay
            ];
        }

        // Sort the results by name by default
        $this->sortBy('nom');
    }
    
    // New method to filter students by search term
    public function search()
    {
        if (!empty($this->etus) && !empty($this->searchTerm)) {
            return array_filter($this->etus, function($student) {
                return stripos($student['nom'], $this->searchTerm) !== false;
            });
        }
        
        return $this->etus;
    }
    
    // Method to export data to Excel (requires additional package like maatwebsite/excel)
    public function exportToExcel()
    {
        // Implementation would require the Excel package
        // This is just a placeholder
        $this->dispatchBrowserEvent('show-export-notification', [
            'message' => 'Téléchargement du fichier Excel en cours...'
        ]);
        
        // In a real implementation, you would return a download response here
    }
}