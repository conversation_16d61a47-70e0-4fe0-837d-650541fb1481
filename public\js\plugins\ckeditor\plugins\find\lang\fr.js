CKEDITOR.plugins.setLang("find","fr",{find:"Rechercher",findOptions:"Options de recherche",findWhat:"Rechercher :",matchCase:"Respecter la casse",matchCyclic:"<PERSON><PERSON><PERSON>",matchWord:"Mot entier uniquement",notFoundMsg:"Le texte spécifié ne peut être trouvé.",replace:"Remplacer",replaceAll:"Remplacer tout",replaceSuccessMsg:"%1 occurrence(s) remplacée(s).",replaceWith:"Remplacer par : ",title:"Rechercher et remplacer"});