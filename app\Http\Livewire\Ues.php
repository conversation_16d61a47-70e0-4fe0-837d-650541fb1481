<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Semestre;
use App\Models\Ue;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class Ues extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    
    // Recherche et filtres
    public $query = '';
    public $selectedParcours = [];
    public $selectedNiveaux = [];
    public $filtreAnnee = '';
    
    // Gestion UE
    public $newUe = [];
    public $editUe = [];
    public $editingUeId = null;
    public $expandedUes = [];
    public $showQuickAddModal = false;
    
    // Gestion EC
    public $showEcModal = false;
    public $ecModalMode = 'add';
    public $ecForm = [];
    public $currentEcUe = null;
    public $currentEcId = null;
    
    // Gestion recherche enseignant dans modal EC
    public $enseignantQuery = '';
    public $filteredEnseignants = [];
    public $showAddEnseignantForm = false;
    public $newEnseignant = [
        'nom' => '',
        'prenom' => '',
        'email' => '',
        'telephone1' => '',
        'sexe' => 'M'
    ];
    public $recentlyAssignedEnseignants = [];

    // Reset pagination sur changement des filtres
    public function updatingQuery() { $this->resetPage(); }
    public function updatingSelectedParcours() { $this->resetPage(); }
    public function updatingSelectedNiveaux() { $this->resetPage(); }
    public function updatingFiltreAnnee() { $this->resetPage(); }

    public function render()
    {
        // Construire la requête avec filtres
        $query = Ue::query()->with(['parcours', 'niveau', 'semestre', 'annee', 'matiere.user']);
        
        // Recherche globale
        if(!empty($this->query)) {
            $query->where(function($q) {
                $q->where('nom', 'like', '%'. $this->query .'%')
                  ->orWhere('code', 'like', '%'. $this->query .'%')
                  ->orWhereHas('parcours', function($q) {
                      $q->where('sigle', 'like', '%'. $this->query .'%');
                  });
            });
        }
        
        // Appliquer les filtres sélectionnés
        if(count($this->selectedParcours) > 0) {
            $query->whereIn('parcour_id', $this->selectedParcours);
        }
        
        if(count($this->selectedNiveaux) > 0) {
            $query->whereIn('niveau_id', $this->selectedNiveaux);
        }
        
        if(!empty($this->filtreAnnee)) {
            $query->where('annee_universitaire_id', $this->filtreAnnee);
        }
        
        // Nombre de filtres actifs pour l'UI
        $activeFiltersCount = count($this->selectedParcours) + count($this->selectedNiveaux) + ($this->filtreAnnee ? 1 : 0);
        
        return view('livewire.deraq.ue.index', [
            "ues" => $query->paginate(10),
            "parcours" => Parcour::all(),
            "semestres" => Semestre::all(),
            "annees" => AnneeUniversitaire::all(),
            "niveaux" => Niveau::all(),
            "enseignants" => User::whereHas('roles', fn($q) => $q->where('role_id', '=', 2))->get(),
            "activeFiltersCount" => $activeFiltersCount
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    // Validation
    protected function rules()
    {
        if($this->ecModalMode === 'edit') {
            return [
                'ecForm.nom' => 'required',
                'ecForm.code' => 'required',
                'ecForm.user_id' => 'nullable',
            ];
        } elseif($this->ecModalMode === 'add') {
            return [
                'ecForm.nom' => 'required',
                'ecForm.code' => 'required|unique:matieres,code',
                'ecForm.user_id' => 'nullable',
            ];
        } elseif($this->editingUeId) {
            return [
                'editUe.nom' => 'required',
                'editUe.code' => 'required',
                'editUe.credit' => 'required|numeric',
                'editUe.parcour_id' => 'required',
                'editUe.semestre_id' => 'required',
                'editUe.niveau_id' => 'required',
                'editUe.annee_universitaire_id' => 'required',
            ];
        } else {
            // Rules for adding a new UE
            return [
                'newUe.nom' => 'required',
                'newUe.code' => 'required', // Retiré unique temporairement
                'newUe.credit' => 'required|numeric',
                'newUe.parcour_id' => 'required',
                'newUe.niveau_id' => 'required',
                'newUe.semestre_id' => 'required',
                'newUe.annee_universitaire_id' => 'required',
            ];
        }
    }

    // Gestion des UE
    public function addUe()
    {
        $validateArr = [
            'newUe.nom' => 'required|string|max:255',
            'newUe.code' => 'required|string|max:50|unique:ues,code', // Ensure code is unique in the 'ues' table
            'newUe.credit' => 'required|numeric|min:0',
            'newUe.parcour_id' => 'required|exists:parcours,id', // Check if parcour_id exists in 'parcours' table
            'newUe.niveau_id' => 'required|exists:niveaux,id', // Check if niveau_id exists in 'niveaux' table
            'newUe.semestre_id' => 'required|exists:semestres,id', // Check if semestre_id exists in 'semestres' table
            'newUe.annee_universitaire_id' => 'required|exists:annee_universitaires,id', // Check if annee_universitaire_id exists in 'annee_universitaires' table
        ];
        
        $this->validate($validateArr);

        
        Ue::create($this->newUe);
        
        $this->newUe = [];
        $this->showQuickAddModal = false;
        
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "UE créée avec succès!"
        ]);
    }
    
    public function startEditing($ueId)
    {
        $ue = Ue::findOrFail($ueId);
        $this->editUe = $ue->toArray();
        $this->editingUeId = $ueId;
    }
    
    public function cancelEdit()
    {
        $this->editUe = [];
        $this->editingUeId = null;
    }
    
    public function updateUe()
    {
        $validateArr = [
            'editUe.nom' => 'required|string|max:255',
            // Ensure code is unique, ignoring the current UE being edited
            'editUe.code' => 'required|string|max:50|unique:ues,code,' . $this->editingUeId, 
            'editUe.credit' => 'required|numeric|min:0',
            'editUe.parcour_id' => 'required|exists:parcours,id', // Check if parcour_id exists in 'parcours' table
            'editUe.niveau_id' => 'required|exists:niveaux,id', // Check if niveau_id exists in 'niveaux' table
            'editUe.semestre_id' => 'required|exists:semestres,id', // Check if semestre_id exists in 'semestres' table
            'editUe.annee_universitaire_id' => 'required|exists:annee_universitaires,id', // Check if annee_universitaire_id exists in 'annee_universitaires' table
        ];
        
        $this->validate($validateArr);
        
        Ue::find($this->editingUeId)->update($this->editUe);
        
        $this->editUe = [];
        $this->editingUeId = null;
        
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "UE mise à jour avec succès!"
        ]);
    }
    
    public function deleteUe($id)
    {
        // Vérifier si l'UE a des ECs associés
        $ue = Ue::with('matiere')->find($id);
        
        if($ue->matiere->count() > 0) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Impossible de supprimer cette UE car elle contient des ECs. Supprimez d'abord les ECs."
            ]);
            return;
        }
        
        Ue::destroy($id);
        
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "UE supprimée avec succès!"
        ]);
    }
    
    // Gestion de l'affichage des ECs
    public function toggleEcList($ueId)
    {
        if(in_array($ueId, $this->expandedUes)) {
            $this->expandedUes = array_diff($this->expandedUes, [$ueId]);
        } else {
            $this->expandedUes[] = $ueId;
        }
    }
    
    // Gestion des recherches enseignants dans le EC modal
    public function updatedEnseignantQuery()
    {
        if (strlen($this->enseignantQuery) >= 2) {
            $this->filteredEnseignants = User::whereHas('roles', fn($q) => $q->where('role_id', '=', 2))
                ->where(function($query) {
                    $query->where('nom', 'like', '%'. $this->enseignantQuery .'%')
                        ->orWhere('prenom', 'like', '%'. $this->enseignantQuery .'%')
                        ->orWhere('email', 'like', '%'. $this->enseignantQuery .'%');
                })
                ->orderByRaw("CASE WHEN id IN (" . implode(',', array_filter($this->recentlyAssignedEnseignants)) . ") THEN 0 ELSE 1 END")
                ->limit(5)
                ->get();
        } else {
            $this->filteredEnseignants = collect([]);
        }
    }

    // Méthode pour sélectionner un enseignant filtré
    public function selectEnseignant($id)
    {
        $this->ecForm['user_id'] = $id;
        
        // Ajouter l'ID aux enseignants récemment assignés
        if (!in_array($id, $this->recentlyAssignedEnseignants)) {
            $this->recentlyAssignedEnseignants[] = $id;
            // Garder uniquement les 5 derniers
            if (count($this->recentlyAssignedEnseignants) > 5) {
                array_shift($this->recentlyAssignedEnseignants);
            }
        }
        
        $this->enseignantQuery = '';
        $this->filteredEnseignants = collect([]);
    }

    // Méthode pour afficher le formulaire d'ajout d'enseignant
    public function toggleAddEnseignantForm()
    {
        $this->showAddEnseignantForm = !$this->showAddEnseignantForm;
        if (!$this->showAddEnseignantForm) {
            $this->resetNewEnseignant();
        }
    }

    // Méthode pour réinitialiser le formulaire d'ajout d'enseignant
    public function resetNewEnseignant()
    {
        $this->newEnseignant = [
            'nom' => '',
            'prenom' => '',
            'email' => '',
            'telephone1' => '',
            'sexe' => 'M'
        ];
    }

    // Méthode pour créer un nouvel enseignant depuis le modal d'EC
    public function createEnseignant()
    {
        $this->validate([
            'newEnseignant.nom' => 'required',
            'newEnseignant.prenom' => 'required',
            'newEnseignant.telephone1' => 'nullable|numeric|unique:users,telephone1',
            'newEnseignant.sexe' => 'required'
        ]);
        
        // Générer un mot de passe aléatoire
        $password = Str::random(10);
        
        // Créer l'utilisateur
        $user = User::create([
            'nom' => $this->newEnseignant['nom'],
            'prenom' => $this->newEnseignant['prenom'],
            'telephone1' => $this->newEnseignant['telephone1'],
            'sexe' => $this->newEnseignant['sexe'],
            'password' => Hash::make($password),
            'photo' => 'media/avatars/avatar0.jpg'
        ]);
        
        // Attribuer le rôle d'enseignant
        $user->roles()->attach(2);
        
        // Optionnel : Envoyer un email avec les identifiants
        // Mail::to($user->email)->send(new SignUp($user->nom, $user->prenom, $password, $user->email));
        
        // Sélectionner le nouvel enseignant
        $this->ecForm['user_id'] = $user->id;
        $this->recentlyAssignedEnseignants[] = $user->id;
        
        // Fermer le formulaire
        $this->showAddEnseignantForm = false;
        $this->resetNewEnseignant();
        
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "Enseignant créé avec succès!"
        ]);
    }
    
    // Gestion des EC (Éléments Constitutifs)
    public function addQuickEC($ueId)
    {
        $this->currentEcUe = Ue::findOrFail($ueId);
        $this->ecForm = [
            'code' => '',
            'nom' => '',
            'user_id' => '',
            'ue_id' => $ueId
        ];
        $this->ecModalMode = 'add';
        $this->showEcModal = true;
        $this->loadRecentEnseignants();
        
        // S'assurer que l'UE est étendue pour voir l'EC ajouté
        if(!in_array($ueId, $this->expandedUes)) {
            $this->expandedUes[] = $ueId;
        }
    }
    
    // Charger les enseignants récemment assignés
    private function loadRecentEnseignants()
    {
        if (count($this->recentlyAssignedEnseignants) === 0) {
            $recentlyAssigned = Matiere::whereNotNull('user_id')
                ->select('user_id')
                ->distinct()
                ->orderBy('updated_at', 'desc')
                ->limit(5)
                ->pluck('user_id')
                ->toArray();
            
            $this->recentlyAssignedEnseignants = array_filter($recentlyAssigned);
        }
    }
    
    public function editEC($matiereId)
    {
        $matiere = Matiere::findOrFail($matiereId);
        $this->currentEcUe = $matiere->ue;
        $this->currentEcId = $matiereId;
        $this->ecForm = $matiere->toArray();
        $this->ecModalMode = 'edit';
        $this->showEcModal = true;
        $this->loadRecentEnseignants();
    }
    
    public function saveEC()
    {
        $this->validate();
        
        if($this->ecModalMode === 'add') {
            Matiere::create($this->ecForm);
            $message = "EC créé avec succès!";
        } else {
            Matiere::find($this->currentEcId)->update($this->ecForm);
            $message = "EC mis à jour avec succès!";
        }
        
        $this->closeEcModal();
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => $message]);
    }
    
    public function closeEcModal()
    {
        $this->showEcModal = false;
        $this->ecForm = [];
        $this->currentEcUe = null;
        $this->currentEcId = null;
        $this->enseignantQuery = '';
        $this->filteredEnseignants = collect([]);
        $this->showAddEnseignantForm = false;
    }
    
    public function deleteEC($id)
    {
        Matiere::destroy($id);
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "EC supprimé avec succès!"
        ]);
    }
}