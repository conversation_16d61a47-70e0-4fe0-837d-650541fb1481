/* Base16 Atelier Sulphurpool Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/sulphurpool) */
/* Original Base16 color scheme by <PERSON> (https://github.com/ch<PERSON><PERSON><PERSON>/base16) */

/* Atelier-Sulphurpool Comment */
.hljs-comment {
  color: #898ea4;
}

/* Atelier-Sulphurpool Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.hljs-name,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #c94922;
}

/* Atelier-Sulphurpool Orange */
.hljs-number,
.hljs-preprocessor,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #c76b29;
}

/* Atelier-Sulphurpool Yellow */
.ruby .hljs-class .hljs-title,
.css .hljs-rule .hljs-attribute {
  color: #c08b30;
}

/* Atelier-Sulphurpool Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #ac9739;
}

/* Atelier-Sulphurpool Aqua */
.hljs-title,
.css .hljs-hexcolor {
  color: #22a2c9;
}

/* Atelier-Sulphurpool Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #3d8fd1;
}

/* Atelier-Sulphurpool Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #6679cc;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #202746;
  color: #979db4;
  padding: 0.5em;
  -webkit-text-size-adjust: none;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
