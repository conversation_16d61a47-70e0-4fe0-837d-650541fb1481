<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-2">
            <div class="flex-grow-1">
                <h1 class="h3 fw-bold mb-2">
                    <button type="button" class="btn btn-primary btn-lg" wire:click.prevent="goToListUser()">
                        <i class="si si-arrow-left fa-2x"></i>
                    </button>
                </h1>

            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="" wire:click.prevent="goToListUser()">Liste d'utilisateur</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Creation d'enseignant
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="content">
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="addUser()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire d'ajout d'enseignant</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.nom"
                            class="form-control @error('newUser.nom') is-invalid @enderror" id="example-text-input"
                            name="firstname" placeholder="Text Input">

                        @error('newUser.nom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Prénom <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.prenom"
                            class="form-control @error('newUser.prenom') is-invalid @enderror" id="example-text-input"
                            name="lastname" placeholder="Text Input">

                        @error('newUser.prenom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    

                    <div class="col-6">
                        <label class="form-label" for="example-select">Sexe <span class="text-danger">*</span></label>
                        <select class="form-select @error('newUser.sexe') is-invalid @enderror"
                            wire:model="newUser.sexe" id="example-select" name="example-select">
                            <option selected value="">Open this select menu</option>
                            <option value="H">Homme</option>
                            <option value="F">Femme</option>
                        </select>

                        @error('newUser.sexe')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    
                    

                    <div class="col-6">
                        <label class="form-label" for="example-email-input">Email <span
                                class="text-danger">*</span></label>
                        <input type="email" wire:model="newUser.email"
                            class="form-control @error('newUser.email') is-invalid @enderror" id="example-email-input"
                            name="example-email-input" placeholder="Email Input">

                        @error('newUser.email')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone1</label>
                        <input type="text" wire:model="newUser.telephone1"
                            class="form-control @error('newUser.telephone1') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('newUser.telephone1')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
