<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SemestreTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table("semestres")->insert([
            ["nom"=>"Semestre 1", "niveau_id"=>"1"],
            ["nom"=>"Semestre 2", "niveau_id"=>"1"],
            ["nom"=>"Semestre 3", "niveau_id"=>"2"],
            ["nom"=>"Semestre 4", "niveau_id"=>"2"],
            ["nom"=>"Semestre 5", "niveau_id"=>"3"],
            ["nom"=>"Semestre 6", "niveau_id"=>"3"],
            ["nom"=>"Semestre 7", "niveau_id"=>"4"],
            ["nom"=>"Semestre 8", "niveau_id"=>"4"],
            ["nom"=>"Semestre 9", "niveau_id"=>"5"],
            ["nom"=>"Semestre 10", "niveau_id"=>"5"]
        ]);
    }
}
