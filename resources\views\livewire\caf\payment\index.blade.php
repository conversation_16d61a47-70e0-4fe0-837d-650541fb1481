@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
@endsection

<div wire:ignore.self>
    @if ($currentPage == PAGECREATEFORM)
        @include('livewire.caf.payment.create')
    @endif

    @if ($currentPage == PAGEEDITFORM)
        @include('livewire.caf.payment.edit')
    @endif

    @if ($currentPage == PAGELIST)
        @include('livewire.caf.payment.liste')
    @endif
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Success message using One.js notification
        window.addEventListener("showSuccessMessage", event => {
            One.helpersOnLoad(['jq-notify']);
            One.helpers('jq-notify', {
                type: 'success',
                icon: 'fa fa-check me-1',
                message: event.detail.message || 'Opération effectuée avec succès!'
            });
        });
        
        // Error message handler
        window.addEventListener("showErrorMessage", event => {
            One.helpersOnLoad(['jq-notify']);
            One.helpers('jq-notify', {
                type: 'danger',
                icon: 'fa fa-times me-1',
                message: event.detail.message || 'Une erreur est survenue!'
            });
        });
        
        // Add loading overlay for Livewire operations
        Livewire.hook('message.sent', () => {
            One.block('state_loading', '.block');
        });
        
        Livewire.hook('message.processed', () => {
            One.block('state_normal', '.block');
        });
        
        // Add confirmation before delete actions
        window.addEventListener("confirmDelete", event => {
            One.helpers('jq-notify', {
                type: 'warning',
                icon: 'fa fa-exclamation-triangle me-1',
                message: 'Êtes-vous sûr de vouloir supprimer cet élément?',
                onShow: function() {
                    // Create confirmation buttons
                    this.find('.jq-notify-content')
                        .append($('<div class="mt-2 text-end">')
                            .append($('<button type="button" class="btn btn-sm btn-danger">').text('Supprimer')
                                .on('click', function() {
                                    Livewire.emit('deleteConfirmed', event.detail.id);
                                    $(this).closest('.jq-notify').fadeOut();
                                }))
                            .append($('<button type="button" class="btn btn-sm btn-light ms-1">').text('Annuler')
                                .on('click', function() {
                                    $(this).closest('.jq-notify').fadeOut();
                                }))
                        );
                }
            });
        });
        
        // Add form reset confirmation
        window.addEventListener("confirmReset", event => {
            One.helpers('jq-notify', {
                type: 'info',
                icon: 'fa fa-question-circle me-1',
                message: 'Réinitialiser le formulaire? Toutes les données saisies seront perdues.',
                onShow: function() {
                    // Create confirmation buttons
                    this.find('.jq-notify-content')
                        .append($('<div class="mt-2 text-end">')
                            .append($('<button type="button" class="btn btn-sm btn-alt-primary">').text('Réinitialiser')
                                .on('click', function() {
                                    Livewire.emit('resetConfirmed');
                                    $(this).closest('.jq-notify').fadeOut();
                                }))
                            .append($('<button type="button" class="btn btn-sm btn-light ms-1">').text('Annuler')
                                .on('click', function() {
                                    $(this).closest('.jq-notify').fadeOut();
                                }))
                        );
                }
            });
        });
        
        // Add print receipt function
        window.printReceipt = function(userId, paymentId) {
            window.open(`/caf/receipt/${userId}/${paymentId}`, '_blank');
        }
        
        // Add copy to clipboard functionality
        $(document).on('click', '.copy-btn', function() {
            const text = $(this).data('copy');
            navigator.clipboard.writeText(text).then(() => {
                const originalText = $(this).html();
                $(this).html('<i class="fa fa-check"></i> Copié!');
                setTimeout(() => {
                    $(this).html(originalText);
                }, 2000);
            });
            
            // Show a mini notification
            One.helpers('jq-notify', {
                type: 'info',
                icon: 'fa fa-clipboard me-1',
                message: 'Texte copié dans le presse-papiers!'
            });
        });
    });
</script>