<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ues', function (Blueprint $table) {
            $table->id();
            $table->string("nom")->nullable();
            $table->string("code");
            $table->integer("credit")->default(0);
            $table->foreignId("parcour_id")->constrained();
            $table->foreignId("annee_universitaire_id")->constrained();
            $table->foreignId("semestre_id")->constrained();
            $table->foreignId("niveau_id")->constrained();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ues', function (Blueprint $table) {
            $table->dropForeign("semestre_id");
            $table->dropForeign("parcour_id");
            $table->dropForeign("niveau_id");
            $table->dropForeign("annee_universitaire_id");
        });
        Schema::dropIfExists('ues');
    }
};
