/* Base16 Atelier Cave Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/cave) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON>on/base16) */

/* Atelier-Cave Comment */
.hljs-comment {
  color: #7e7887;
}

/* Atelier-Cave Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.hljs-name,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #be4678;
}

/* Atelier-Cave Orange */
.hljs-number,
.hljs-preprocessor,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #aa573c;
}

/* Atelier-Cave Yellow */
.ruby .hljs-class .hljs-title,
.css .hljs-rule .hljs-attribute {
  color: #a06e3b;
}

/* Atelier-Cave Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #2a9292;
}

/* Atelier-Cave Aqua */
.hljs-title,
.css .hljs-hexcolor {
  color: #398bc6;
}

/* Atelier-Cave Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #576ddb;
}

/* Atelier-Cave Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #955ae7;
}

.diff .hljs-deletion,
.diff .hljs-addition {
  color: #19171c;
  display: inline-block;
  width: 100%;
}

.diff .hljs-deletion {
  background-color: #be4678;
}

.diff .hljs-addition {
  background-color: #2a9292;
}

.diff .hljs-change {
  color: #576ddb;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #19171c;
  color: #8b8792;
  padding: 0.5em;
  -webkit-text-size-adjust: none;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
