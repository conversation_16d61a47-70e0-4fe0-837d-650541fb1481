(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.lv = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Latvian = {
      firstDayOfWeek: 1,
      weekdays: {
          shorthand: ["Sv", "Pr", "Ot", "Tr", "Ce", "Pk", "Se"],
          longhand: [
              "Svētdiena",
              "Pirmdiena",
              "Otrdiena",
              "Trešdiena",
              "Ceturtdiena",
              "Piektdiena",
              "Sestdiena",
          ],
      },
      months: {
          shorthand: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "Aug",
              "<PERSON>",
              "<PERSON>t",
              "<PERSON>",
              "<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>lijs",
              "Augusts",
              "Septembris",
              "Oktobris",
              "Novembris",
              "Decembris",
          ],
      },
      rangeSeparator: " līdz ",
      time_24hr: true,
  };
  fp.l10ns.lv = Latvian;
  var lv = fp.l10ns;

  exports.Latvian = Latvian;
  exports.default = lv;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
