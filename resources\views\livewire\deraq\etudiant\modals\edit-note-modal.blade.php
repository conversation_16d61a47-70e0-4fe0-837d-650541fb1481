<!-- Edit Note Modal -->
<div class="modal fade" id="modal-edit-note" tabindex="-1" role="dialog" aria-labelledby="modal-edit-note-label" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-edit-note-label">Modifier la Note</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" wire:click="closeEditNoteModal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if($showEditNoteModal && !empty($editNote))
                    <form wire:submit.prevent="updateNote">
                        {{-- Display student name for context --}}
                        <div class="mb-3">
                            <label class="form-label">Étudiant</label>
                            <input type="text" class="form-control" value="{{ $editNote['student_name'] ?? 'N/A' }}" readonly disabled>
                        </div>

                        <div class="mb-3">
                            <label for="editNote.matiere_id" class="form-label">Matière <span class="text-danger">*</span></label>
                            <select class="form-select @error('editNote.matiere_id') is-invalid @enderror" id="editNote.matiere_id" wire:model.defer="editNote.matiere_id" required>
                                <option value="">Sélectionner...</option>
                                @if(!empty($editNote['available_matieres']))
                                    @foreach($editNote['available_matieres'] as $matiere)
                                        <option value="{{ $matiere['id'] }}">{{ $matiere['nom'] }}</option>
                                    @endforeach
                                @else
                                     {{-- Fallback or message if matieres aren't loaded --}}
                                     <option value="" disabled>Chargement des matières...</option>
                                @endif
                            </select>
                            @error('editNote.matiere_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label for="editNote.type_note_id" class="form-label">Type d'évaluation <span class="text-danger">*</span></label>
                            <select class="form-select @error('editNote.type_note_id') is-invalid @enderror" id="editNote.type_note_id" wire:model.defer="editNote.type_note_id" required>
                                <option value="">Sélectionner...</option>
                                @if(!empty($editNote['available_types']))
                                    @foreach($editNote['available_types'] as $type)
                                        <option value="{{ $type['id'] }}">{{ $type['nom'] }}</option>
                                    @endforeach
                                @else
                                     <option value="" disabled>Chargement des types...</option>
                                @endif
                            </select>
                            @error('editNote.type_note_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label for="editNote.valeur" class="form-label">Note (sur 20) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('editNote.valeur') is-invalid @enderror" id="editNote.valeur" wire:model.defer="editNote.valeur" min="0" max="20" step="0.01" required>
                            @error('editNote.valeur') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="modal-footer px-0 pb-0">
                            <button type="button" class="btn btn-alt-secondary" data-bs-dismiss="modal" wire:click="closeEditNoteModal">Annuler</button>
                            <button type="submit" class="btn btn-primary">
                                <span wire:loading wire:target="updateNote" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                @else
                    {{-- Placeholder or loading state if modal is shown but data not ready --}}
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
