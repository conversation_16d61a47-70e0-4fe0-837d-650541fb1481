<!-- Hero avec breadcrumb pour une meilleure navigation -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
            <h1 class="h3 fw-bold mb-1">
                <i class="fa fa-money-bill-wave me-2 text-primary"></i>Gestion des types de paiements
            </h1>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="javascript:void(0)">Administration</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Types de paiements
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<!-- <PERSON><PERSON> Hero -->

<!-- Page Content -->
<div class="content">
    <!-- Dynamic Table Full -->
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                <i class="fa fa-list me-1"></i> Liste des types de paiements
            </h3>
            <div class="block-options">
                <div class="input-group me-2 d-inline-flex" style="width: 220px">
                    <input type="text" wire:model.debounce.300ms="searchTerm" class="form-control form-control-sm" placeholder="Rechercher...">
                    <span class="input-group-text">
                        <i class="fa fa-search"></i>
                    </span>
                </div>
                <button type="button" class="btn btn-sm btn-alt-secondary" data-toggle="tooltip" title="Filtrer">
                    <i class="fa fa-filter"></i>
                </button>
                <button type="button" class="btn btn-sm btn-primary" wire:click.prevent="goToAddPay()">
                    <i class="fa fa-plus me-1"></i> Nouveau
                </button>
            </div>
        </div>
        <div class="block-content block-content-full">
            <div class="table-responsive">
                <table class="table table-bordered table-striped table-vcenter js-dataTable-responsive">
                    <thead>
                        <tr>
                            <th class="text-center" style="width: 80px;">
                                <a href="#" wire:click.prevent="sortBy('id')" class="text-muted">
                                    #
                                    @if ($sortField === 'id')
                                        <i class="fa fa-arrow-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                    @endif
                                </a>
                            </th>
                            <th>
                                <a href="#" wire:click.prevent="sortBy('nom')" class="text-muted">
                                    Nom
                                    @if ($sortField === 'nom')
                                        <i class="fa fa-arrow-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                    @endif
                                </a>
                            </th>
                            <th class="d-none d-sm-table-cell">Tarifs par niveau</th>
                            <th class="text-center" style="width: 100px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($pays as $pay)
                            <tr>
                                <td class="text-center fs-sm">{{ $pay->id }}</td>
                                <td class="fw-semibold fs-sm">{{ $pay->nom }}</td>
                                <td class="d-none d-sm-table-cell fs-sm">
                                    @if(count($pay->niveau) > 0)
                                        <div class="d-flex flex-wrap">
                                            @foreach ($pay->niveau->groupBy('pivot.annee_universitaire_id') as $anneeId => $niveaux)
                                                <div class="badge bg-primary-light text-primary m-1 p-2">
                                                    {{ \App\Models\AnneeUniversitaire::find($anneeId)->libelle ?? 'Année '.$anneeId }}
                                                    <div class="mt-1">
                                                        @foreach($niveaux as $niv)
                                                            <span class="badge bg-light text-dark m-1">{{ $niv->sigle }}: {{ number_format($niv->pivot->prix, 0, ',', ' ') }} F</span>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <span class="badge bg-warning">Aucun tarif défini</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <div class="btn-group">
                                        <button type="button" wire:click="goToEditPay({{ $pay->id }})" class="btn btn-sm btn-alt-primary" data-bs-toggle="tooltip" title="Modifier">
                                            <i class="fa fa-pencil-alt"></i>
                                        </button>
                                        <button type="button" wire:click="confirmDelete({{ $pay->id }})" class="btn btn-sm btn-alt-danger" data-bs-toggle="tooltip" title="Supprimer">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Affichage de {{ $pays->firstItem() ?? 0 }} à {{ $pays->lastItem() ?? 0 }} sur {{ $pays->total() ?? 0 }} résultats
                </div>
                <div>
                    {{ $pays->links() }}
                </div>
            </div>
        </div>
    </div>
</div>