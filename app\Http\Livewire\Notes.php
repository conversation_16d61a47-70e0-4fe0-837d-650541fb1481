<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoryNote;
use App\Models\Matiere;
use App\Models\Note;
use App\Models\TypeNote;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class Notes extends Component
{
    use WithPagination;
    
    public $noteEtus = [];
    public Matiere $current_matiere;
    public $noteType;
    public $isAdded = FALSE;
    public $isEditable = TRUE;
    public $historyNote = FALSE;
    public $searchTerm = '';
    public $bulkNote = null;
    public $selectedStudents = [];
    public $perPage = 10;

    protected $listeners = ['refreshNotes' => '$refresh'];

    public function mount($coursId)
    {
        $this->current_matiere = Matiere::find($coursId);
        $this->noteType = 1;
        $this->loadStudents();
    }

    public function render()
    {
        return view('livewire.enseignant.note.index', [
            "typeNotes" => TypeNote::all()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        return [
            "noteEtus.*.note" => "nullable|numeric|min:0|max:20",
        ];
    }

    public function loadStudents()
{
    $notes = Note::with(['user', 'historyNote'])
        ->whereMatiereId($this->current_matiere->id)
        ->whereTypeNoteId($this->noteType)
        ->get();
    
    // Récupérer tous les étudiants actuels dans le niveau/parcours
    $users = User::whereHas('info', fn ($q) => 
        $q->whereAnneeUniversitaireId($this->current_matiere->ue->annee_universitaire_id)
          ->whereNiveauId($this->current_matiere->ue->niveau_id)
          ->whereParcourId($this->current_matiere->ue->parcour_id)
    )
    ->when($this->searchTerm, function($query) {
        return $query->where(function($q) {
            $q->where('nom', 'like', '%' . $this->searchTerm . '%')
              ->orWhere('prenom', 'like', '%' . $this->searchTerm . '%');
        });
    })
    ->get(['id', 'nom', 'prenom']);
        
    if ($notes->isEmpty()) {
        $this->isAdded = FALSE;
        $this->noteEtus = [];
        
        foreach ($users as $user) {
            array_push($this->noteEtus, [
                "user_id" => $user->id,  // Utilisez user_id au lieu de id
                "nom" => $user->nom, 
                "prenom" => $user->prenom, 
                "note" => "", 
                "observation" => ""
            ]);
        }
    } else {
        // Utiliser les notes existantes
        $this->noteEtus = $notes->toArray();
        $this->isAdded = TRUE;
        $this->isEditable = $this->noteEtus[0]['history_note']['estModifiable'];
        
        // Vérifier s'il y a de nouveaux étudiants non déjà notés
        $existingUserIds = collect($this->noteEtus)->pluck('user_id')->toArray();
        
        foreach ($users as $user) {
            if (!in_array($user->id, $existingUserIds)) {
                array_push($this->noteEtus, [
                    "user_id" => $user->id,  // Utilisez user_id explicitement
                    "nom" => $user->nom, 
                    "prenom" => $user->prenom, 
                    "valeur" => "", 
                    "observation" => "",
                    "user" => [
                        "id" => $user->id,
                        "nom" => $user->nom,
                        "prenom" => $user->prenom
                    ],
                    "history_note" => [
                        "estModifiable" => $this->isEditable
                    ]
                ]);
            }
        }
    }
}

    public function updatedNoteType()
    {
        $this->loadStudents();
    }

    public function updatedSearchTerm()
    {
        $this->loadStudents();
    }

    public function saveNote($index)
    {
        $studentData = $this->noteEtus[$index];
        
        if (!$this->isAdded) {
            // Si les notes n'ont pas été ajoutées, créons l'historyNote pour la première note
            if (!$this->historyNote) {
                $this->historyNote = HistoryNote::create([
                    "matiere_id" => $this->current_matiere->id,
                    "type_note_id" => $this->noteType,
                    "user_id" => auth()->user()->id,
                    "estModifiable" => 1,
                ]);
            }
            
            $note = $studentData["note"] == "" ? 0 : $studentData["note"];
            
            Note::create([
                "type_note_id" => $this->noteType,
                "valeur" => $note,
                "user_id" => $studentData["user_id"],
                "matiere_id" => $this->current_matiere->id,
                "history_note_id" => $this->historyNote->id,
                "observation" => $studentData["observation"] ?? "",
            ]);
            
            // Si c'est la dernière note à ajouter, mettre à jour l'état
            if ($index == count($this->noteEtus) - 1) {
                $this->isAdded = TRUE;
                $this->loadStudents();
            }
        } else {
            // Mise à jour d'une note existante
            Note::where('id', $studentData['id'])
                ->update([
                    'valeur' => $studentData['valeur'],
                    'observation' => $studentData['observation'] ?? "",
                ]);
        }
        
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Note sauvegardée!"]);
    }

    public function addNote()
{
    $validateArr = [
        "noteEtus.*.note" => "nullable|numeric|min:0|max:20",
    ];
    
    $this->validate($validateArr);
    
    // Si des notes existent déjà, mettre à jour au lieu d'en créer de nouvelles
    if ($this->isAdded) {
        foreach ($this->noteEtus as $index => $noteEtu) {
            // Vérifier que c'est bien une note existante (avec un ID de note)
            if (isset($noteEtu['id']) && isset($noteEtu['valeur'])) {
                Note::where('id', $noteEtu['id'])
                    ->update([
                        'valeur' => $noteEtu['valeur'] ?? 0,
                        'observation' => $noteEtu['observation'] ?? "",
                    ]);
            }
            // Si c'est un nouvel étudiant avec pas encore de note, en créer une
            else if (isset($noteEtu['user_id']) || (isset($noteEtu['user']) && isset($noteEtu['user']['id']))) {
                $userId = isset($noteEtu['user_id']) ? $noteEtu['user_id'] : $noteEtu['user']['id'];
                
                // Trouver l'ID de l'historique existant
                $existingNote = Note::where('matiere_id', $this->current_matiere->id)
                                    ->where('type_note_id', $this->noteType)
                                    ->first();
                
                $historyNoteId = $existingNote ? $existingNote->history_note_id : null;
                
                // Créer une nouvelle note pour cet étudiant
                Note::create([
                    "type_note_id" => $this->noteType,
                    "valeur" => $noteEtu['valeur'] ?? 0,
                    "user_id" => $userId,
                    "matiere_id" => $this->current_matiere->id,
                    "history_note_id" => $historyNoteId,
                    "observation" => $noteEtu['observation'] ?? "",
                ]);
            }
        }
        
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Notes mises à jour avec succès!"]);
    } else {
        // Créer un nouvel historique et de nouvelles notes
        $historyNote = HistoryNote::create([
            "matiere_id" => $this->current_matiere->id,
            "type_note_id" => $this->noteType,
            "user_id" => auth()->user()->id,
            "estModifiable" => 1,
        ]);

        foreach ($this->noteEtus as $noteEtu) {
            $note = $noteEtu["note"] == "" ? 0 : $noteEtu["note"];

            // Déterminer l'ID de l'utilisateur selon la structure actuelle
            $userId = null;
            if (isset($noteEtu['user_id'])) {
                $userId = $noteEtu['user_id'];
            } else if (isset($noteEtu['id'])) {
                // Dans la structure actuelle, 'id' est utilisé pour l'ID de l'utilisateur
                $userId = $noteEtu['id'];
            } else if (isset($noteEtu['user']) && isset($noteEtu['user']['id'])) {
                $userId = $noteEtu['user']['id'];
            }
            
            if ($userId) {
                Note::create([
                    "type_note_id" => $this->noteType,
                    "valeur" => $note,
                    "user_id" => $userId,
                    "matiere_id" => $this->current_matiere->id,
                    "history_note_id" => $historyNote->id,
                    "observation" => $noteEtu["observation"] ?? "",
                ]);
            }
        }
        
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Notes ajoutées avec succès!"]);
    }
    
    $this->loadStudents();
}

    public function updateNote($index)
    {
        $studentData = $this->noteEtus[$index];
        
        // Cas 1: Note existante avec un ID - Mettre à jour
        if (isset($studentData['id']) && is_numeric($studentData['id'])) {
            // dd($studentData);
            Note::where('id', $studentData['id'])
                ->update([
                    'valeur' => $studentData['valeur'],
                    'observation' => $studentData['observation'] ?? "",
                ]);
        } 
        // Cas 2: Nouvel étudiant qui n'a pas encore de note - Créer une note
        else if (isset($studentData['user']) && isset($studentData['user']['id'])) {
            // Trouver l'historique de note existant pour cette matière/type
            $existingNote = Note::where('matiere_id', $this->current_matiere->id)
                                ->where('type_note_id', $this->noteType)
                                ->first();
            
            $historyNoteId = $existingNote ? $existingNote->history_note_id : null;
            
            // Si pas d'historique trouvé, en créer un nouveau (cas rare)
            if (!$historyNoteId) {
                $newHistory = HistoryNote::create([
                    "matiere_id" => $this->current_matiere->id,
                    "type_note_id" => $this->noteType,
                    "user_id" => auth()->user()->id,
                    "estModifiable" => 1,
                ]);
                $historyNoteId = $newHistory->id;
            }
            
            // Créer la note pour ce nouvel étudiant
            $newNote = Note::create([
                "type_note_id" => $this->noteType,
                "valeur" => $studentData['valeur'] ?? 0,
                "user_id" => $studentData['user']['id'],
                "matiere_id" => $this->current_matiere->id,
                "history_note_id" => $historyNoteId,
                "observation" => $studentData['observation'] ?? "",
            ]);
            
            // Mise à jour des données locales avec l'ID de la nouvelle note
            $this->noteEtus[$index]['id'] = $newNote->id;
        }
        
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Note mise à jour!"]);
    }
    
    public function toggleSelectStudent($index)
    {
        if (in_array($index, $this->selectedStudents)) {
            $this->selectedStudents = array_diff($this->selectedStudents, [$index]);
        } else {
            $this->selectedStudents[] = $index;
        }
    }
    
    public function selectAllStudents()
    {
        if (count($this->selectedStudents) === count($this->noteEtus)) {
            $this->selectedStudents = [];
        } else {
            $this->selectedStudents = array_keys($this->noteEtus);
        }
    }
    
    public function applyBulkNote()
    {
        if ($this->bulkNote !== null && !empty($this->selectedStudents)) {
            // Si des notes existent déjà pour certains étudiants
            if ($this->isAdded) {
                // Récupérer l'ID de l'historique de notes pour cette matière et ce type d'évaluation
                $existingNote = Note::where('matiere_id', $this->current_matiere->id)
                                    ->where('type_note_id', $this->noteType)
                                    ->first();
                
                $historyNoteId = $existingNote ? $existingNote->history_note_id : null;

                
                // Si pas d'historique de notes trouvé, en créer un nouveau
                if (!$historyNoteId) {
                    $newHistory = HistoryNote::create([
                        "matiere_id" => $this->current_matiere->id,
                        "type_note_id" => $this->noteType,
                        "user_id" => auth()->user()->id,
                        "estModifiable" => 1,
                    ]);
                    $historyNoteId = $newHistory->id;
                }
                
                

                foreach ($this->selectedStudents as $index) {
                    $studentData = $this->noteEtus[$index];
                    
                    // Cas 1: Note existante avec un ID - Mettre à jour
                    if ($studentData['valeur'] !== "") {

                        
                        $this->noteEtus[$index]['valeur'] = $this->bulkNote;
                        
                        Note::where('id', $studentData['id'])
                            ->update([
                                'valeur' => $this->bulkNote,
                                'observation' => $studentData['observation'] ?? "",
                            ]);
                    } 
                    // Cas 2: Nouvel étudiant mais avec des notes existantes pour d'autres - Créer une note
                    else {
                        
                        // Déterminer l'ID de l'utilisateur selon la structure
                        $userId = null;
                        if (isset($studentData['user']) && isset($studentData['user']['id'])) {
                            $userId = $studentData['user']['id'];
                        } else if (isset($studentData['id'])) {
                            $userId = $studentData['id']; // Dans le cas où id est l'ID de l'utilisateur
                        }
                        
                        if ($userId) {
                            // Créer une nouvelle note pour cet étudiant
                            $newNote = Note::create([
                                "type_note_id" => $this->noteType,
                                "valeur" => $this->bulkNote,
                                "user_id" => $userId,
                                "matiere_id" => $this->current_matiere->id,
                                "history_note_id" => $historyNoteId,
                                "observation" => $studentData['observation'] ?? "",
                            ]);
                            
                            // Mettre à jour la valeur localement pour l'affichage
                            if (isset($this->noteEtus[$index]['valeur'])) {
                                $this->noteEtus[$index]['valeur'] = $this->bulkNote;
                            }
                        }
                    }
                }
            } else {
                // Cas simple: Aucune note n'existe encore
                foreach ($this->selectedStudents as $index) {
                    $this->noteEtus[$index]['note'] = $this->bulkNote;
                }
                // Sauvegarder toutes les notes via la méthode existante
                $this->addNote();
            }
            
            $this->bulkNote = null;
            $this->selectedStudents = [];
            
            // Recharger les données pour afficher les mises à jour correctement
            $this->loadStudents();
            
            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "Notes appliquées en masse avec succès!"
            ]);
        }
    }
}