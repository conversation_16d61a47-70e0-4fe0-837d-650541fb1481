 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Paiments incomplets : {{ $current_niveau->nom }} {{ $current_annee->nom }}
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Etudiants
             </h3>

         </div>
         <div class="block-content block-content-full">

             <div class="table-responsive">
                 <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
                 <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                     <thead>
                         <tr>
                             <th class="text-center" style="width: 50px;">#</th>
                             <th class="text-center">Nom</th>
                             <th class="text-center">Prénom</th>
                             <th class="text-center">Parcours</th>
                             <th class="text-center">Nom Paiment</th>
                             <th class="text-center">Payé</th>
                             <th class="text-center">Reste</th>
                         </tr>
                     </thead>
                     <tbody>

                         @forelse ($debtPay as $etu)
                             <tr>
                                 <td class="text-center">{{ $loop->iteration }}</td>
                                 <td class="fw-semibold text-center">
                                     {{ $etu["user_nom"] }}
                                 </td>
                                 <td class="fw-semibold text-center">
                                     {{ $etu["user_prenom"] }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu["user_parcours"] }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu["type_nom"] }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu["montant"] }}
                                 </td>
                                 <td class="fw-semibold text-muted text-center">
                                     {{ $etu["manque"] }}
                                 </td>
                                 
                                 {{--                              
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->niveau->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->annee->nom }}
                             </td> --}}

                             </tr>
                        @empty
                         <tr>
                             <td colspan="7">
                                 <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                     role="alert">
                                     <div class="flex-grow-1 me-3">
                                         <p class="mb-0 text-center">
                                             Pas de paiments incomplets
                                         </p>
                                     </div>
                                     <div class="flex-shrink-0">
                                         <i class="fa fa-fw fa-exclamation-circle"></i>
                                     </div>

                                 </div>
                             </td>
                         </tr>
                         @endforelse



                     </tbody>
                 </table>


             </div>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
