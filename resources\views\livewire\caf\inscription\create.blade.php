 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Inscription
         </h1>

     </div>
 </div>
 <!-- END Hero -->
<div class="content">
    <!-- Basic -->

    <form method="POST" role="form" wire:submit.prevent="addUser()" enctype="multipart/form-data">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">Formulaire d'ajout</h3>
            </div>
            <div class="block-content">
                <div class="row g-4">
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Nom <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.nom"
                            class="form-control @error('newUser.nom') is-invalid @enderror" id="example-text-input"
                            name="firstname" placeholder="Entrer du texte">

                        @error('newUser.nom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Prénom <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.prenom"
                            class="form-control @error('newUser.prenom') is-invalid @enderror" id="example-text-input"
                            name="lastname" placeholder="Entrer du texte">

                        @error('newUser.prenom')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-select1">Niveau<span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('newUser.niveau_id') is-invalid @enderror"
                            wire:model="newUser.niveau_id" id="example-select1" name="example-select1">
                            <option selected value="null">Ouvrez le menu déroulant</option>
                            @foreach ($niveaux as $niveau)
                                <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                            @endforeach
                        </select>

                        @error('newUser.niveau_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-6">
                        <label class="form-label" for="example-select1">Année universitaire<span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('newUser.annee') is-invalid @enderror"
                            wire:model="newUser.annee" id="example-select1" name="example-select1">
                            <option selected value="null">Ouvrez le menu déroulant</option>
                            @foreach ($annees as $annee)
                                <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                            @endforeach
                        </select>

                        @error('newUser.annee')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <h2 class="content-heading border-bottom mb-1">Frais de scolarité</h2>

                    <div class="col-6 mb-4">
                        <label class="form-label" for="example-select1">Moyen de payment<span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('newUser.moyen') is-invalid @enderror"
                            wire:model="newUser.moyen" id="example-select1" name="example-select1">
                            <option selected value="null">Ouvrez le menu déroulant</option>
                            @foreach ($moyens as $moyen)
                                <option value="{{ $moyen->id }}">{{ $moyen->nom }}</option>
                            @endforeach
                        </select>

                        @error('newUser.moyen')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-2">
                            <label class="form-label" for="example-text-input">Droit d'inscription<span
                                    class="text-danger">*</span></label>
                        </div>
                        <div class="col-md-5">
                            <input type="text" wire:model="newUser.droitcode"
                                class="form-control @error('newUser.droitcode') is-invalid @enderror"
                                id="example-text-input" name="example-text-input" placeholder="Entrez le code du reçu">

                            @error('newUser.droitcode')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-5">
                            <input type="number" wire:model="newUser.droit"
                                class="form-control @error('newUser.droit') is-invalid @enderror"
                                id="example-text-input" name="address" placeholder="Entrez le montant">

                            @error('newUser.droit')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                    </div>

                    <div class="row mb-3">
                        <div class="col-md-2">
                            <label class="form-label" for="example-text-input">Fiche<span
                                    class="text-danger">*</span></label>
                        </div>
                        <div class="col-md-5">
                            <input type="text" wire:model="newUser.fichecode"
                                class="form-control @error('newUser.fichecode') is-invalid @enderror"
                                id="example-text-input" name="example-text-input"
                                placeholder="Entrez le code du reçu">

                            @error('newUser.fichecode')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-5">
                            <input type="number" wire:model="newUser.fiche"
                                class="form-control @error('newUser.fiche') is-invalid @enderror"
                                id="example-text-input" name="address" placeholder="Entrez le montant">

                            @error('newUser.fiche')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                    </div>

                    {{-- <div class="row mb-3">
                        <div class="col-md-2">
                            <label class="form-label" for="example-text-input">Droit d'examen</label>
                        </div>
                        <div class="col-md-5">
                            <input type="text" wire:model="newUser.examcode"
                                class="form-control @error('newUser.examcode') is-invalid @enderror"
                                id="example-text-input" name="example-text-input"
                                placeholder="Entrez le code du reçu">

                            @error('newUser.examcode')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-5">
                            <input type="number" wire:model="newUser.exam"
                                class="form-control @error('newUser.exam') is-invalid @enderror"
                                id="example-text-input" name="address" placeholder="Entrez le montant">

                            @error('newUser.exam')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-md-2">
                            <label class="form-label" for="example-text-input">Polo</label>
                        </div>
                        <div class="col-md-5">
                            <input type="text" wire:model="newUser.polocode"
                                class="form-control @error('newUser.polocode') is-invalid @enderror"
                                id="example-text-input" name="example-text-input"
                                placeholder="Entrez le code du reçu">

                            @error('newUser.polocode')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-5">
                            <input type="number" wire:model="newUser.polo"
                                class="form-control @error('newUser.polo') is-invalid @enderror"
                                id="example-text-input" name="address" placeholder="Entrez le montant">

                            @error('newUser.polo')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                    </div> --}}
                    
                    
                    {{-- <div class="col-6">
                        <label class="form-label" for="example-text-input">Pays <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.pays"
                            class="form-control @error('newUser.pays') is-invalid @enderror" id="example-text-input"
                            name="example-text-input" placeholder="Text Input">

                        @error('newUser.pays')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-email-input">Email <span
                                class="text-danger">*</span></label>
                        <input type="email" wire:model="newUser.email"
                            class="form-control @error('newUser.email') is-invalid @enderror" id="example-email-input"
                            name="example-email-input" placeholder="Email Input">

                        @error('newUser.email')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone1 <span
                                class="text-danger">*</span></label>
                        <input type="text" wire:model="newUser.telephone1"
                            class="form-control @error('newUser.telephone1') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('newUser.telephone1')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">Telephone2</label>
                        <input type="text" wire:model="newUser.telephone2" class="form-control"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="example-text-input">numeroPieceIdentite</label>
                        <input type="text" wire:model="newUser.cin"
                            class="form-control @error('newUser.cin') is-invalid @enderror"
                            id="example-text-input" name="example-text-input" placeholder="Text Input">

                        @error('newUser.cin')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <h2 class="content-heading border-bottom mb-4 pb-2">Divers informations</h2>

                    <div class="col-12">
                        <label class="form-label" for="example-select">Rôle</label>
                        <select class="form-select @error('newUser.role') is-invalid @enderror"
                            wire:model="newUser.role" id="example-select" name="example-select">
                            <option selected value="0">Open this select menu</option>
                            @foreach ($roles as $role)
                                <option value="{{ $role->id }}">{{ $role->nom }}</option>
                            @endforeach
                        </select>

                        @error('newUser.role')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    @if ($isEtu)
                        <div class="col-6">
                            <label class="form-label" for="example-select1">Parcours(pour les étudiants)</label>
                            <select class="form-select @error('newUser.parcour_id') is-invalid @enderror"
                                wire:model="newUser.parcour_id" id="example-select1" name="example-select1">
                                <option selected value="null">Open this select menu</option>
                                @foreach ($parcours as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->nom }}</option>
                                @endforeach
                            </select>

                            @error('newUser.parcour_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="col-6">
                            <label class="form-label" for="example-select1">Niveau(pour les étudiants)</label>
                            <select class="form-select @error('newUser.niveau_id') is-invalid @enderror"
                                wire:model="newUser.niveau_id" id="example-select1" name="example-select1">
                                <option selected value="null">Open this select menu</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>

                            @error('newUser.niveau_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    @endif
 --}}


                    <div>
                        <button type="submit" class="btn btn-primary mb-3">Enregistrer</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
