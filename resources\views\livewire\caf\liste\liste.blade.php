 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Etudiants
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Etudiants
             </h3>
         </div>
         <div class="block-content block-content-full">
             <div class="row">
                 <div class="col-sm-12 col-md-6">
                     <div class="d-flex">
                         <div>
                             <label>
                                 <select wire:model="filtreParcours" class="form-select form-select-sm">
                                     <option selected value="">Filtre Parcours</option>
                                     @foreach ($parcours as $parcour)
                                         <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                     <option selected value="">Filtre Niveau</option>
                                     @foreach ($niveaux as $niveau)
                                         <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                     @endforeach
                                 </select>
                             </label>
                         </div>

                     </div>

                 </div>
                 <div class="col-sm-12 col-md-6 text-end">

                     <label>
                         <input type="search" wire:model="query" class="form-control form-control-sm"
                             placeholder="Search..">
                     </label>

                 </div>
             </div>
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                 <thead>
                     <tr>
                         <th>Nom et Prénom</th>
                         <th class="d-none d-sm-table-cell">Niveau</th>
                         <th class="d-none d-sm-table-cell">Parcours</th>
                     </tr>
                 </thead>
                 <tbody>

                     @foreach ($etus as $etu)
                         <tr>
                             <td class="fw-semibold">
                                 {{ $etu->user->nom }} {{ $etu->user->prenom }}
                             </td>
                             {{-- <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->parcours->sigle }}
                             </td> --}}
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->niveau->nom }}
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->parcours->nom }}
                             </td>
                             {{-- <td class="text-muted d-none d-sm-table-cell">
                                 {{ $etu->annee->nom }}
                             </td> --}}
                             {{-- <td class="text-center">
                                 <div class="btn-group mb-2">
                                     <a href="{{ route('caf.caisse.payment.etudiant', ['userId' => $etu->user->id, 'niveauId' => $etu->niveau->id, 'anneeId' => $etu->annee->id]) }}"
                                         class="btn btn-sm btn-alt-secondary" target="_blank">
                                         <i class="si si-wallet"></i> Payment
                                     </a>
                                 </div>

                             </td> --}}
                         </tr>
                     @endforeach



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     {{ $etus->links() }}
                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
