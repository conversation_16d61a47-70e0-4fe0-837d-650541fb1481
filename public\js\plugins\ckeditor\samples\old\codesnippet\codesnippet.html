<!DOCTYPE html>
<!--
Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>Code Snippet &mdash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link href="../../../samples/old/sample.css" rel="stylesheet">
	<link href="../../../plugins/codesnippet/lib/highlight/styles/monokai_sublime.css" rel="stylesheet">
	<meta name="ckeditor-sample-name" content="Code Snippet plugin">
	<meta name="ckeditor-sample-group" content="Plugins">
	<meta name="ckeditor-sample-description" content="View and modify code using the Code Snippet plugin.">
	<meta name="ckeditor-sample-isnew" content="1">
	<meta name="description" content="Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.">
	<style>

		#editable
		{
			padding: 10px 20px;
		}

	</style>
</head>
<body>
	<h1 class="samples">
		<a href="../../../samples/old/index.html">CKEditor Samples</a> &raquo; Code Snippet Plugin
	</h1>
	<div class="warning deprecated">
		This sample is not maintained anymore. Check out its <a href="https://ckeditor.com/docs/ckeditor4/latest/examples/codesnippet.html">brand new version in CKEditor Examples</a>.
	</div>

	<div class="description">
		<p>
			This editor is using the <strong><a href="https://ckeditor.com/cke4/addon/codesnippet">Code Snippet</a></strong> plugin which introduces beautiful code snippets.
			By default the <code>codesnippet</code> plugin depends on the built-in client-side syntax highlighting
			library <a href="https://highlightjs.org">highlight.js</a>.
		</p>
		<p>
			You can adjust the appearance of code snippets using the <code><a href="https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_config.html#cfg-codeSnippet_theme">codeSnippet_theme</a></code> configuration variable
			(see <a href="https://highlightjs.org/static/demo/">available themes</a>).
		</p>
		<p>
			Select theme: <select id="select"></select>
		</p>
		<p>
			The CKEditor instance below was created by using the following configuration settings:
		</p>

<pre class="samples" id="sampleConfig">
CKEDITOR.replace( 'editor1', {
	<strong>extraPlugins: 'codesnippet',</strong>
	codeSnippet_theme: 'monokai_sublime'
} );
</pre>

		<p id="ie8-warning">
			Please note that this plugin is not compatible with Internet Explorer 8.
		</p>
	</div>

	<textarea id="editor1">
&lt;p&gt;JavaScript code:&lt;/p&gt;

&lt;pre&gt;
&lt;code class="language-javascript"&gt;function isEmpty( object ) {
	for ( var i in object ) {
		if ( object.hasOwnProperty( i ) )
			return false;
	}
	return true;
}&lt;/code&gt;&lt;/pre&gt;

&lt;p&gt;SQL query:&lt;/p&gt;

&lt;pre&gt;
&lt;code class="language-sql"&gt;SELECT cust.id FROM cust LEFT JOIN loc ON ( cust.loc_id = loc.id ) WHERE cust.type IN ( 1, 2 );&lt;/code&gt;&lt;/pre&gt;

&lt;p&gt;Unknown markup:&lt;/p&gt;

&lt;pre&gt;
&lt;code&gt; ________________
/                \
| How about moo? |  ^__^
\________________/  (oo)\_______
                  \ (__)\       )\/\
                        ||----w |
                        ||     ||
&lt;/code&gt;&lt;/pre&gt;
	</textarea>

	<h2>Inline editor</h2>

	<div class="description">
		<p>
			The following sample shows the <strong>Code Snippet</strong> plugin running inside
			an inline CKEditor instance. The CKEditor instance below was created by using the following configuration settings:
		</p>

<pre class="samples">
CKEDITOR.inline( 'editable', {
	<strong>extraPlugins: 'codesnippet'</strong>
} );
</pre>

		<p>
			<strong>Note</strong>: The <a href="https://highlightjs.org/static/demo/">highlight.js themes</a>
			must be loaded manually to be applied inside an inline editor instance, as the
			<code>codeSnippet_theme</code> setting will not work in that case.
			You need to include the stylesheet in the <code>&lt;head&gt;</code> section of the page, for example:
		</p>

<pre class="samples">
&lt;head&gt;
	...
	&lt;link href="path/to/highlight.js/styles/monokai_sublime.css" rel="stylesheet"&gt;
&lt;/head&gt;
</pre>

	</div>

	<div id="editable" contenteditable="true">

<p>JavaScript code:</p>

<pre><code class="language-javascript">function isEmpty( object ) {
	for ( var i in object ) {
		if ( object.hasOwnProperty( i ) )
			return false;
	}
	return true;
}</code></pre>

<p>SQL query:</p>

<pre><code class="language-sql">SELECT cust.id, cust.name, loc.city FROM cust LEFT JOIN loc ON ( cust.loc_id = loc.id ) WHERE cust.type IN ( 1, 2 );</code></pre>

<p>Unknown markup:</p>

<pre><code> ________________
/                \
| How about moo? |  ^__^
\________________/  (oo)\_______
                  \ (__)\       )\/\
                        ||----w |
                        ||     ||
</code></pre>
	</div>

	<h2>Server-side Highlighting and Custom Highlighting Engines</h2>

	<p>
		The <a href="https://ckeditor.com/cke4/addon/codesnippetgeshi"><strong>Code Snippet GeSHi</strong></a> plugin is an
		extension of the <strong>Code Snippet</strong> plugin which uses a server-side highligter.
	</p>

	<p>
		It also is possible to replace the default highlighter with any library using
		the <a href="https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_plugins_codesnippet_highlighter.html">Highlighter API</a>
		and the <a href="https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_plugins_codesnippet.html#method-setHighlighter"><code>editor.plugins.codesnippet.setHighlighter()</code></a> method.
	</p>

	<script>
		( function() {
			CKEDITOR.disableAutoInline = true;

			var config = {
				extraPlugins: 'codesnippet',
				toolbar: [
					[ 'Source' ], [ 'Undo', 'Redo' ], [ 'Bold', 'Italic', 'Underline' ], [ 'CodeSnippet' ]
				],
				codeSnippet_theme: 'monokai_sublime',
				height: 400
			};

			CKEDITOR.replace( 'editor1', config );

			CKEDITOR.inline( 'editable', CKEDITOR.tools.extend( {}, config, {
				extraPlugins: 'codesnippet'
			}, true ) );

			initThemeChange();

			function initThemeChange() {
				var templates = [
						'monokai_sublime', 'default', 'arta', 'ascetic', 'atelier-dune.dark', 'atelier-dune.light', 'atelier-forest.dark', 'atelier-forest.light', 'atelier-heath.dark', 'atelier-heath.light', 'atelier-lakeside.dark', 'atelier-lakeside.light', 'atelier-seaside.dark', 'atelier-seaside.light', 'brown_paper', 'dark', 'docco', 'far', 'foundation', 'github', 'googlecode', 'idea', 'ir_black', 'magula', 'mono-blue', 'monokai', 'obsidian', 'paraiso.dark', 'paraiso.light', 'pojoaque', 'railscasts', 'rainbow', 'school_book', 'solarized_dark', 'solarized_light', 'sunburst', 'tomorrow-night-blue', 'tomorrow-night-bright', 'tomorrow-night-eighties', 'tomorrow-night', 'tomorrow', 'vs', 'xcode', 'zenburn'
					],
					pre = CKEDITOR.document.getById( 'sampleConfig' ),
					select = CKEDITOR.document.getById( 'select' ),
					codeTemplate = 'CKEDITOR.replace( \'editor1\', {\n' +
						'	<strong>extraPlugins: \'codesnippet\',</strong>\n' +
						'	codeSnippet_theme: \'{tpl}\'\n' +
					'} );',
					name, option;

				while ( ( name = templates.shift() ) ) {
					option = CKEDITOR.document.createElement( 'option', {
						attributes: {
							value: name
						}
					} );

					option.setText( name );
					select.append( option );
				}

				select.$.onchange = function() {
					if ( CKEDITOR.instances.editor1 )
						CKEDITOR.instances.editor1.destroy();

					CKEDITOR.replace( 'editor1', CKEDITOR.tools.extend( {}, config, {
						codeSnippet_theme: this.value
					}, true ) );

					pre.setHtml( codeTemplate.replace( '{tpl}', this.value ) );
				};
			}

			if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 )
				CKEDITOR.document.getById( 'ie8-warning' ).addClass( 'warning' );
		}() );
	</script>

	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="https://ckeditor.com/">https://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2022, <a class="samples" href="https://cksource.com/">CKSource</a> Holding sp. z o.o. All rights reserved.
		</p>
	</div>
</body>
</html>
